import 'dart:developer';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path/path.dart' as p;
import 'package:intl/intl.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:newarc_platform/utils/inputFormatters.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/utils/storage.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/classes/baseAddressInfo.dart';
import 'package:newarc_platform/classes/basePersonInfo.dart';
import 'package:newarc_platform/pages/work/gestione/agencies/agencies_controller.dart';
import 'package:newarc_platform/widget/UI/address_completion_google.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/select-box.dart';
import 'package:newarc_platform/widget/UI/image-picker.dart';
import 'package:newarc_platform/app_const.dart' as appConst;
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:image/image.dart' as img;
import 'package:path/path.dart' as path;

import '../../../../utils/resizeXFile.dart';
import '../../../../widget/UI/file-picker.dart';

class AgenciesInsideView extends StatefulWidget {
  final Function updateViewCallback;
  final Agency agency;
  const AgenciesInsideView({super.key, required this.updateViewCallback, required this.agency});

  @override
  State<AgenciesInsideView> createState() => _AgenciesInsideViewState();
}

class _AgenciesInsideViewState extends State<AgenciesInsideView> {
  final controller = Get.put<AgenciesController>(AgenciesController());
  AgencyUser? agencyUser;
  final profilePicture = [];
  String? profilePictureFilename;
  final _formKey = GlobalKey<FormState>();

  Future<bool> updateAgencyActiveStatus(Agency agency) async {
    final FirebaseFirestore _db = FirebaseFirestore.instance;
    return await _db
        .collection(appConfig.COLLECT_AGENCIES)
        .doc(agency.id)
        .update(agency.toMap())
        .then((value) {
      return true;
    }).onError((error, stackTrace) {
      // print({error, stackTrace});
      return false;
    });
  }

  Future<void> getAgencyUser(Agency agency) async {
    try{
      final FirebaseFirestore _db = FirebaseFirestore.instance;
      QuerySnapshot<Map<String, dynamic>> querySnapshot = await _db
                  .collection(appConfig.COLLECT_USERS)
                  .where('agencyId', isEqualTo: agency.id)
                  .get();
      setState(() {
        agencyUser = AgencyUser.fromDocument(
          querySnapshot.docs.first.data(), querySnapshot.docs.first.id
        );
        profilePictureFilename = agencyUser!.profilePicture;
      });
    } catch (e, s) {
      log('Error in fetching agencyUser', error: e, stackTrace: s);
    }
  }

  Future<bool> updateData(BuildContext context) async {
    setState(() {
      controller.validationMessage = "";
    });
    widget.agency.name = controller.agencyName.text;
    widget.agency.address = controller.agencyOperativeAddress.fullAddress;
    widget.agency.streetNumber = controller.agencyOperativeAddress.streetNumber;
    widget.agency.streetName = controller.agencyOperativeAddress.streetName;
    widget.agency.locality = controller.agencyOperativeAddress.locality;
    widget.agency.city = controller.agencyOperativeAddress.city;
    widget.agency.province = controller.agencyOperativeAddress.province;
    widget.agency.region = controller.agencyOperativeAddress.region;
    widget.agency.country = controller.agencyOperativeAddress.country;
    widget.agency.postalCode = controller.agencyOperativeAddress.postalCode;
    widget.agency.latitude = controller.agencyOperativeAddress.latitude;
    widget.agency.longitude = controller.agencyOperativeAddress.longitude;
    widget.agency.renovationFee = controller.renovationFeeController.text;
    widget.agency.documentPath = [];
    for (final doc in widget.agency.documents!) {
      widget.agency.documentPath?.add({
        "filename": doc,
        "location": "/agencies/${widget.agency.id}/document/",
      });
    }

    widget.agency.phone = controller.agencyPhone.text;
    widget.agency.email = controller.agencyEmail.text;
    widget.agency.referencePerson = BasePersonInfo.fromMap(
        {
          'name': controller.contactName.text, 
          'surname': controller.contactSurname.text, 
          'phone': controller.contactPhone.text, 
          'email': controller.contactEmail.text
        }
    );
    widget.agency.legalEntity = controller.agencyLegalEntity.text;
    widget.agency.formationType = controller.agencyFormationType.text;
    widget.agency.vat = controller.agencyVat.text;
    widget.agency.sedeLegaleFull = controller.agencyLegalAddress;
    widget.agency.sedeLegale = controller.agencyLegalAddress.fullAddress;
    widget.agency.sdi = controller.agencyBillingCode.text;
    widget.agency.iban = controller.agencyIban.text;
    widget.agency.fiscalCode = controller.agencyFiscalCode.text;


    if (profilePicture.length > 0) {
      String __profilePictureFilename =
          'agency-profile' + p.extension(profilePicture[0].name);

      await uploadProfilePicture('${appConfig.COLLECT_AGENCIES}/', widget.agency.id!, __profilePictureFilename, profilePicture[0]);

      final resizedFile = await resizeXFile(profilePicture[0],width: 240,height: 240,quality: 60,customFileName: "agency-profile_thumbnail");
      await uploadProfilePicture('${appConfig.COLLECT_AGENCIES}/',widget.agency.id!, resizedFile.name, resizedFile);
      agencyUser!.profilePicture = __profilePictureFilename;
      profilePictureFilename = __profilePictureFilename;
      profilePicture.clear();
    } else {
      profilePictureFilename = agencyUser!.profilePicture;
    }
    
    final FirebaseFirestore _db = FirebaseFirestore.instance;
    await _db
        .collection(appConfig.COLLECT_AGENCIES)
        .doc(widget.agency.id)
        .update(widget.agency.toMap());
    return true;
  }

  StreamBuilder _immaginaSubscription(width) {
    return StreamBuilder<DocumentSnapshot<Map<String, dynamic>>>(
      stream: FirebaseFirestore.instance
          .collection(appConfig.COLLECT_AGENCIES)
          .doc(widget.agency.id)
          .snapshots(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return SizedBox();
        }

        if (snapshot.hasError) {
          return SizedBox();
        }

        if (!snapshot.hasData || !snapshot.data!.exists) {
          return SizedBox();
        }

        final agencyData = snapshot.data!.data();
        final subscriptionServiceCountLeft =
            agencyData?['subscriptionServiceCountLeft'] ?? 0;
        final subscriptionServiceCount =
            agencyData?['subscriptionServiceCount'] ?? 0;
        DateTime expiryDate = DateTime.fromMillisecondsSinceEpoch(
            agencyData?['subscriptionEndDate'] ?? 0);
        DateTime now = DateTime.now();
        bool isExpired = now.isAfter(expiryDate);
        bool isServiceOver = subscriptionServiceCountLeft <= 0;

        return Container(
          padding: EdgeInsets.all(18),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            color: Color(0xffF7F7F7),
          ),
          width: width,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (isExpired) ...[
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    NarFormLabelWidget(
                      label: 'Nessun abbonamento attivo',
                      fontSize: 14,
                      fontWeight: '600',
                      textColor: Colors.black,
                    ),
                  ],
                ),
                SizedBox(
                  height: 30,
                ),
              ] else ...[
                Expanded(
                  flex: 2,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      NarFormLabelWidget(
                        label: 'Abbonamento acquistato',
                        fontSize: 13,
                        fontWeight: '600',
                        textColor: Color(0xff909090),
                      ),
                      SizedBox(
                        height: 6,
                      ),
                      FutureBuilder<
                          DocumentSnapshot<Map<String, dynamic>>>(
                        future: FirebaseFirestore.instance
                            .collection(appConfig.COLLECT_IMMAGINA_SUBSCRIPTION)
                            .doc(agencyData?['subscriptionId'])
                            .get(),
                        builder: (context, futureSnapshot) {
                          if (futureSnapshot.connectionState ==
                              ConnectionState.waiting) {
                            return SizedBox();
                          }
    
                          if (futureSnapshot.hasError ||
                              !futureSnapshot.hasData ||
                              !futureSnapshot.data!.exists) {
                            return SizedBox();
                          }
    
                          final subscriptionData = futureSnapshot.data!.data();
                          String planName = subscriptionData?['planType'] ?? 'Unknown Plan';
                          bool isSuccessFee = subscriptionData?['successFee'] != '0';
                          String planType = isSuccessFee ? ' + Success Fee' : '';
    
                          return NarFormLabelWidget(
                            label: planName + planType,
                            fontSize: 15,
                            fontWeight: '700',
                            textColor: Colors.black,
                          );
                        },
                      ),
                      SizedBox(
                        height: 3,
                      ),
                      NarFormLabelWidget(
                        label:
                            'Valido fino al: ${DateFormat('dd/MM/yyyy').format(expiryDate)}',
                        fontSize: 12,
                        fontWeight: '500',
                        textColor: Colors.black,
                      ),
                    ],
                  ),
                ),
                SizedBox(
                  height: 60,
                  child: Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8.0),
                    child: VerticalDivider(
                      color: Color(0xff909090),
                    ),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      NarFormLabelWidget(
                        label: 'Crediti disponibili',
                        fontSize: 13,
                        fontWeight: '600',
                        textColor: isServiceOver
                            ? Color(0xffE82525)
                            : Color(0xff909090),
                      ),
                      SizedBox(
                        height: 6,
                      ),
                      NarFormLabelWidget(
                        label:
                            '${subscriptionServiceCountLeft}/${subscriptionServiceCount}',
                        fontSize: 15,
                        fontWeight: '600',
                        textColor: isServiceOver
                            ? Color(0xffE82525)
                            : Colors.black,
                      ),
                    ],
                  ),
                ),
                SizedBox(
                  height: 15,
                ),
              ],
            ],
          )
        );
      },
    );
  }

  @override
  void initState() {
    profilePicture.clear();
    log("widget.agency ===> ${widget.agency.id}");
    getAgencyUser(widget.agency);
    controller.initInsideViewController(widget.agency);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var containerWidth = MediaQuery.of(context).size.width * .75;
    return SingleChildScrollView(
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      hoverColor: Colors.transparent,
                      focusColor: Colors.transparent,
                      onPressed: () {
                        controller.clearInsideViewController();
                        widget.updateViewCallback('agencies');
                      },
                      icon: SvgPicture.asset('assets/icons/arrow_left.svg',
                          height: 20, color: Colors.black),
                    ),
                    SizedBox(
                      width: 10,
                    ),
                    NarFormLabelWidget(
                      label:
                          widget.agency.name!,
                      fontSize: 20,
                      fontWeight: 'bold',
                    ),
                  ],
                ),
                Row(
                  children: [
                    NarFormLabelWidget(
                      label: 'Attiva/Disattiva',
                      fontSize: 15,
                      fontWeight: '500',
                    ),
                    Switch(
                      // This bool value toggles the switch.
                      value: widget.agency.isActive!,
                      activeColor: Theme.of(context).primaryColor,
                      onChanged: (bool value) async {
                        // This is called when the user toggles the switch.
                        setState(() {
                          widget.agency.isActive = value;
                        });
                        await updateAgencyActiveStatus(widget.agency);
                      },
                    ),
                  ],
                )
              ],
            ),
            SizedBox(height: 35),
            NarFormLabelWidget(
              label: 'Dati Agenzia',
              fontSize: 18,
              fontWeight: 'bold',
            ),
            SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomTextFormField(
                  label: 'Nome Agenzia',
                  controller: controller.agencyName,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Obbligatorio';
                    }
                    return null;
                  },
                ),
              ],
            ),
            SizedBox(
              height: 15,
            ),
            AddressSearchBar(
              label: "Indirizzo sede operativa", 
              initialAddress: widget.agency.fullAddress ?? widget.agency.address ?? "",
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Obbligatorio';
                }
                return null;
              },
              onPlaceSelected: (selectedPlace){
                log('Selected place: \n$selectedPlace');
                BaseAddressInfo selectedAddress = BaseAddressInfo.fromMap(selectedPlace['place']);
                if (selectedAddress.isValidAddress()){
                  controller.agencyOperativeAddress = selectedAddress;
                } else {
                  controller.agencyOperativeAddress = BaseAddressInfo.empty();
                }
              }
            ),
            SizedBox(
              height: 15,
            ),
            Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                CustomTextFormField(
                  label: 'Telefono',
                  controller: controller.agencyPhone,
                  inputFormatters: [phoneNumberMaskFormatterIt],
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Obbligatorio';
                    }
                    return null;
                  },
                ),
                SizedBox(
                  width: 15,
                ),
                CustomTextFormField(
                  label: 'E-mail',
                  hintText: controller.agencyEmail.text,
                  readOnly: true,
                  enabled: false,
                ),
              ],
            ),
            SizedBox(
              height: 15,
            ),
            Container(
              width: containerWidth * .80,
              height: 1,
              decoration: BoxDecoration(
                color: Color(0xFFDCDCDC),
              ),
              child: SizedBox(height: 0),
            ),
            SizedBox(
              height: 15,
            ),
            NarFormLabelWidget(
              label: 'Logo Agenzia',
              fontSize: 18,
              fontWeight: 'bold',
            ),
            SizedBox(
              height: 20,
            ),
            Row(
              mainAxisSize: MainAxisSize.max,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                NarImagePickerWidget(
                  allowMultiple: false,
                  imagesToDisplayInList: 0,
                  removeButton: false,
                  removeButtonText: 'rimuovi',
                  uploadButtonPosition: 'back',
                  showMoreButtonText: '+ espandi',
                  removeButtonPosition: 'bottom',
                  displayFormat: 'row',
                  imageDimension: 100,
                  imageBorderRadius: 50,
                  borderRadius: 7,
                  fontSize: 14,
                  fontWeight: '600',
                  text: 'Carica logo',
                  borderSideColor: Theme.of(context).primaryColor,
                  hoverColor: Color.fromRGBO(133, 133, 133, 1),
                  images: profilePicture,
                  pageContext: context,
                  storageDirectory: '${appConfig.COLLECT_AGENCIES}/',
                  preloadedImages: profilePictureFilename == null ||
                          profilePictureFilename == ''
                      ? []
                      : [profilePictureFilename],
                  firebaseId: widget.agency.id,
                  removeExistingOnChange: true
                )
              ],
            ),
            SizedBox(
              height: 15,
            ),
            Container(
              width: containerWidth * .80,
              height: 1,
              decoration: BoxDecoration(
                color: Color(0xFFDCDCDC),
              ),
              child: SizedBox(height: 0),
            ),
            SizedBox(
              height: 15,
            ),
            NarFormLabelWidget(
              label: 'Persona di riferimento',
              fontSize: 18,
              fontWeight: 'bold',
            ),
            SizedBox(
              height: 20,
            ),
            Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                CustomTextFormField(
                  label: 'Nome',
                  controller: controller.contactName,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Obbligatorio';
                    }
                    return null;
                  },
                ),
                SizedBox(
                  width: 15,
                ),
                CustomTextFormField(
                  label: 'Cognome', 
                  controller: controller.contactSurname,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Obbligatorio';
                    }
                    return null;
                  },
                ),
              ],
            ),
            SizedBox(
              height: 15,
            ),
            Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                CustomTextFormField(
                  label: 'Telefono',
                  controller: controller.contactPhone,
                  inputFormatters: [phoneNumberMaskFormatterIt],
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Obbligatorio';
                    }
                    return null;
                  },
                ),
                SizedBox(
                  width: 15,
                ),
                CustomTextFormField(
                  label: 'E-mail',
                  controller: controller.contactEmail,
                  validator: (value) {
                    if (value == '') {
                      return 'Obbligatorio';
                    }
                    final emailRegexp = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
                    if (!emailRegexp.hasMatch(value)) {
                      return 'Inserisci un indirizzo email valido';
                    }
                    return null;
                  },
                ),
              ],
            ),
            SizedBox(
              height: 15,
            ),
            Container(
              width: containerWidth * .80,
              height: 1,
              decoration: BoxDecoration(
                color: Color(0xFFDCDCDC),
              ),
              child: SizedBox(height: 0),
            ),
            SizedBox(
              height: 15,
            ),
            NarFormLabelWidget(
              label: 'Fatturazione e pagamenti',
              fontSize: 18,
              fontWeight: 'bold',
            ),
            SizedBox(
              height: 20,
            ),
            Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                CustomTextFormField(
                  label: 'Denominazione',
                  controller: controller.agencyLegalEntity,
                  textCapitalization: TextCapitalization.words,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Obbligatorio';
                    }
                    return null;
                  },
                ),
                SizedBox(
                  width: 15,
                ),
                Expanded(
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      NarFormLabelWidget(
                        label: "Forma Societaria",
                        textColor: Color(0xff696969),
                        fontSize: 13,
                        fontWeight: '600',
                      ),
                      SizedBox(height: 4),
                      NarSelectBoxWidget(
                        options: appConst.supplierFormationTypesList,
                        controller: controller.agencyFormationType,
                        validationType: 'required',
                        parametersValidate: 'Required!',
                        onChanged: (value){
                          setState((){});
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(
              height: 15,
            ),
            Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                CustomTextFormField(
                  label: 'IBAN',
                  controller: controller.agencyIban,
                  textCapitalization: TextCapitalization.words,
                  inputFormatters: [ibanMaskFormatter],
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Obbligatorio';
                    }
                    return null;
                  },
                ),
                SizedBox(
                  width: 15,
                ),
                CustomTextFormField(
                  label: 'Codice Fatturazione Elettronico',
                  controller: controller.agencyBillingCode,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Obbligatorio';
                    }
                    return null;
                  },
                ),
              ],
            ),
            SizedBox(
              height: 15,
            ),
            Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                CustomTextFormField(
                  label: 'P.IVA',
                  controller: controller.agencyVat,
                  inputFormatters: [ivaMaskFormatter],
                  validator: (value) {
                    if (value == null || value.isEmpty || !RegExp(r"^(IT)?[0-9]{11}$").hasMatch(value)) {
                      return 'Obbligatorio';
                    }
                    return null;
                  },
                ),
                SizedBox(
                  width: 15,
                ),
                CustomTextFormField(
                  label: 'Codice Fiscale',
                  enabled : controller.agencyFormationType.text == 'Impresa individuale',
                  controller: controller.agencyFiscalCode,
                  inputFormatters: [codiceFiscaleMaskFormatter],
                  validator: (value) {
                    if (controller.agencyFormationType.text != 'Impresa individuale') {
                      return null;
                    }
                    if (value == null || value.isEmpty || value.length < 16) {
                      return 'Obbligatorio';
                    }
                    return null;
                  },
                ),
              ],
            ),
            SizedBox(
              height: 15,
            ),
            AddressSearchBar(
              label: "Indirizzo sede legale", 
              initialAddress: widget.agency.sedeLegaleFull?.fullAddress ?? widget.agency.sedeLegale ?? "",
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Obbligatorio';
                }
                return null;
              },
              onPlaceSelected: (selectedPlace){
                log('Selected place: \n$selectedPlace');
                BaseAddressInfo selectedAddress = BaseAddressInfo.fromMap(selectedPlace['place']);
                if (selectedAddress.isValidAddress()){
                  controller.agencyLegalAddress = selectedAddress;
                } else {
                  controller.agencyLegalAddress = BaseAddressInfo.empty();
                }
              }
            ),
            SizedBox(
              height: 15,
            ),
            // Container(
            //   width: containerWidth * .80,
            //   height: 1,
            //   decoration: BoxDecoration(
            //     color: Color(0xFFDCDCDC),
            //   ),
            //   child: SizedBox(height: 0),
            // ),
            // SizedBox(
            //   height: 15,
            // ),
            // NarFormLabelWidget(
            //   label: 'Gestione servizi',
            //   fontSize: 18,
            //   fontWeight: 'bold',
            // ),
            // SizedBox(
            //   height: 20,
            // ),
            // Row(
            //   mainAxisSize: MainAxisSize.max,
            //   children: [
            //     Switch(
            //       activeColor: Theme.of(context).primaryColor,
            //       value: controller.agencyIsImmaginaActive, 
            //       onChanged: (value){
            //         setState(() {
            //           controller.agencyIsImmaginaActive = value;
            //         });
            //       }
            //     ),
            //     SizedBox(
            //       width: 10,
            //     ),
            //     NarFormLabelWidget(
            //       label: 'Newarc Immagina',
            //       textColor: controller.agencyIsImmaginaActive ? Colors.black : Colors.grey,
            //       fontSize: 15,
            //       fontWeight: '500',
            //     ),
            //   ],
            // ),
            // Row(
            //   mainAxisSize: MainAxisSize.max,
            //   children: [
            //     Switch(
            //       activeColor: Theme.of(context).primaryColor,
            //       value: controller.agencyIsContattiActive, 
            //       onChanged: (value){
            //         setState(() {
            //           controller.agencyIsContattiActive = value;
            //         });
            //       }
            //     ),
            //     SizedBox(
            //       width: 10,
            //     ),
            //     NarFormLabelWidget(
            //       label: 'Gestione Contatti',
            //       textColor: controller.agencyIsContattiActive ? Colors.black : Colors.grey,
            //       fontSize: 15,
            //       fontWeight: '500',
            //     ),
            //   ],
            // ),
            // Row(
            //   mainAxisSize: MainAxisSize.max,
            //   children: [
            //     Switch(
            //       activeColor: Theme.of(context).primaryColor,
            //       value: controller.agencyIsValutatoreActive, 
            //       onChanged: (value){
            //         setState(() {
            //           controller.agencyIsValutatoreActive = value;
            //         });
            //       }
            //     ),
            //     SizedBox(
            //       width: 10,
            //     ),
            //     NarFormLabelWidget(
            //       label: 'Valutatore',
            //       textColor: controller.agencyIsValutatoreActive ? Colors.black : Colors.grey,
            //       fontSize: 15,
            //       fontWeight: '500',
            //     ),
            //   ],
            // ),
            // SizedBox(
            //   height: 15,
            // ),
            Container(
              width: containerWidth * .80,
              height: 1,
              decoration: BoxDecoration(
                color: Color(0xFFDCDCDC),
              ),
              child: SizedBox(height: 0),
            ),
            SizedBox(
              height: 15,
            ),
            NarFormLabelWidget(
              label: 'Newarc Immagina',
              fontSize: 18,
              fontWeight: 'bold',
            ),
            SizedBox(
              height: 20,
            ),
            // Immagina subscription popup
            _immaginaSubscription(containerWidth*.40),
            SizedBox(
              height: 15,
            ),

            Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                Expanded(
                  child: NarSelectBoxWidget(
                    label: "Renovation Fee concordata",
                    options: ["Nessuna","1%","2%","3%","4%","5%","6%","7%","8%","9%","10%"],
                    controller: controller.renovationFeeController,
                  ),
                ),
                SizedBox(
                  width: 15,
                ),
                Expanded(child: SizedBox(height: 0,))
              ],
            ),
            SizedBox(
              height: 15,
            ),
            Container(
              width: containerWidth * .80,
              height: 1,
              decoration: BoxDecoration(
                color: Color(0xFFDCDCDC),
              ),
              child: SizedBox(height: 0),
            ),
            SizedBox(
              height: 15,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                NarFormLabelWidget(
                  label: 'Documenti',
                  fontSize: 18,
                  fontWeight: 'bold',
                ),
                NarFilePickerWidget(
                  allowMultiple: true,
                  displayFormat: 'inline-button',
                  borderRadius: 7,
                  fontSize: 11,
                  fontWeight: '600',
                  text: 'Carica documenti',
                  borderSideColor: Theme.of(context).primaryColor,
                  hoverColor: Color.fromRGBO(133, 133, 133, 1),
                  allFiles: widget.agency.documents,
                  pageContext: context,
                  storageDirectory: 'agencies/${widget.agency.id}/document/',
                  progressMessage: controller.fileProgressMessage,
                  onUploadCompleted: (){
                    setState(() {});
                  },
                )
              ],
            ),
            SizedBox(height: 10),
            NarFilePickerWidget(
              allowMultiple: false,
              filesToDisplayInList: 0,
              removeButton: true,
              isDownloadable: true,
              removeButtonText: 'Elimina',
              uploadButtonPosition: 'back',
              showMoreButtonText: '+ espandi',
              actionButtonPosition: 'bottom',
              displayFormat: 'inline-widget',
              containerWidth: 110,
              containerHeight: 110,
              containerBorderRadius: 13,
              borderRadius: 7,
              fontSize: 11,
              fontWeight: '600',
              text: 'Carica Progetto',
              borderSideColor:
              Theme.of(context).primaryColor,
              hoverColor:
              Color.fromRGBO(133, 133, 133, 1),
              allFiles: widget.agency.documents,
              pageContext: context,
              storageDirectory: 'agencies/${widget.agency.id}/document/',
              removeExistingOnChange: true,
              progressMessage: controller.fileProgressMessage,
              onUploadCompleted: (){
                setState(() {});
              },
            ),
            SizedBox(height: 30),
            NarFormLabelWidget(
              label: controller.validationMessage != '' ? controller.validationMessage : '',
              fontSize: 12,
              textColor: Colors.red,
            ),
            SizedBox(
              height: 50,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              mainAxisSize: MainAxisSize.max,
              children: [
                NarFormLabelWidget(
                  label: controller.progressMessage != '' ? controller.progressMessage : '',
                  fontSize: 12,
                )
              ],
            ),
            SizedBox(height: 10),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                BaseNewarcButton(
                  buttonText: "Salva",
                  onPressed: () async {
                    if (_formKey.currentState!.validate()) {
                      setState(() {
                        controller.progressMessage = 'Salvataggio in corso...';
                      });
                      bool response = await updateData(context);
                      if (response == true) {
                        setState(() {
                          controller.progressMessage = '';
                        });
                        await showAlertDialog(context, "Salvataggio",
                            "Informazioni agenzia salvate con successo");
                      } else {
                        setState(() {
                          controller.progressMessage =
                              'Si è verificato un errore. Contatta l\'assistenza.';
                        });
                      }
                    }
                  }
                )
              ],
            )
            // Row(
            //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
            //   mainAxisSize: MainAxisSize.max,
            //   children: [
            //     BaseNewarcButton(
            //         notAccent: true,
            //         buttonText: "Elimina ditta",
            //         onPressed: () async {
            //           setState(() {
            //             progressMessage = 'Eliminazione in corso';
            //           });
            //           bool unlock = await showAlertDialog(
            //               context,
            //               "Conferma eliminazione",
            //               "Sei sicuro di voler eliminare questa ditta?",
            //               addCancel: true);
            //           if (unlock) {
            //             bool response = await updateData(context, isArchive: true);
            //             if (response == true) {
            //               widget.fetchSupplier!();
            //             } else {
            //               setState(() {
            //                 progressMessage =
            //                     'Si è verificato un errore. Contatta l\'assistenza.';
            //               });
            //             }
            //           }
            //         }),
            //     BaseNewarcButton(
            //         buttonText: "Salva",
            //         onPressed: () async {
            //           setState(() {
            //             progressMessage = 'Salvataggio in corso...';
            //           });
            //           bool response = await updateData(context, isArchive: false);
            //           if (response == true) {
            //             setState(() {
            //               progressMessage = '';
            //               //widget.getProfilePicture!();
            //             });
            //             await showAlertDialog(context, "Salvataggio",
            //                 "Informazioni ditta salvate con successo");
        
            //             widget.updateSelectedSupplier!();
            //           } else {
            //             setState(() {
            //               progressMessage =
            //                   'Si è verificato un errore. Contatta l\'assistenza.';
            //             });
            //           }
            //         })
            //   ],
            // )
          ],
        ),
      ),
    );
  }
}