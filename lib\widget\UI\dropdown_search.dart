import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/renovationContact.dart';

class SearchSelectBox extends StatefulWidget {
  final TextEditingController? controller;
  final List<Map<String, String>>? options;
  final Function(String)? onSelection;
  final bool? enabled;

  const SearchSelectBox({ 
    Key? key, 
    this.controller,
    this.options,
    this.onSelection,
    this.enabled,
    }):super(key:key);

  @override
  State<SearchSelectBox> createState() => _SearchSelectBoxState();
}

class _SearchSelectBoxState extends State<SearchSelectBox> {
  String errorText = '';

  @override
  Widget build(BuildContext context) {
    String selectedClient;
    // double maxLargeness = MediaQuery.of(context).size.width > 700 ? 700 : MediaQuery.of(context).size.width;
    double maxLargeness = MediaQuery.of(context).size.width/3;
    Padding leadingIcon = 
      Padding(
        padding: const EdgeInsets.only(left: 8),
        child: 
          Icon(
            Icons.search,
            color: Theme.of(context).primaryColorLight,
            size: 24,
          )
      );
    // SvgPicture trailingIcon = 
    //   SvgPicture.asset(
    //               'assets/icons/arrow_down.svg',
    //               color: Color(0xff7e7e7e),
    //               width: 13,
    //             );
    IconButton trailingIcon = 
      IconButton(
        splashRadius: 1,
        iconSize: 22,
        onPressed: () {
              setState(() {
                widget.controller!.text = '';
              });
            },
        icon: 
          Icon(
                Icons.clear_rounded,
                color: Theme.of(context).primaryColorLight,
              )
      );
    IconButton selectedTrailingIcon = 
      IconButton(
        splashRadius: 1,
        iconSize: 22,
        onPressed: () {
              setState(() {
                widget.controller!.text = '';
              });
            },
        icon: 
          Icon(
                Icons.clear_rounded,
                color: Theme.of(context).primaryColorLight,
              )
      );
    
    int? searchCallback(List<DropdownMenuEntry<String>> entries, String query) {
    if (query.isEmpty) {
      return null;
    }
    final int index = entries
    .indexWhere((DropdownMenuEntry<String> entry) => entry.label == query);
    return index != -1 ? index : null;
    }
    
    return 
      Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          DropdownMenu<String>(
            enabled: widget.enabled ?? true,
            menuHeight: 200,
            searchCallback: searchCallback,
            width: maxLargeness,
            controller: widget.controller,
            leadingIcon: (widget.enabled ?? true) ? leadingIcon : null,
            trailingIcon: (widget.enabled ?? true) ? trailingIcon : SizedBox.shrink(),
            selectedTrailingIcon: (widget.enabled ?? true) ? selectedTrailingIcon: null,
            requestFocusOnTap: true,
            enableFilter: true, 
            menuStyle: MenuStyle(
              // shadowColor: const WidgetStatePropertyAll(Colors.black),
              // elevation: const WidgetStatePropertyAll(15),
              backgroundColor: const WidgetStatePropertyAll(Colors.white),
              shape: 
                WidgetStatePropertyAll(
                  ContinuousRectangleBorder(
                    borderRadius: BorderRadius.circular(20)
                  )
                ),
            ),
            textStyle: 
              const TextStyle(
                fontFamily: 'Raleway-700',
                fontSize: 15,
                color: Colors.black
              ),
            inputDecorationTheme: 
              InputDecorationTheme(
                filled: true,
                fillColor: Colors.transparent,
                constraints: BoxConstraints.tight(const Size.fromHeight(55)),
                disabledBorder: 
                  OutlineInputBorder(
                    borderRadius: const BorderRadius.all(
                      Radius.circular(8),
                    ),
                    borderSide: BorderSide(
                      color: Color(0xffdbdbdb),
                      width: 1,
                    ),
                  ),
                enabledBorder: 
                   OutlineInputBorder(
                    borderRadius: const BorderRadius.all(
                      Radius.circular(8),
                    ),
                    borderSide: BorderSide(
                      color: Color(0xffdbdbdb),
                      width: 1,
                    ),
                  ),
                focusedBorder: 
                  OutlineInputBorder(
                    borderRadius: const BorderRadius.all(
                      Radius.circular(8),
                    ),
                    borderSide: BorderSide(
                      color: Color(0xffdbdbdb),
                      width: 1,
                    ),
                  ),
                errorBorder: 
                  const OutlineInputBorder(
                    borderSide: BorderSide(color: Color.fromARGB(255, 234, 28, 28)),
                    borderRadius: BorderRadius.all(
                      Radius.circular(8),
                    ),
                  ),
                focusedErrorBorder: 
                  const OutlineInputBorder(
                    borderSide: BorderSide(color: Color.fromARGB(255, 234, 28, 28)),
                    borderRadius: BorderRadius.all(
                      Radius.circular(8),
                    ),
                  ),
                isDense: true,
              ),
            errorText: errorText != '' ? errorText : null,
            onSelected: (String? client) {
              if (client != null && client != '') {
                setState(() {
                  widget.onSelection?.call(client);
                  widget.controller!.text = widget.options!.where((elem) => elem['id']==client).first['name']!;
                  errorText = '';
                });
              }
              else {
                setState(() {
                  errorText = 'Nessun risultato trovato';
                });
              }
              
            },
            dropdownMenuEntries:
                widget.options!.map<DropdownMenuEntry<String>>((Map client) {
              return DropdownMenuEntry<String>(
                  value: client['id'],
                  label: client['name'],
                  style: 
                    MenuItemButton.styleFrom(
                      padding: const EdgeInsets.only(left: 20),
                      textStyle: 
                        const TextStyle(
                          fontFamily: 'Raleway-700',
                          fontSize: 15,
                        ),
                      foregroundColor: Colors.black,
                      backgroundColor: Colors.white,
                    ),
              );
            }).toList(),
          ),
        ],
      );
  }
}