import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/acquiredContact.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/classes/notification.dart';
import 'package:newarc_platform/functions/various.dart';
import 'package:newarc_platform/pages/agency/acquired_contacts_view.dart';
import 'package:newarc_platform/utils/firestore.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/widget/UI/button.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';

class AssegnaImmobilePopup extends StatefulWidget {
  const AssegnaImmobilePopup(
      {Key? key,
      required this.acquiredContact,
      this.agency,
      this.initialFetchContacts})
      : super(key: key);

  final AcquiredContact acquiredContact;
  final Agency? agency;
  final Function? initialFetchContacts;

  @override
  State<AssegnaImmobilePopup> createState() => _AssegnaImmobilePopupState();
}

class _AssegnaImmobilePopupState extends State<AssegnaImmobilePopup> {
  TextStyle sectionTitleStyle =
      TextStyle(fontSize: 16, fontWeight: FontWeight.bold);

  bool loading = false;
  bool hasUpdated = false;
  QuerySnapshot<Map<String, dynamic>>? collectionSnapshot;
  List<Agency>? agencyUser = [];
  List<Agency>? tmpAgencyUser = [];
  TextEditingController contSearchAgency = new TextEditingController();
  Key? listviewKey;
  Agency? selectedAgency;

  @override
  void initState() {
    super.initState();
    loading = true;
    initialDataLoading();
  }

  initialDataLoading() async {
    collectionSnapshot = await FirebaseFirestore.instance
        .collection(appConfig.COLLECT_AGENCIES)
        .get();
    // print(collectionSnapshot);

    for (var element in collectionSnapshot!.docs) {
      Agency _agency = Agency.fromDocument(element.data(), element.id);

      agencyUser!.add(_agency);
      tmpAgencyUser!.add(_agency);
    }

    if (widget.acquiredContact.assignedAgencyId != null) {
      selectedAgency = agencyUser!.firstWhere(
          (element) => element.id == widget.acquiredContact.assignedAgencyId);
    }

    setState(() {
      loading = false;
    });
  }

  filterAgency(value) {
    tmpAgencyUser!.clear();

    for (var i = 0; i < agencyUser!.length; i++) {
      if (agencyUser![i].name!.toLowerCase().indexOf(value.toLowerCase()) >=
          0) {
        // print({value.toLowerCase(), agencyUser![i].name});
        tmpAgencyUser!.add(agencyUser![i]);
      }
    }

    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        // setState(() {
        //   widget.initialFetchContacts!(widget.agency);
        // });
        return true;
      },
      child: Material(
        borderRadius: BorderRadius.circular(20),
        child: StatefulBuilder(
            builder: (BuildContext context, StateSetter setPopupState) {
          return Container(
            height: 550,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              border: Border(
                  bottom: BorderSide(
                      width: 1, color: Color.fromRGBO(226, 226, 226, 1)),
                  left: BorderSide(
                      width: 1, color: Color.fromRGBO(226, 226, 226, 1)),
                  top: BorderSide(
                      width: 1, color: Color.fromRGBO(226, 226, 226, 1)),
                  right: BorderSide(
                      width: 1, color: Color.fromRGBO(226, 226, 226, 1))),
              color: Colors.white,
            ),
            width: MediaQuery.of(context).size.width * 0.5,
            padding: EdgeInsets.symmetric(horizontal: 30, vertical: 20),
            child: Stack(
              alignment: Alignment.center,
              children: [
                Container(
                  width: 450,
                  child: Column(
                    children: [
                      NarFormLabelWidget(
                        label: 'Assegnazione',
                        textColor: Colors.black,
                        fontSize: 22,
                        fontWeight: '800',
                      ),
                      NarFormLabelWidget(
                          label: widget.acquiredContact.address,
                          textColor: Color.fromRGBO(113, 113, 113, 1),
                          fontSize: 15,
                          fontWeight: '600'),
                      Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            NarFormLabelWidget(
                                label: 'Stato: ',
                                textColor: Color.fromRGBO(101, 101, 101, 1),
                                fontSize: 13,
                                fontWeight: '600'),
                            NarFormLabelWidget(
                                label: widget.acquiredContact.unlocked!
                                    ? 'Sbloccato'
                                    : 'Non sbloccato',
                                textColor: widget.acquiredContact.unlocked!
                                    ? Theme.of(context).primaryColor
                                    : Colors.red,
                                fontSize: 13,
                                fontWeight: '600'),
                          ]),
                      SizedBox(height: 12),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          NarFormLabelWidget(
                              label: 'Seleziona un’agenzia',
                              textColor: Color.fromRGBO(101, 101, 101, 1),
                              fontSize: 13,
                              fontWeight: '600'),
                          Container(
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(10),
                                    topRight: Radius.circular(10)),
                                border: Border(
                                    bottom: BorderSide(
                                        width: 1,
                                        color:
                                            Color.fromRGBO(226, 226, 226, 1)),
                                    left: BorderSide(
                                        width: 1,
                                        color:
                                            Color.fromRGBO(226, 226, 226, 1)),
                                    top: BorderSide(
                                        width: 1,
                                        color:
                                            Color.fromRGBO(226, 226, 226, 1)),
                                    right: BorderSide(
                                        width: 1,
                                        color:
                                            Color.fromRGBO(226, 226, 226, 1)))),
                            padding: const EdgeInsets.symmetric(
                                vertical: 13, horizontal: 20),
                            width: 394,
                            child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  selectedAgency != null
                                      ? Text(
                                          selectedAgency!.name!,
                                          style: TextStyle(
                                              fontWeight: FontWeight.bold),
                                        )
                                      : Text(
                                          "Seleziona un'agenzia",
                                          style: TextStyle(
                                              fontStyle: FontStyle.italic),
                                        ),
                                  /*Icon(
                                    Icons.keyboard_arrow_down,
                                    color: Color.fromRGBO(166, 166, 166, 1),
                                    size: 22,
                                  )*/
                                ]),
                          ),
                          SizedBox(
                            width: 450,
                            child: TextField(
                              decoration: new InputDecoration(
                                contentPadding: const EdgeInsets.symmetric(
                                    vertical: 13, horizontal: 20),
                                hintText: 'Cerca agenzia...',
                                suffixIcon: Icon(
                                  Icons.search,
                                  size: 30,
                                ),
                                suffixIconColor:
                                    Color.fromRGBO(193, 193, 193, 1),
                                enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(0),
                                    borderSide: BorderSide(
                                        width: 1,
                                        color:
                                            Color.fromRGBO(226, 226, 226, 1))),
                                focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(0),
                                    borderSide: BorderSide(
                                        width: 1,
                                        color:
                                            Color.fromRGBO(226, 226, 226, 1))),
                                focusColor: Color.fromRGBO(242, 242, 242, 1),
                                fillColor: Color.fromRGBO(242, 242, 242, 1),
                                // bottom: BorderSide(width: 1, color: Color.fromRGBO(226, 226, 226, 1)), left: BorderSide(width: 1, color: Color.fromRGBO(226, 226, 226, 1)), right: BorderSide(width: 1, color: Color.fromRGBO(226, 226, 226, 1)))
                              ),
                              style: TextStyle(
                                color: Colors.black,
                                fontSize: 14.0,
                                fontWeight: FontWeight.w800,
                                fontStyle: FontStyle.normal,
                                letterSpacing: 1,
                              ),
                              controller: contSearchAgency,
                              onSubmitted: (value) {
                                filterAgency(value);
                                // setState(() {});
                              },
                              onChanged: (value) {
                                filterAgency(value);
                                // setState(() {});
                              },
                            ),
                          ),
                        ],
                      ),
                      Container(
                        height: 200,
                        width: 450,
                        decoration: BoxDecoration(
                            // border: Border(
                            //   bottom: BorderSide(width: 1, color: Color.fromRGBO(226, 226, 226, 1)),
                            //   // left: BorderSide(width: 1, color: Color.fromRGBO(226, 226, 226, 1)),
                            //   // right: BorderSide(width: 1, color: Color.fromRGBO(226, 226, 226, 1))
                            //   )
                            ),
                        child: SingleChildScrollView(
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              loading == true
                                  ? Text('Caricamento')
                                  : ListView.builder(
                                      key: listviewKey,
                                      scrollDirection: Axis.vertical,
                                      shrinkWrap: true,
                                      itemCount: tmpAgencyUser!.length,
                                      itemBuilder:
                                          (BuildContext context, int index) {
                                        return MouseRegion(
                                          cursor: SystemMouseCursors.click,
                                          child: GestureDetector(
                                            key: ValueKey(
                                                tmpAgencyUser![index].id),
                                            onTap: () {
                                              if (widget.acquiredContact
                                                      .assignedAgencyId ==
                                                  tmpAgencyUser![index].id) {
                                                return;
                                              }

                                              setState(() {
                                                selectedAgency =
                                                    tmpAgencyUser![index];
                                              });

                                              return;
                                              /*setState(() {
                                                  loading = true;
                                                });
                                                widget.acquiredContact
                                                        .assignedAgencyId =
                                                    tmpAgencyUser![index].id;
                                                widget.acquiredContact
                                                        .assignedAgencyTimestamp =
                                                    DateTime.now()
                                                        .millisecondsSinceEpoch;
                                                widget.acquiredContact
                                                        .dateUnlocked =
                                                    "Not unlocked";
                                                widget.acquiredContact.unlocked =
                                                    null;
                                        
                                                updateDocument(
                                                    'valuatorSubmissions',
                                                    widget.acquiredContact
                                                        .firebaseId!,
                                                    widget.acquiredContact
                                                        .toMap());
                                        
                                                NewarcNotification notification =
                                                    NewarcNotification.empty();
                                                //notification.message =
                                                //    "Nuovo contatto ricevuto: ${widget.acquiredContact.address} ${widget.acquiredContact.streetNumber}, ${widget.acquiredContact.city}";
                                                notification.message =
                                                    "Nuovo contatto ricevuto!";
                                                writeDocument(
                                                    'agencies/${tmpAgencyUser![index].id}/notifications',
                                                    notification.toMap());
                                                updateDocument(
                                                    'agencies/',
                                                    tmpAgencyUser![index].id!,
                                                    {'notificationsRead': false});
                                        
                                                hasUpdated = true;
                                        
                                                sendEmail(4302701, widget.agency);
                                        
                                                setState(() {
                                                  loading = false;
                                                });*/
                                            },
                                            child: Container(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      vertical: 13,
                                                      horizontal: 20),
                                              decoration: BoxDecoration(
                                                  color: widget.acquiredContact
                                                              .assignedAgencyId ==
                                                          tmpAgencyUser![index]
                                                              .id
                                                      ? Color.fromRGBO(
                                                          250, 250, 250, 1)
                                                      : Colors.white,
                                                  border: Border(
                                                      bottom: BorderSide(
                                                          width: 1,
                                                          color: Color.fromRGBO(
                                                              226, 226, 226, 1)),
                                                      left: BorderSide(
                                                          width: 1,
                                                          color:
                                                              Color.fromRGBO(226, 226, 226, 1)),
                                                      right: BorderSide(width: 1, color: Color.fromRGBO(226, 226, 226, 1)))),
                                              child: NarFormLabelWidget(
                                                label:
                                                    tmpAgencyUser![index].name!,
                                                textColor: widget
                                                            .acquiredContact

                                                            .assignedAgencyId ==
                                                        tmpAgencyUser![index].id
                                                    ? Color.fromRGBO(
                                                        10, 10, 10, 1)
                                                    : Color.fromRGBO(
                                                        80, 80, 80, 1),
                                                fontSize: 13,
                                                fontWeight: widget
                                                            .acquiredContact
                                                            .assignedAgencyId ==
                                                        tmpAgencyUser![index].id
                                                    ? '800'
                                                    : '600',
                                              ),
                                            ),
                                          ),
                                        );
                                      }),
                            ],
                          ),
                        ),
                      ),
                      SizedBox(
                        height: 20,
                      ),
                      NarButtonWidget(
                        text: 'Conferma assegnazione',
                        onClick: () {
                          if (selectedAgency != null) {
                            widget.acquiredContact.assignedAgencyId =
                                selectedAgency!.id;
                            widget.acquiredContact.assignedAgencyTimestamp =
                                DateTime.now().millisecondsSinceEpoch;
                            widget.acquiredContact.dateUnlocked =
                                "Not unlocked";
                            widget.acquiredContact.unlocked = false;
                            widget.acquiredContact.contactStage =
                                "Da sbloccare";

                            updateDocument(
                                'valuatorSubmissions',
                                widget.acquiredContact.firebaseId!,
                                widget.acquiredContact.toMap());

                            NewarcNotification notification =
                                NewarcNotification.empty();
                            //notification.message =
                            //    "Nuovo contatto ricevuto: ${widget.acquiredContact.address} ${widget.acquiredContact.streetNumber}, ${widget.acquiredContact.city}";
                            notification.message = "Nuovo contatto ricevuto!";
                            writeDocument(
                                'agencies/${selectedAgency!.id}/notifications',
                                notification.toMap());
                            updateDocument('agencies/', selectedAgency!.id!,
                                {'notificationsRead': false});

                            hasUpdated = true;

                            //TODO the email is not set for some reason
                            if (selectedAgency!.email != '-') {
                              sendEmail(templateId: 4302701, agency: selectedAgency!, subject: "Nuovo contatto assegnato!");
                            }
                          }

                          if ( widget.agency != null && hasUpdated == true) {
                            Navigator.pop(context,
                                widget.initialFetchContacts!(widget.agency));
                          } else {
                            Navigator.pop(context);
                          }
                        },
                        color: Color.fromRGBO(78, 154, 122, 1),
                        textColor: Colors.white,
                        fontSize: 14,
                        fontWeight: '700',
                        borderRadius: 10,
                        borderSideColor: Color.fromRGBO(78, 154, 122, 1),
                        height: 40,
                      )
                    ],
                  ),
                ),
                Positioned(
                  top: 0,
                  right: 0,
                  child: IconButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      icon: Icon(Icons.close, size: 30, color: Colors.black)),
                )
              ],
            ),
          );
        }),
      ),
    );
  }

  // @override
  // Widget build(BuildContext context) {
  //   return Container(
  //     height: 350,
  //     width: MediaQuery.of(context).size.width * 0.6,
  //     margin: EdgeInsets.symmetric(horizontal: 20),
  //     padding: EdgeInsets.symmetric(horizontal: 30, vertical: 20),
  //     child: Material(
  //       color: Colors.white,
  //       borderRadius: BorderRadius.circular(10),
  //       child: StreamBuilder(
  //         stream: FirebaseFirestore.instance
  //             .collection(appConfig.COLLECT_AGENCIES)
  //             .snapshots(),
  //         builder: (BuildContext context,
  //             AsyncSnapshot<QuerySnapshot<Map<String, dynamic>>> snapshot) {
  //           if (!snapshot.hasData) {
  //             return Center(
  //               child: CircularProgressIndicator(),
  //             );
  //           } else {
  //             //return //Text("Totali" + snapshot.data!.docs.length.toString());
  //             return Padding(
  //               padding:
  //                   const EdgeInsets.symmetric(horizontal: 8.0, vertical: 10),
  //               child: Column(children: [
  //                 NarFormLabelWidget(label: 'Assegnazione', textColor: Colors.black, fontSize: 22, fontWeight: '800',),
  //                 // Text(
  //                 //   "Stato agenzia",
  //                 //   style: TextStyle(
  //                 //       color: Theme.of(context).primaryColor,
  //                 //       fontSize: 21,
  //                 //       fontWeight: FontWeight.bold),
  //                 // ),
  //                 // SizedBox(height: 5),
  //                 NarFormLabelWidget(label: widget.acquiredContact.address, textColor: Color.fromRGBO(113, 113, 113, 1), fontSize: 15, fontWeight: '600'),
  //                 SizedBox(height: 10),
  //                 Expanded(
  //                   child: Column(
  //                     mainAxisSize: MainAxisSize.max,
  //                     children: [
  //                       Container(
  //                         width: 300,
  //                         child: ListView(
  //                           shrinkWrap: false,
  //                           children:
  //                               List.generate(snapshot.data!.docs.length, (index) {
  //                             Agency _agency = Agency.fromDocument(
  //                                 snapshot.data!.docs.elementAt(index).data(),
  //                                 snapshot.data!.docs.elementAt(index).id);
  //                             return ListTile(

  //                               title: NarFormLabelWidget( label: _agency.name!, textColor: Color.fromRGBO(80, 80, 80, 1), fontSize: 13, fontWeight: '600', ),
  //                               // subtitle: (widget.acquiredContact.assignedAgencyId !=
  //                               //             null) &&
  //                               //         (widget.acquiredContact.assignedAgencyId ==
  //                               //             _agency.id)
  //                               //     ? Text(
  //                               //         "Data sblocco: " +
  //                               //             widget.acquiredContact.dateUnlocked,
  //                               //         style: TextStyle(fontWeight: FontWeight.bold),
  //                               //       )
  //                               //     : Container(),
  //                               // secondary: const Icon(Icons.business),
  //                               autofocus: false,
  //                               // activeColor: Colors.green,
  //                               // checkColor: Colors.white,
  //                               selected: (widget.acquiredContact.assignedAgencyId !=
  //                                       null) &&
  //                                   (widget.acquiredContact.assignedAgencyId ==
  //                                       _agency.id),
  //                               // value: (widget.acquiredContact.assignedAgencyId !=
  //                               //         null) &&
  //                               //     (widget.acquiredContact.assignedAgencyId ==
  //                               //         _agency.id),
  //                               // onTap: () => {},
  //                               onTap: () async {
  //                                 if (widget.acquiredContact.assignedAgencyId ==
  //                                     _agency.id) {
  //                                   return;
  //                                 }

  //                                 setState(() {
  //                                   loading = true;
  //                                 });
  //                                 widget.acquiredContact.assignedAgencyId =
  //                                     _agency.id;
  //                                 widget.acquiredContact.assignedAgencyTimestamp =
  //                                     DateTime.now().millisecondsSinceEpoch;
  //                                 widget.acquiredContact.dateUnlocked =
  //                                     "Not unlocked";
  //                                 widget.acquiredContact.unlocked = null;

  //                                 updateDocument(
  //                                     'valuatorSubmissions',
  //                                     widget.acquiredContact.firebaseId!,
  //                                     widget.acquiredContact.toMap());

  //                                 NewarcNotification notification =
  //                                     NewarcNotification.empty();
  //                                 //notification.message =
  //                                 //    "Nuovo contatto ricevuto: ${widget.acquiredContact.address} ${widget.acquiredContact.streetNumber}, ${widget.acquiredContact.city}";
  //                                 notification.message = "Nuovo contatto ricevuto!";
  //                                 writeDocument(
  //                                     'agencies/${_agency.id}/notifications',
  //                                     notification.toMap());
  //                                 updateDocument('agencies/', _agency.id!,
  //                                     {'notificationsRead': false});

  //                                 sendEmail(4302701, widget.agency);

  //                                 setState(() {
  //                                   loading = false;
  //                                 });
  //                               },
  //                             );
  //                           }),
  //                         ),
  //                       ),
  //                     ],
  //                   ),
  //                 )
  //               ]),
  //             );
  //           }
  //         },
  //       ),
  //     ),
  //   );
  // }
}
