import 'package:newarc_platform/classes/renovationContact.dart';

class RenovationQuotation{
  // int? indexPlace;
  
  String? id;
  String? code;
  int? codeCounter;
  int? year; 
  String? renovationContactId;
  String? paymentMode;
  String? constructionDuration;
  String? status;
  bool? isArchived;
  RenovationContact? renovationContact;

  String? discountType;
  String? discount;
  
  List<RenovationActivityCategory>? renovationActivity = [];
  int? created;
  int? modificationDate;
  int? version;
  int? revision;
  String? comment;

  RenovationQuotation(Map<String, dynamic> data) {
    this.id = data['id'];
    this.code = data['code'];
    this.codeCounter = data['codeCounter'];
    this.year = data['year'];
    this.renovationContactId = data['renovationContactId'];
    this.paymentMode = data['paymentMode'] == null ? '' : data['paymentMode'];
    this.constructionDuration = data['constructionDuration'] == null ? '' : data['constructionDuration'];
    this.renovationActivity = data['renovationActivity'] == null ? [] : data['renovationActivity'];
    this.created = data['created'];
    this.status = data['status'];
    this.isArchived = data['isArchived'];
    this.renovationContact = null;

    this.discountType = data['discountType'];
    this.discount = data['discount'];
    this.version = data['version'];
    this.version = data['revision'];
    this.modificationDate = data['modificationDate'];
    this.comment = data['comment'];
  }

  RenovationQuotation.empty(){
    this.id = '';
    this.code = '';
    this.codeCounter = 0;
    this.year = 0;
    this.renovationContactId = '';
    this.paymentMode = '';
    this.constructionDuration = '';
    this.status = '';
    this.isArchived = false;
    this.renovationActivity = [];
    this.created = null;
    this.discountType = 'Percentuale';
    this.discount = '0%';
    this.version = 1;
    this.revision = 0;
    this.modificationDate = null;
    this.comment = '';
  }

  Map<String, dynamic> toMap() {
    return {
      'code': this.code,
      'codeCounter': this.codeCounter,
      'year': this.year,
      'renovationContactId': this.renovationContactId,
      'paymentMode': this.paymentMode,
      'constructionDuration': this.constructionDuration,
      'status': this.status,
      'isArchived': this.isArchived == null ? false : this.isArchived,
      'renovationActivity': this.renovationActivity!.map((e) => e.toMap()).toList(),
      'created': this.created,
      'discountType': this.discountType,
      'discount': this.discount,
      'version': this.version,
      'revision': this.revision,
      'modificationDate': this.modificationDate,
      'comment': this.comment,
    };
  }

  RenovationQuotation.fromDocument(Map<String, dynamic> data, String id) {

    try {

      this.id = id;
      this.code = data['code'] == null ? '' : data['code'];
      this.codeCounter = data['codeCounter'] == null ? 0 : data['codeCounter'];
      this.year = data['year'] == null ? 0 : data['year'];
      this.renovationContactId = data['renovationContactId'];
      this.paymentMode = data['paymentMode'] == null ? '' : data['paymentMode'];
      this.status = data['status'] == null ? '' : data['status'];
      this.isArchived = data['isArchived'] == null ? false : data['isArchived'];
      this.constructionDuration = data['constructionDuration'] == null ? '' : data['constructionDuration'];
      this.created = data['created']; 
      this.modificationDate = data['modificationDate'];
      this.version = data['version'];
      this.revision = data['revision'];

      this.discountType = data['discountType'] ??'';
      this.discount = data['discount'] ?? '';
      this.comment = data['comment'] ?? '';

      if( data['renovationActivity'] != null ) {

        if( data['renovationActivity'].length > 0 ) {
          for (var i = 0; i < data['renovationActivity'].length ; i++) {
            this.renovationActivity!.add( RenovationActivityCategory.fromDocument(data['renovationActivity'][i], i ) );
          }
        } else {
          this.renovationActivity = [];
        }
        
      } else {
        data['renovationActivity'] = [];
      }

      
    } catch (e, s) {
      print({'Class renovationQuotation.dart', e, s});
    }
    
  }

}

class RenovationActivityCategory {

  int? index;
  String? category;
  List<RenovationActivity>? activity = [];
  

  RenovationActivityCategory(Map<String, dynamic> data) {
    
    this.index = data['index'] == '' ? 0 : data['index'];
    this.category = data['category'] == null ? '' : data['category'];
    this.activity = data['activity'] == null ? [] : data['activity'];
    

  }

  RenovationActivityCategory.empty(){
    this.index = 0;
    this.category = '';
    this.activity = [];
    
  }

  Map<String, dynamic> toMap() {
    return {
      'index': this.index,
      'category': this.category,
      'activity': this.activity!.map((e) => e.toMap())
    };
  }

  RenovationActivityCategory.fromDocument(Map<String, dynamic> data, int index) {
    this.index = index;
    this.category = data['category'];
    
    if( data['activity'] != null && data['activity'].length > 0 ) {

      for (var i = 0; i < data['activity'].length ; i++) {
        this.activity!.add( RenovationActivity.fromDocument(data['activity'][i], data['activity'][i]['index'] ) );
      }
      
    } else {
      data['activity'] = [];
    }
  }

}

class RenovationActivity {

  int? index;
  String? title;
  String? measurementUnit;
  double? quantity;
  double? unitPrice;
  String? description;
  String? priceLevel;
  String? subCategory;
  String? code;
  String? comment; 
  bool? isDiscounted;
  bool? isManualActivity;

  RenovationActivity(Map<String, dynamic> fixedProperty) {
    
    this.index = fixedProperty['index'] == '' ? 0 : fixedProperty['index'];
    this.title = fixedProperty['title'] == null ? '' : fixedProperty['title'];
    this.measurementUnit = fixedProperty['measurementUnit'] == null ? '' : fixedProperty['measurementUnit'];
    this.priceLevel = fixedProperty['priceLevel'] == null ? '' : fixedProperty['priceLevel'];
    this.quantity =  fixedProperty['quantity'] == null ? 0 : fixedProperty['quantity'];
    this.unitPrice =  fixedProperty['unitPrice'] == null ? 0 : fixedProperty['unitPrice'];
    this.description =  fixedProperty['description'] == null ? '' : fixedProperty['description'];
    this.subCategory =  fixedProperty['subCategory'] == null ? '' : fixedProperty['subCategory'];
    this.code =  fixedProperty['code'] == null ? '' : fixedProperty['code'];
    this.comment =  fixedProperty['comment'] == null ? '' : fixedProperty['comment'];
    this.isDiscounted =  fixedProperty['isDiscounted'] == null ? false : fixedProperty['isDiscounted'];
    this.isManualActivity =  fixedProperty['isManualActivity']??false;

  }

  RenovationActivity.empty(){
    this.index = 0;
    this.title = '';
    this.measurementUnit = '';
    this.priceLevel = '';
    this.quantity = 0;
    this.unitPrice = 0;
    this.description = '';
    this.subCategory = '';
    this.code = '';
    this.comment = '';
    this.isDiscounted = false;
    this.isManualActivity = false;
    
  }

  Map<String, dynamic> toMap() {
    return {
      'index': this.index,
      'title': this.title,
      'measurementUnit': this.measurementUnit,
      'priceLevel': this.priceLevel,
      'quantity': this.quantity,
      'unitPrice': this.unitPrice,
      'description': this.description,
      'subCategory': this.subCategory,
      'code': this.code,
      'comment': this.comment,
      'isDiscounted': this.isDiscounted,
      'isManualActivity': this.isManualActivity
      
    };
  }

  RenovationActivity.fromDocument(Map<String, dynamic> data, int index) {
    this.index = index;
    this.title = data['title'];
    this.measurementUnit = data['measurementUnit'];
    this.priceLevel = data['priceLevel'];
    this.quantity = data['quantity'] != null ? data['quantity'] : 0;
    this.unitPrice = data['unitPrice'] != null ? data['unitPrice'] : 0;
    this.description = data['description'] != null ? data['description'] : '';
    this.subCategory = data['subCategory'] != null ? data['subCategory'] : '';
    this.code = data['code'] != null ? data['code'] : '';
    this.comment = data['comment'] != null ? data['comment'] : '';
    this.isDiscounted = data['isDiscounted'] != null ? data['isDiscounted'] : false;
    this.isManualActivity = data['isManualActivity']??false;
  }

}