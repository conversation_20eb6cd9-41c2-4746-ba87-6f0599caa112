import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class CommonUtils {
  static const bloccata = 'bloccata';
  static const daCompletare = 'da completare';
  static const preventivazione = 'preventivazione';
  static const inAnalisi = 'in analisi';
  static const confermata = 'confermata';
  static const inLavorazione = 'in lavorazione';
  static const venduto = 'venduto';
  static const nonVenduto = 'non venduto';
  static const completato = 'completato';
  static const daSbloccare = 'Da sbloccare';
  static const daContattare = 'Da contattare';
  static const contattato = 'contattato';

  static getColor(String status) {
    switch (status.toLowerCase()) {
      case daCompletare:
        return Color(0xffD4D4D4);
      case preventivazione:
        return Color(0xff5DB1E3);
      case inAnalisi:
        return Color(0xffFEC600);
      case bloccata:
        return Color(0xffE82525);
      case confermata:
        return Color(0xff39C14F);
      case inLavorazione:
        return Color(0xffFF7B00);
      case venduto:
        return Color(0xff39C14F);
      case nonVenduto:
        return Color(0xffDD0000);
      case completato:
        return Color(0xff39C14F);
      case daSbloccare:
        return Color.fromRGBO(166, 166, 166, 1);
      case daContattare:
        return Color.fromRGBO(245, 198, 32, 1);
      case contattato:
        return Color.fromRGBO(86, 195, 229, 1);
      default:
        return Colors.black;
    }
  }


  String formatStringToDecimal({
    required String input,
    String locale = 'it_IT', // Locale for formatting
    int decimalPlaces = 2, // Number of decimal places
    String decimalSeparator = '.', // Decimal separator (e.g., ',' or '.')
  }) {
    // Remove all non-numeric characters except the decimal separator
    input = input.replaceAll(
        RegExp(r'[^0-9' + RegExp.escape(decimalSeparator) + r']'), '');

    if (input.isEmpty) {
      return ''; // Return an empty string for invalid input
    }

    // Check if the string contains a decimal separator
    if (input.contains(decimalSeparator)) {
      int separatorIndex = input.indexOf(decimalSeparator);
      String beforeSeparator = input.substring(
          0, separatorIndex); // Part before the separator
      String afterSeparator = input.substring(
          separatorIndex + 1); // Part after the separator

      // Allow only the specified number of decimal places
      if (afterSeparator.length > decimalPlaces) {
        afterSeparator = afterSeparator.substring(0, decimalPlaces);
      }

      // Format the part before the separator (add thousand separators)
      String formattedBeforeSeparator = NumberFormat('#,##0', locale).format(
        int.tryParse(beforeSeparator.replaceAll('.', '')) ?? 0,
      );

      return '$formattedBeforeSeparator$decimalSeparator$afterSeparator';
    } else {
      // If there's no decimal separator, format the string as an integer
      return NumberFormat('#,##0', locale).format(
        int.tryParse(input.replaceAll('.', '')) ?? 0,
      );
    }
  }


  static const String agencyProjectArchiveEmailSubject = "Progetto archiviato";
  static const int agencyProjectArchiveEmailTemplateId = 6629093;

  static const String subscriptionActivatedEmailSubject = "Abbonamento Immagina attivato";
  static const int subscriptionActivatedEmailTemplateId = 6629098;

  static const String subscriptionActivatedForWorkSideEmailSubject = "Abbonamento Immagina attivato";
  static const int subscriptionActivatedForWorkSideEmailTemplateId = 6913042;

  static const String newRequestEmailSubject = "Nuova richiesta creata";
  static const int newRequestEmailTemplateId = 6629059;

  static const String requestBlockedEmailSubject = "Richiesta di progetto bloccata";
  static const int requestBlockedEmailTemplateId = 6629083;

  static const String requestConfirmedEmailSubject = "Richiesta confermata";
  static const int requestConfirmedEmailTemplateId = 6629084;

  static const String filesUploadedEmailSubject = "Progetto Immagina completato";
  static const int filesUploadedEmailTemplateId = 6629089;

  static const String bankTransferEmailSubject = "Istruzioni per il saldo del tuo abbonamento Immagina";
  static const int bankTransferEmailTemplateId = 6640364;

  static const String newSingleRequestEmailSubject = "Istruzioni per il saldo della tua richiesta singola";
  static const int newSingleRequestEmailTemplateId = 6672905;

  static const String agencyArchiveProjectAndHasSuccessFeeEmailSubject = "Istruzioni per il saldo della Success Fee";
  static const int agencyArchiveProjectAndHasSuccessFeeEmailTemplateId = 6672930;

  static const String agencyNewRequestForWorkSideEmailSubject = "Nuova richiesta";
  static const int agencyNewRequestForWorkSideEmailTemplateId = 6673008;

  static const String agencyProjectArchiveForWorkSideEmailSubject = "Progetto archiviato";
  static const int agencyProjectArchiveForWorkSideEmailTemplateId = 6673103;

  static const String agencyNewOversizedRequestForWorkSideEmailSubject = 'Nuova richiesta preventivo personalizzato';
  static const int agencyNewOversizedRequestForWorkSideEmailTemplateId = 6748687;

  static const String agencyNewOversizedRequestEmailSubject = 'Nuova richiesta preventivo personalizzato';
  static const int agencyNewOversizedRequestEmailTemplateId = 6748663;

  //----------------- Renovation Quotation Status

  static const inAttesa = 'in-attesa';
  static const accettato = 'accettato';
  static const rifiutato = 'rifiutato';
  static const daModificare = 'da-modificare';


  static getRenovationQuotationStatusColor(String status) {
    switch (status.toLowerCase()) {
      case inAttesa:
        return Color(0xffD4D4D4);
      case accettato:
        return Color(0xff39C14F);
      case rifiutato:
        return Color(0xffDD0000);
      case daModificare:
        return Color(0xffF2CB00);
      default:
        return Colors.black;
    }
  }
}