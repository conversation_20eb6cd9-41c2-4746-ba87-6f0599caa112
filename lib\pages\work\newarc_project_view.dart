import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
// import 'package:firebase_auth/firebase_auth.dart';
// import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:newarc_platform/classes/renovationContact.dart';
import 'package:newarc_platform/classes/supplier.dart';
import 'package:newarc_platform/classes/user.dart';
import 'package:newarc_platform/pages/login_page.dart';
import 'package:newarc_platform/pages/work/newarc_project_virtual_tour.dart';
import 'package:newarc_platform/utils/storage.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/classes/newarcProject.dart';
import 'package:newarc_platform/classes/property.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/work/project/add_property_form.dart';
import 'package:newarc_platform/widget/work/project/agreements.dart';
import 'package:newarc_platform/widget/work/project/assign_agency.dart';
import 'package:newarc_platform/widget/work/project/assign_team.dart';
import 'package:newarc_platform/widget/work/project/assign_vendor.dart';
import 'package:newarc_platform/widget/work/project/dashboard.dart';
import 'package:newarc_platform/widget/work/project/economic_account.dart';
import 'package:newarc_platform/widget/work/project/files/brochure.dart';
import 'package:newarc_platform/widget/work/project/files/construction-practices.dart';
import 'package:newarc_platform/widget/work/project/files/deed-and-contract.dart';
import 'package:newarc_platform/widget/work/project/files/design.dart';
import 'package:newarc_platform/widget/work/project/files/floor-plan.dart';
import 'package:newarc_platform/widget/work/project/files/plant-engineering.dart';
import 'package:newarc_platform/widget/work/project/files/render.dart';
import 'package:newarc_platform/widget/work/project/files/rennovation.dart';
import 'package:newarc_platform/widget/work/project/files/videos.dart';
import 'package:newarc_platform/widget/work/project/fixed_asset_details.dart';
import 'package:newarc_platform/widget/work/project/manage_jobs.dart';
import 'package:newarc_platform/widget/work/project/manage_material.dart';
import 'package:newarc_platform/widget/work/project/new_economic.dart';
import 'package:newarc_platform/widget/work/project/payments.dart';
import 'package:newarc_platform/widget/work/project/vendor_and_professional.dart';
import 'package:flutter/scheduler.dart';

class NewarcProjectView extends StatefulWidget {
  final String? projectFirebaseId;
  // final Property? property;
  final Function? initialFetchProperties;
  // final AgencyUser? agencyUser;
  final Function? updateViewCallback;
  final NewarcUser newarcUser;

  NewarcProjectView({
    Key? key,
    required this.newarcUser,
    this.projectFirebaseId = '',
    // this.property,
    this.initialFetchProperties,
    // this.agencyUser,
    this.updateViewCallback,
  }) : super(key: key);

  @override
  State<NewarcProjectView> createState() => _NewarcProjectViewState();
}

class _NewarcProjectViewState extends State<NewarcProjectView>
    with WidgetsBindingObserver {
  late Timer _timer;
  DateTime _lastUserInteraction = DateTime.now();
  Duration _inactivityDuration = Duration(minutes: 10);

  bool loading = false;
  GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
  final NewarcProject? newarcProject = NewarcProject.empty();
  final Property? property = new Property();

  Map supplierTypeIndex = {'Torino': 0, 'Milano': 1, 'Roma': 2};

  Widget? tabContent;

  List<Map> dataList = [];
  final List<Map> userList = [];
  final List<Supplier> suppliers = [];
  final List<Agency> agencies = [];

  List designFiles = [];
  List platEngineeringFiles = [];
  List floorPlanFiles = [];
  List renderFiles = [];
  List brochureFiles = [];
  List preRennovationFiles = [];
  List postRennovationFiles = [];

  Map userRoleIndex = {
    'scouter': 0,
    'renovator': 1,
    'renderist': 2,
    'geometra': 3,
    'media_creator': 4,
    'finance': 5,
    'administration': 6
  };

  int tmpCurrentIndex = 0;
  final List<bool> isInputChangeDetected = [false];

  @override
  void initState() {

    super.initState();
    loading = true;
    
    WidgetsBinding.instance.addObserver(this);

    WidgetsBinding.instance.addPostFrameCallback((_){
      initialFetch();
      _startInactivityTimer();
    });

    print(widget.newarcUser.projectTabAccess);
    
  }

  @override
  void didUpdateWidget(oldWidget) {
    super.didUpdateWidget(oldWidget);
  }

  detectChange() {}

  void _handleUserInteraction() {
    _lastUserInteraction = DateTime.now();
  }

  void _startInactivityTimer() {
    _timer = Timer.periodic(Duration(seconds: 1), (_) {
      final now = DateTime.now();
      final inactivityDuration = now.difference(_lastUserInteraction);
      if (inactivityDuration >= _inactivityDuration) {
        _logoutUser();
      }
    });
  }

  void _logoutUser() {
    Navigator.of(context).pushReplacementNamed(
      LoginPage.route,
    );
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _timer?.cancel();
    super.dispose();
  }

  initialFetch() async {
    DocumentSnapshot<Map<String, dynamic>> collectionSnapshot;

    collectionSnapshot = await FirebaseFirestore.instance
        .collection(appConfig.COLLECT_NEWARC_PROJECTS)
        .doc(widget.projectFirebaseId)
        .get();

    if (collectionSnapshot.data() != null) {
      newarcProject!.copy(NewarcProject.fromDocument(
          collectionSnapshot.data()!, widget.projectFirebaseId!));
    }

    designFiles = newarcProject!.designFiles!;

    if (newarcProject!.type == 'Ristrutturazione' &&
        newarcProject!.renovationContactId != null) {}

    await fetchProperty();
    await fetchUsers();
    await fetchSuppliers();
    await fetchAgencies();
    await fetchRenovatorContact();
    setTabContent();

    setState(() {
      loading = false;
    });
  }

  updateProject(NewarcProject project, { updateTabs = false }) {
    setState(() {
      newarcProject!.copy(project);
    });
    if( updateTabs == true ) {
      setTabContent();
    }
    
  }

  Future<void> fetchRenovatorContact() async {
    setState(() {
      loadingMessage = 'Caricamento renovator';
    });

    try {
      if (newarcProject!.renovationContactId != '') {
        DocumentSnapshot<Map<String, dynamic>> collectionSnapshot =
            await FirebaseFirestore.instance
                .collection(appConfig.COLLECT_RENOVATION_CONTACTS)
                .doc(newarcProject!.renovationContactId)
                .get();

        RenovationContact renovationContact = RenovationContact.fromDocument(
            collectionSnapshot.data()!, collectionSnapshot.id);

        newarcProject!.fixedProperty!.refFirstName = renovationContact.name;
        newarcProject!.fixedProperty!.refLastName = renovationContact.surname;
        newarcProject!.fixedProperty!.refPhone = renovationContact.phone;
        newarcProject!.fixedProperty!.refEmail = renovationContact.email;
      }
    } catch (e, s) {
      // print({e, s});
    }
  }

  String loadingMessage = 'Caricamento progetto';

  Future<void> fetchProperty() async {
    setState(() {
      loadingMessage = 'Caricamento progetto';
    });

    if (newarcProject!.propertyId != null) {
      DocumentSnapshot<Map<String, dynamic>> collectionSnapshot =
          await FirebaseFirestore.instance
              .collection(appConfig.COLLECT_NEWARC_HOME)
              .doc(newarcProject!.propertyId)
              .get();

      property!.setData(collectionSnapshot);
    }
  }

  Future<void> fetchAgencies() async {
    setState(() {
      loadingMessage = 'Caricamento agenzie';
    });

    QuerySnapshot<Map<String, dynamic>> collectionSnapshot =
        await FirebaseFirestore.instance
            .collection(appConfig.COLLECT_AGENCIES)
            .where('isActive', isEqualTo: true)
            .get();

    agencies.add(Agency.empty());

    for (QueryDocumentSnapshot<Map<String, dynamic>> agencyDocument
        in collectionSnapshot.docs) {
      try {
        agencies
            .add(Agency.fromDocument(agencyDocument.data(), agencyDocument.id));
        /*agencies.add({
          'value': element.id,
          'label': element.data()['name'] == null
              ? 'No name'
              : element.data()['name']
        });*/
      } catch (e, s) {
        // print({"error in document Assign Agency -- ${agencyDocument.id}", e});
        // print(s);
      }
    }
    //print({'agencies', agencies});
  }

  Future<void> fetchSuppliers() async {
    setState(() {
      loadingMessage = 'Caricamento venditori';
    });

    QuerySnapshot<Map<String, dynamic>> collectionSnapshot =
        await FirebaseFirestore.instance
            .collection(appConfig.COLLECT_SUPPLIERS)
            .where('isArchived', isEqualTo: false)
            .where('isActive', isEqualTo: true)
            .get();
    suppliers.add(Supplier.empty());
    for (QueryDocumentSnapshot<Map<String, dynamic>> supplierDoc
        in collectionSnapshot.docs) {
      suppliers.add(Supplier.fromDocument(supplierDoc.data(), supplierDoc.id));
    }

  }

  Future<void> fetchUsers() async {
    setState(() {
      loadingMessage = 'Caricamento team';
    });

    userList.add({
      "name": "COMMERCIALE",
      'controller': new TextEditingController(),
      "subMenu": []
    });
    userList.add({
      "name": "ARCHITETTO",
      'controller': new TextEditingController(),
      "subMenu": []
    });
    userList.add({
      "name": "RENDERIST",
      'controller': new TextEditingController(),
      "subMenu": []
    });
    userList.add({
      "name": "GEOMETRA",
      'controller': new TextEditingController(),
      "subMenu": []
    });
    userList.add({
      "name": "MEDIA CREATOR",
      'controller': new TextEditingController(),
      "subMenu": []
    });
    userList.add({
      "name": "FINANCE",
      'controller': new TextEditingController(),
      "subMenu": []
    });
    userList.add({
      "name": "AMMINISTRAZIONE",
      'controller': new TextEditingController(),
      "subMenu": []
    });
    // userList.add({
    //   "name": "MASTER",
    //   'controller': new TextEditingController(),
    //   "subMenu": []
    // });

    userList[0]['subMenu'].add({'value': '', 'label': ''});
    userList[1]['subMenu'].add({'value': '', 'label': ''});
    userList[2]['subMenu'].add({'value': '', 'label': ''});
    userList[3]['subMenu'].add({'value': '', 'label': ''});
    userList[4]['subMenu'].add({'value': '', 'label': ''});
    userList[5]['subMenu'].add({'value': '', 'label': ''});
    userList[6]['subMenu'].add({'value': '', 'label': ''});

    QuerySnapshot<Map<String, dynamic>> collectionSnapshot =
        await FirebaseFirestore.instance
            .collection(appConfig.COLLECT_USERS)
            .where('type', isEqualTo: 'newarc')
            .where('isActive', isEqualTo: true)
            .get();

    for (var element in collectionSnapshot.docs) {
      try {
        NewarcUser _user = NewarcUser.fromDocument(element.data(), element.id);

        if (_user.role == null || _user.role == '') continue;

        var typeIndex = userRoleIndex[_user.role];

        if (typeIndex.runtimeType == Null) continue;

        String image = _user.profilePicture != null
            ? await printUrl('users/', _user.id!, _user.profilePicture!)
            : await printUrl('utils/', "", "imageplaceholder.png");

        if (_user.isArchived == true &&
            newarcProject!.assignedTeam.length > 0) {
          for (var i = 0; i < newarcProject!.assignedTeam.length; i++) {
            Map e = newarcProject!.assignedTeam[i];

            if (e['type'].toString().toLowerCase() ==
                    _user.role.toString().toLowerCase() &&
                e['userId'] == _user.id) {
              userList[typeIndex]['subMenu'].add({
                'value': _user.id,
                'label': _user.firstName! +
                    ' ' +
                    _user.lastName! +
                    (_user.isArchived == true ? ' (Utente eliminato)' : ''),
                'image': image,
              });
            } else {
              continue;
            }
          }
        } else if (_user.isArchived == false) {
          userList[typeIndex]['subMenu'].add({
            'value': _user.id,
            'label': _user.firstName! +
                ' ' +
                _user.lastName! +
                (_user.isArchived == true ? ' (Utente eliminato)' : ''),
            'image': image,
          });
        }
      } catch (e, s) {
        // print({"error in document -- ${element.id}", e});
        // print(s);
      }
    }
  }

  unselectAllTabs() {
    for (var i = 0; i < dataList.length; i++) {
      dataList[i]['isExpanded'] = false;
      for (var j = 0; j < dataList[i]['subMenu'].length; j++) {
        dataList[i]['subMenu'][j]['selected'] = false;
      }
    }
  }

  setTabContent() async {
    List<Map> acquisitionMenu = [];
    List<Map> economic = [];

    Map tabAccess = widget.newarcUser.projectTabAccess!;

    if (newarcProject!.type == 'Ristrutturazione') {
      acquisitionMenu = [];
      economic = [
        
        {
          'name': 'Economics',
          'selected': false,
          "modal": NewEconomic(
              project: newarcProject,
              suppliers: suppliers,
              updateProject: updateProject)
          }
      ];
    } else {
      acquisitionMenu = [
        if( tabAccess['acquisizione/rivendita']['status'] == 'show' && tabAccess['acquisizione/rivendita']['children']['Agenzia'] == 'show' ) {
          'name': 'Agenzia',
          "selected": false,
          "modal": ProjectAssignAgency(
              project: newarcProject,
              agencies: agencies,
              isInputChangeDetected: isInputChangeDetected,
              updateProject: updateProject)
        },
        if( tabAccess['acquisizione/rivendita']['status'] == 'show' && tabAccess['acquisizione/rivendita']['children']['Annuncio'] == 'show' ) {
          'name': 'Annuncio',
          'selected': false,
          "modal": AddPropertyForm(
              project: newarcProject,
              property: property,
              initialFetchProperties: widget.initialFetchProperties)
        }
      ];

      economic = [
        if( tabAccess['economics']['status'] == 'show' && tabAccess['economics']['children']['Pagamenti'] == 'show' ){
          'name': 'Pagamenti',
          'selected': false,
          "modal": ProjectPayments(
              project: newarcProject,
              suppliers: suppliers,
              updateProject: updateProject)
        },
      ];
      if (newarcProject!.type != 'ristrutturazione' && tabAccess['economics']['status'] == 'show' && tabAccess['economics']['children']['Conto Economico'] == 'show' ) {
        economic.add({
          'name': 'Conto economico',
          'selected': false,
          "modal": ProjectManageEconomicAccount(
              project: newarcProject,
              isInputChangeDetected: isInputChangeDetected)
        });
      }
    }

    

    dataList = [
      // {
      //   "hasTitle": false,
      //   "name": "",
      //   "isExpanded": true,
      //   "subMenu": [
      //     {"name": 'Generali', "selected": true, "modal": null}
      //   ]
      // },
      if( tabAccess['generali']['status'] == 'show' ){
        "hasTitle": true,
        "name": "GENERALI",
        "isExpanded": true,
        "subMenu": [
          if( tabAccess['generali']['status'] == 'show' && tabAccess['generali']['children']['Dashboard'] == 'show' ) {
            'name': 'Dashboard',
            'selected': true,
            "modal": ProjectDashboard(
                project: newarcProject, updateProject: updateProject)
          },
          if( tabAccess['generali']['status'] == 'show' && tabAccess['generali']['children']['Immobile'] == 'show' ){
            'name': 'Immobile',
            'selected': false,
            "modal": ProjectFixedAssetDetails(
                project: newarcProject,
                isInputChangeDetected: isInputChangeDetected)
          },
          if( tabAccess['generali']['status'] == 'show' && tabAccess['generali']['children']['Team Newarc'] == 'show' ){
            'name': 'Team Newarc',
            'selected': false,
            "modal": ProjectAssignTeam(
                project: newarcProject,
                userList: userList,
                isInputChangeDetected: isInputChangeDetected)
          },
        ]
      },
      if( tabAccess['acquisizione/rivendita']['status'] == 'show' ){
        "hasTitle": true,
        "name": "ACQUISIZIONE/RIVENDITA",
        "isExpanded": true,
        "subMenu": acquisitionMenu
      },
      if( newarcProject!.type != 'Immagina' && tabAccess['ristrutturazione']['status'] == 'show' ) {
        "hasTitle": true,
        "name": "RISTRUTTURAZIONE",
        "isExpanded": true,
        "subMenu": [
          if( tabAccess['ristrutturazione']['status'] == 'show' && tabAccess['ristrutturazione']['children']['Gestisci Lavori'] == 'show' ) {
            'name': 'Gestisci Lavori',
            'selected': false,
            "modal": ProjectManageJobs(
                project: newarcProject,
                suppliers: suppliers,
                updateProject: updateProject)
          },
          if( tabAccess['ristrutturazione']['status'] == 'show' && tabAccess['ristrutturazione']['children']['Gestisci Materiali'] == 'show' ) {
            'name': 'Gestisci Materiali',
            'selected': false,
            "modal": ProjectManageMaterial(
                project: newarcProject, updateProject: updateProject)
          },
          if( tabAccess['ristrutturazione']['status'] == 'show' && tabAccess['ristrutturazione']['children']['Aggiornamenti cantiere'] == 'show' ) {
            'name': 'Aggiornamenti cantiere',
            'selected': false,
            "modal": Agreements(
              project: newarcProject,
              suppliers: suppliers,
              updateProject: updateProject
            )
          },
        ]
      },
      if( newarcProject!.type != 'Immagina' && tabAccess['economics']['status'] == 'show' ) {
        "hasTitle": true,
        "name": "ECONOMICS",
        "isExpanded": true,
        "subMenu": economic
      },
      if( tabAccess['file e documenti']['status'] == 'show' ) {
        "hasTitle": true,
        "name": "FILE E DOCUMENTI",
        "isExpanded": true,
        "subMenu": [
          if( newarcProject!.type != 'Immagina' && tabAccess['file e documenti']['status'] == 'show' && tabAccess['file e documenti']['children']['Atti e Contratti'] == 'show' ) {
            'name': 'Atti e Contratti',
            'selected': false,
            "modal": ProjectFileDeedAndContract(
              project: newarcProject,
              allFiles: newarcProject!.deedAndContractsFiles,
            )
          },
          if( tabAccess['file e documenti']['status'] == 'show' && tabAccess['file e documenti']['children']['Progettazione'] == 'show' ) {
            'name': 'Progettazione',
            'selected': false,
            "modal": ProjectFileDesigns(
              project: newarcProject,
              allFiles: designFiles,
            )
          },
          if( tabAccess['file e documenti']['status'] == 'show' && tabAccess['file e documenti']['children']['Impiantistica'] == 'show' ){
            'name': 'Impiantistica',
            'selected': false,
            "modal": ProjectFilePlantEngineering(
              project: newarcProject,
              allFiles: newarcProject!.plantEngineeringFiles,
            )
          },
          if( tabAccess['file e documenti']['status'] == 'show' && tabAccess['file e documenti']['children']['Planimetrie'] == 'show' ){
            'name': 'Planimetrie',
            'selected': false,
            "modal": ProjectFileFloorPlan(
              project: newarcProject,
              allFiles: newarcProject!.floorPlanFiles,
            )
          },
          if( tabAccess['file e documenti']['status'] == 'show' && tabAccess['file e documenti']['children']['Render'] == 'show' ){
            'name': 'Render',
            'selected': false,
            "modal": ProjectFileRender(
              project: newarcProject,
              allFiles: newarcProject!.renderFiles,
            )
          },
          if( tabAccess['file e documenti']['status'] == 'show' && tabAccess['file e documenti']['children']['Tour virtuale'] == 'show' ){
            'name': 'Tour virtuale',
            'selected': false,
            "modal": ProjectVirtualTourDetails(
              project: newarcProject,
            )
          },
          if( newarcProject!.type != 'Immagina' && tabAccess['file e documenti']['status'] == 'show' && tabAccess['file e documenti']['children']['Pratiche Edilizie'] == 'show' ){
            'name': 'Pratiche Edilizie',
            'selected': false,
            "modal": ProjectFileConstructionPractices(
              project: newarcProject,
              allFiles: newarcProject!.constructionPracticesFiles,
            )
          },
          if( tabAccess['file e documenti']['status'] == 'show' && tabAccess['file e documenti']['children']['Brochure'] == 'show' ){
            'name': 'Brochure',
            'selected': false,
            "modal": ProjectFileBrochure(
              project: newarcProject,
              allFiles: newarcProject!.brochureFiles,
            )
          },
          if( tabAccess['file e documenti']['status'] == 'show' && tabAccess['file e documenti']['children']['Foto'] == 'show' ){
            'name': 'Foto',
            'selected': false,
            "modal": ProjectFileRennovation(
              project: newarcProject,
              allPreRennoFiles: newarcProject!.preRennovationFiles,
              allPostRennoFiles: newarcProject!.postRennovationFiles,
            )
          },
          if( tabAccess['file e documenti']['status'] == 'show' && tabAccess['file e documenti']['children']['Video'] == 'show' ){
            'name': 'Video',
            'selected': false,
            "modal": ProjectVideos(
              project: newarcProject,
              allVideoFiles: newarcProject!.videoFiles,
            )
          },
        ]
      },
    ];

    for (var i = 0; i < dataList.length; i++) {
      for (var j = 0; j < dataList[i]['subMenu'].length; j++) {
        if (dataList[i]['subMenu'][j]['selected'] == true) {
          // setState(() {
          if (dataList[i]['subMenu'][j]['modal'] != null) {
            tabContent = dataList[i]['subMenu'][j]['modal'];
          } else {
            tabContent = Container(
                height: double.infinity,
                child: Center(
                    child: NarFormLabelWidget(label: 'Nothing to display')));
          }
          // });
        }
      }
    }
  }

  switchTab(currentIndex, tabData, i) {
    setState(() {
      tmpCurrentIndex = currentIndex;
      isInputChangeDetected[0] = false;

      if (tabData['subMenu'][i]['modal'] != null) {
        tabContent = tabData['subMenu'][i]['modal'];
      } else {
        tabContent = Container(
            height: double.infinity,
            child:
                Center(child: NarFormLabelWidget(label: 'Nothing to display')));
      }
    });
  }

  int activeTabIndex = 0;
  int activeItemIndex = 0;

  _buildExpandableContent(
      BuildContext _buildContext, tabData, int currentIndex) {
    List<Widget> columnContent = [];

    for (var i = 0; i < tabData['subMenu'].length; i++) {
      // print({ vehicle['subMenu'][i] });

      columnContent.add(
        MouseRegion(
          cursor: SystemMouseCursors.click,
          child: GestureDetector(
            onTap: () {
              unselectAllTabs();
              tabData['subMenu'][i]['selected'] = true;

              activeTabIndex = currentIndex;
              activeItemIndex = i;

              if (isInputChangeDetected[0] == true) {
                showDialog(
                    context: _buildContext,
                    builder: (_context) {
                      return StatefulBuilder(
                          builder: (BuildContext _bc2, StateSetter setState) {
                        return Center(
                          child: BaseNewarcPopup(
                            title: 'Attenzione!',
                            buttonText: 'Esci senza salvare',
                            onPressed: () async {
                              switchTab(currentIndex, tabData, i);
                              // Navigator.pop(_bc2);
                              return true;
                            },
                            column: Container(
                                height: 150,
                                width: 465,
                                child: Center(
                                  child: Column(
                                    children: [
                                      NarFormLabelWidget(
                                          overflow: TextOverflow.visible,
                                          label:
                                              "Stai uscendo dalla pagina senza aver\nsalvato le modifiche.",
                                          textAlign: TextAlign.center,
                                          fontSize: 18,
                                          fontWeight: '600',
                                          height: 1.5,
                                          textColor: Color(0xFF696969)),
                                      SizedBox(
                                        height: 10,
                                      ),
                                      NarFormLabelWidget(
                                          overflow: TextOverflow.visible,
                                          label: "Vuoi uscire senza salvare?",
                                          textAlign: TextAlign.center,
                                          fontSize: 18,
                                          fontWeight: 'bold',
                                          height: 1.5,
                                          textColor: Color(0xFF696969)),
                                    ],
                                  ),
                                )),
                          ),
                        );
                      });
                    });
              } else {
                switchTab(currentIndex, tabData, i);
              }
              // tmpCurrentIndex = currentIndex;
            },
            child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: tabData['subMenu'][i]['selected']
                      ? Color.fromRGBO(72, 155, 121, 1)
                      : Color.fromRGBO(240, 240, 240, 1),
                ),
                width: double.infinity,
                padding: EdgeInsets.symmetric(vertical: 15, horizontal: 10),
                margin: EdgeInsets.only(bottom: 8),
                child: NarFormLabelWidget(
                  label: tabData['subMenu'][i]['name'],
                  fontSize: 13,
                  fontWeight: 'bold',
                  textColor: tabData['subMenu'][i]['selected']
                      ? Colors.white
                      : Colors.black,
                )),
          ),
        ),
      );
    }

    return columnContent;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: _handleUserInteraction,
      onPanDown: (_) => _handleUserInteraction(),
      onPanUpdate: (_) => _handleUserInteraction(),
      child: Listener(
        onPointerDown: (_) => _handleUserInteraction(),
        onPointerMove: (_) => _handleUserInteraction(),
        onPointerUp: (_) => _handleUserInteraction(),
        onPointerHover: (_) => _handleUserInteraction(),
        child: Scaffold(
          backgroundColor: Colors.white,
          key: scaffoldKey,
          body: LayoutBuilder(
              builder: (BuildContext context, BoxConstraints constraints) {
            return loading
                ? Center(
                    child: Column(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(
                        color: Theme.of(context).primaryColor,
                      ),
                      SizedBox(height: 5),
                      NarFormLabelWidget(label: loadingMessage)
                    ],
                  ))
                : Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                                mainAxisSize: MainAxisSize.min,
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  IconButton(
                                    hoverColor: Colors.transparent,
                                    focusColor: Colors.transparent,
                                    onPressed: () {
                                      if (newarcProject!.type == 'Ristrutturazione'){
                                        widget.updateViewCallback!('renovation-project');
                                      }
                                      else {widget.updateViewCallback!('progetti-in-corso');}
                                    },
                                    icon: SvgPicture.asset(
                                        'assets/icons/arrow_left.svg',
                                        height: 20,
                                        color: Colors.black),
                                  ),
                                  SizedBox(
                                    width: 10,
                                  ),
                                  Column(
                                    mainAxisSize: MainAxisSize.max,
                                    mainAxisAlignment:
                                        MainAxisAlignment.center,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          NarFormLabelWidget(
                                            label: 'Gestisci progetto',
                                            fontSize: 13,
                                            fontWeight: 'bold',
                                            textColor: Color.fromRGBO(
                                                115, 115, 115, 1),
                                          ),
                                          SizedBox(
                                            width: 10,
                                          ),
                                          Container(
                                            padding: EdgeInsets.symmetric(
                                                vertical: 5, horizontal: 10),
                                            decoration: BoxDecoration(
                                              color: Theme.of(context)
                                                  .primaryColor,
                                              borderRadius:
                                                  BorderRadius.circular(5),
                                              border: Border.all(
                                                  color: Theme.of(context)
                                                      .primaryColor),
                                            ),
                                            child: NarFormLabelWidget(
                                              label:
                                                  newarcProject!.type != null
                                                      ? newarcProject!.type
                                                      : '',
                                              fontSize: 10,
                                              fontWeight: 'bold',
                                              textColor: Colors.white,
                                            ),
                                          )
                                        ],
                                      ),
                                      NarFormLabelWidget(
                                        label: newarcProject!.name != null
                                            ? newarcProject!.name
                                            : 'test',
                                        fontSize: 22,
                                        fontWeight: 'bold',
                                        textColor: Colors.black,
                                      ),
                                    ],
                                  )
                                ]),
                            SizedBox(height: 20),
                            Expanded(
                              child: Row(
                                children: [
                                  Container(
                                    width: 275,
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 10, vertical: 20),
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(13),
                                      border: Border.all(
                                          color: Color(0xffe7e7e7)),
                                    ),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Expanded(
                                            child: ListView.builder(
                                                itemCount: dataList.length,
                                                itemBuilder:
                                                    (context, index) {
                                                  return Theme(
                                                    data: ThemeData().copyWith(
                                                        dividerColor: Colors
                                                            .transparent),
                                                    child: dataList[index][
                                                                'hasTitle'] ==
                                                            true
                                                        ? ExpansionTile(
                                                            initiallyExpanded:
                                                                dataList[
                                                                        index]
                                                                    [
                                                                    'isExpanded'],
                                                            trailing:
                                                                Image.asset(
                                                              dataList[index][
                                                                      'isExpanded']
                                                                  ? 'assets/icons/arrow_up.png'
                                                                  : 'assets/icons/arrow_down.png',
                                                              width: 12,
                                                              color: Color(
                                                                  0xff838383),
                                                            ),
                                                            onExpansionChanged:
                                                                (value) {
                                                              setState(() {
                                                                dataList[index]
                                                                        [
                                                                        'isExpanded'] =
                                                                    value;
                                                              });
                                                            },
                                                            tilePadding:
                                                                EdgeInsets.only(
                                                                    left: 5),
                                                            title:
                                                                NarFormLabelWidget(
                                                              label: dataList[
                                                                          index]
                                                                      ['name']
                                                                  .toString()
                                                                  .toUpperCase(),
                                                              fontSize: 13,
                                                              textColor: Color
                                                                  .fromRGBO(
                                                                      117,
                                                                      117,
                                                                      117,
                                                                      1),
                                                            ),
                                                            children: <Widget>[
                                                                Column(
                                                                  children: _buildExpandableContent(
                                                                      context,
                                                                      dataList[
                                                                          index],
                                                                      index),
                                                                ),
                                                              ])
                                                        : Column(
                                                            children:
                                                                _buildExpandableContent(
                                                                    context,
                                                                    dataList[
                                                                        index],
                                                                    index)),
                                                  );
                                                })),
                                      ],
                                    ),
                                  ),
                                  SizedBox(width: 10),
                                  Expanded(
                                    child: Container(
                                      padding: dataList[activeTabIndex]['subMenu'][activeItemIndex]['name'] == 'Annuncio' || dataList[activeTabIndex]['subMenu'][activeItemIndex]['name'] == "Economics"
                                      ? EdgeInsets.symmetric( horizontal: 0, vertical: 0)
                                      : EdgeInsets.symmetric( horizontal: 15, vertical: 10),
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius:
                                            BorderRadius.circular(13),
                                        border: Border.all(color: dataList[activeTabIndex]['subMenu'][activeItemIndex]['name'] == "Economics" ? Colors.white : Color(0xffe7e7e7)),
                                      ),
                                      child: tabContent,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  );
          }),
        ),
      ),
    );
  }
}
