import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/agencyPersone.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import '../../../widget/UI/tab/common_icon_button.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;

class AgencyPersoneDataSource extends DataTableSource {
  final List<AgencyPersone> agencyPersone;
  final BuildContext context;
  final Function() initialFetch;
  final Function(AgencyPersone agencyPersone) onEditTap;
  final Function(String id) onDeleteTap;

  AgencyPersoneDataSource({
    required this.context,
    required this.agencyPersone,
    required this.initialFetch,
    required this.onEditTap(AgencyPersone agencyPersone),
    required this.onDeleteTap(String id),
  });

  @override
  DataRow? getRow(int index) {
    if (index < agencyPersone.length) {
      return DataRow2(
        specificRowHeight: 50,
        cells: [
          DataCell(FutureBuilder<ImageProvider>(
            future: getImage(agencyPersone[index].profilePicturePath?["location"] ?? "",agencyPersone[index].profilePicturePath?["filename"] ?? ""),
            builder: (context, snapshot) {
              if(snapshot.connectionState == ConnectionState.waiting){
                return SizedBox(
                  width: 30,
                  height: 30,
                  child: CircularProgressIndicator(color: Theme.of(context).primaryColor,),
                );
              } else if (snapshot.hasData) {
                return Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                  ),
                  child: ClipOval(
                    child: Image(
                      image: snapshot.data!,
                      fit: BoxFit.cover,
                    ),
                  ),
                );
              } else if (snapshot.hasError) {
                return SizedBox();
              }
              return SizedBox();
            },
          )),
          //?---- Nome
          DataCell(
            NarFormLabelWidget(
              label: "${agencyPersone[index].name ?? ""} ${agencyPersone[index].surname ?? ""}",
              fontSize: 12,
              fontWeight: '600',
              textAlign: TextAlign.start,
              textColor: Colors.black,
            ),
          ),
          //?---- Nome
          DataCell(
            NarFormLabelWidget(
              label: agencyPersone[index].phone ?? "",
              fontSize: 12,
              fontWeight: '600',
              textAlign: TextAlign.start,
              textColor: Colors.black,
            ),
          ),
          DataCell(
            NarFormLabelWidget(
              label: getFormattedDate(agencyPersone[index].insertTimestamp),
              fontSize: 12,
              fontWeight: '600',
              overflow: TextOverflow.visible,
              textAlign: TextAlign.start,
              textColor: Colors.black,
            ),
          ),
          DataCell(
            Row(
              mainAxisAlignment:
              MainAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButtonWidget(
                  onTap: () {
                    onEditTap(agencyPersone[index]);
                  },
                  iconPadding: EdgeInsets.all(8),
                  isSvgIcon: false,
                  borderRadius: 7,
                  backgroundColor: Color(0xFFF1F1F1),
                  icon: 'assets/icons/edit.png',
                  iconColor: AppColor.greyColor,
                ),
                SizedBox(width: 8),
                IconButtonWidget(
                  onTap: () async {
                    onDeleteTap(agencyPersone[index].firebaseId ?? "");
                  },
                  iconPadding: EdgeInsets.all(6),
                  backgroundColor: Colors.transparent,
                  isOnlyBorder: true,
                  isSvgIcon: false,
                  icon: 'assets/icons/trash-process.png',
                  borderColor: Color(0xFFE7B8B8),
                  borderRadius: 7,
                  iconColor: Color(0xFFEA3132),
                ),
              ],
            ),
          ),
        ],
      );
    }

    return null;
  }


  Future<ImageProvider> getImage(String imagePath,String imageName) async {
    if(imagePath.isNotEmpty && imageName.isNotEmpty){
      final ref = FirebaseStorage.instance.ref().child("$imagePath/$imageName");
      try {
        final url = await ref.getDownloadURL();
        return NetworkImage(url);
      } catch (error) {
        log("Error fetching image for ${"$imagePath/$imageName"}: $error");
        return const AssetImage('assets/icons/user-icon.png');
      }
    }else{
      return const AssetImage('assets/icons/user-icon.png');
    }
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => agencyPersone.length;

  @override
  int get selectedRowCount => 0;
}
