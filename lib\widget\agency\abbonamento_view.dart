import 'dart:async';
import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/functions/various.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/link.dart';
import 'dart:html' as html;
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/economics.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../utils/common_utils.dart';

class AbbonamentoView extends StatefulWidget {
  AbbonamentoView({
    super.key,
    this.updateViewCallback,
    required this.agencyUser,
  });

  final Function? updateViewCallback;
  Agency? agencyUser;

  @override
  State<AbbonamentoView> createState() => _AbbonamentoViewState();
}

class _AbbonamentoViewState extends State<AbbonamentoView> {
  List<String> subscription = ["Con Success Fee", "Senza Success Fee"];
  List<Map<String, dynamic>> subscriptions = [];
  List<String> tabList = ["Con Success Fee", "Senza Success Fee"];
  String errorMsg = "Seleziona un abbonamento";
  List<Map<String, dynamic>> finalSubscriptionsList = [];

  /// selected plan - please check with selectedTab index for with Success fee or without Success fee
  Map<String, dynamic> selectedPlan = {};

  ///selected card index
  int selectedCardIndex = -1;

  /// Success fee or without Success fee
  int selectedTab = 0;

  bool loading = false;
  bool _isFrozen = false;
  bool paymentError = false;

  @override
  void initState() {
    super.initState();
    fetchSubscriptions();
  }

  void fetchSubscriptions() async {
    final QuerySnapshot result =
        await FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_SUBSCRIPTION).get();

    final List<Map<String, dynamic>> fetchedData = result.docs.map((doc) {
      return {"id": doc.id, ...doc.data() as Map<String, dynamic>};
    }).toList();

    setState(() {
      subscriptions = fetchedData;
      finalSubscriptionsList =
          subscriptions.where((test) => (test['successFee'] != '0') && (test['planType'] != 'Start') && !(["oldGoldSubscription", "oldStartSubscription"].contains(test["id"]))).toList();
      finalSubscriptionsList.add({
        "id": "custom", 
        "price": 0, // needed to trigger "Contattaci" display in subscriptioncard
        "planType": "Custom", 
        "successFee": "0.2", 
        "servicePerYear": "servizi/mese su richiesta", 
        "services": finalSubscriptionsList[0]['services']
      });
      sortListAndFill();
    });
  }

  void sortListAndFill() {
    const customOrder = ["Start", "Premium", "Gold", "Supreme", "Custom"];

    finalSubscriptionsList.sort((a, b) {
      int indexA = customOrder.indexOf(a['planType']);
      int indexB = customOrder.indexOf(b['planType']);
      if (indexA == -1) indexA = customOrder.length;
      if (indexB == -1) indexB = customOrder.length;
      return indexA.compareTo(indexB);
    });
  }

  final GlobalKey _planKey = GlobalKey();

  final ScrollController _scrollController = ScrollController();

  void scrollToSection(GlobalKey key) {
    Scrollable.ensureVisible(
      key.currentContext!,
      duration: Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: SingleChildScrollView(
        controller: _scrollController,
        scrollDirection: Axis.vertical,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                NarFormLabelWidget(
                  label: 'Abbonamento Immagina',
                  fontSize: 19,
                  fontWeight: '700',
                  textColor: Colors.black,
                ),
              ],
            ),
            SizedBox(height: 26),
            _yourSubscription(),
            Padding(
              padding: const EdgeInsets.only(top: 50.0, bottom: 16),
              child: Divider(
                height: 1,
                color: Color(0xffCDCDCD),
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                NarFormLabelWidget(
                  label: 'Acquista un abbonamento',
                  fontSize: 19,
                  fontWeight: '700',
                  textColor: Colors.black,
                ),
              ],
            ),
            SizedBox(height: 10),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Column(
                  children: [
                    Image.asset(
                      height: 50,
                      "assets/logo_newarc_immagina.png",
                    ),
                    SizedBox(height: 26),
                    // _subscrionTabBar(),
                    // SizedBox(height: 4),
                    // NarLinkWidget(
                    //   onClick: () {
                    //     launchUrl(Uri.parse('https://www.newarc.it/guida-agenzie/#successfee'), mode: LaunchMode.externalApplication);
                    //   },
                    //   text: "Che cos'è la Success Fee?",
                    //   textColor: Color(0xff7C7C7C),
                    //   fontWeight: '700',
                    //   fontSize: 12,
                    // ),
                    _plans(),
                    SizedBox(height: 10),
                    Visibility(
                      visible: selectedPlan.isNotEmpty,
                      child: Column(
                        children: [
                          NarFormLabelWidget(
                            label:
                                // "${selectedPlan['planType']} ${(selectedPlan["successFee"] != null && selectedPlan["successFee"] != "0") ? "+ Success Fee" : ""}",
                                selectedPlan['planType'],
                            fontSize: 14,
                            fontWeight: '600',
                            textColor: Color(0xff878787),
                          ),
                          SizedBox(height: 20),
                        ],
                      ),
                    ),
                    Visibility(
                      visible: errorMsg.isNotEmpty,
                      child: Column(
                        children: [
                          NarFormLabelWidget(
                            label: widget.agencyUser?.subscriptionId == null ? errorMsg : "Hai già un abbonamento attivo",
                            fontSize: 14,
                            fontWeight: '600',
                            textColor: Color(0xff878787),
                          ),
                          SizedBox(height: 20),
                        ],
                      ),
                    ),
                    _purchasedButton(),
                    SizedBox(height: 40),
                  ],
                ),
              ],
            ),
            SizedBox(height: 10),
            Divider(),
            SizedBox(height: 10),
            pricesForSingleRequest(),
          ],
        ),
      ),
    );
  }

  MouseRegion _purchasedButton() {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () async {
          if (selectedCardIndex == -1) {
            errorMsg = "Seleziona un abbonamento";
            // setState(() {});
            return;
          }

          final hasSubscription =
              (widget.agencyUser?.subscriptionId ?? "").isNotEmpty;
          final expiryDate = DateTime.fromMillisecondsSinceEpoch(
              widget.agencyUser?.subscriptionEndDate ?? 0);
          final now = DateTime.now();
          final isSubscriptionActive = expiryDate.isAfter(now);
          final hasServicesLeft =
              widget.agencyUser?.subscriptionServiceCountLeft != 0;

          if (hasSubscription) {
            if (isSubscriptionActive) {
              if (hasServicesLeft) {
                _alreadyHaveSubscriptionDialog();
              } else {
                await _buySubsciptionDialog(
                  value: selectedPlan,
                  isWithFee: selectedTab == 0,
                );
              }
            } else {
              await _buySubsciptionDialog(
                value: selectedPlan,
                isWithFee: selectedTab == 0,
              );
            }
          } else {
            await _buySubsciptionDialog(
              value: selectedPlan,
              isWithFee: selectedTab == 0,
            );
          }
          setState(() {});
        },
        child: Container(
          height: 43,
          width: 150,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: selectedCardIndex == -1
                ? Theme.of(context).primaryColor.withOpacity(0.5)
                : Theme.of(context).primaryColor,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20.0),
            child: NarFormLabelWidget(
              label: 'Acquista',
              fontSize: 15,
              fontWeight: '600',
              textColor: AppColor.white,
            ),
          ),
        ),
      ),
    );
  }

  _successPaymentDialog() {
    return showDialog(
      context: context,
      builder: (context) {
        return Center(
          child: BaseNewarcPopup(
            noButton: true,
            title: "Acquista il tuo abbonamento",
            column: Container(
              width: 400,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Image.asset(
                    height: 50,
                    "assets/logo_newarc_immagina.png",
                  ),
                  SizedBox(height: 20),
                  NarFormLabelWidget(
                    label:
                        'Il tuo abbonamento Newarc Immagina\nstato attivato!',
                    fontSize: 20,
                    textAlign: TextAlign.center,
                    fontWeight: '600',
                    textColor: AppColor.black,
                  ),
                  SizedBox(height: 50),
                  BaseNewarcButton(
                    width: 142,
                    textColor: AppColor.white,
                    color: Theme.of(context).primaryColor,
                    buttonText: "Chiudi",
                    onPressed: () async {
                      Navigator.pop(context);
                    },
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // _payByBankTransferDialog({dynamic value, required Agency agency}) {
  //   return showDialog(
  //     context: context,
  //     builder: (context) {
  //       return Center(
  //         child: BaseNewarcPopup(
  //           noButton: true,
  //           title: "Acquista il tuo abbonamento",
  //           column: Container(
  //             // height: 305,
  //             width: 489,
  //             child: Column(
  //               mainAxisAlignment: MainAxisAlignment.center,
  //               mainAxisSize: MainAxisSize.min,
  //               children: [
  //                 SvgPicture.asset(
  //                   height: 50,
  //                   "assets/newarc_immagina.svg",
  //                 ),
  //                 SizedBox(height: 26),
  //                 Container(
  //                   padding: EdgeInsets.all(18),
  //                   decoration: BoxDecoration(
  //                     borderRadius: BorderRadius.circular(10),
  //                     color: AppColor.white,
  //                     border: Border.all(
  //                       width: 1,
  //                       color: Color(0xffDBDBDB),
  //                     ),
  //                   ),
  //                   child: Column(
  //                     children: [
  //                       NarFormLabelWidget(
  //                         label:
  //                             "${value['plan_type']} ${(value["success_fee"] != null && value["success_fee"] != "0") ? "+ Success Fee" : ""}",
  //                         fontSize: 17,
  //                         fontWeight: '700',
  //                         textColor: Theme.of(context).primaryColor,
  //                       ),
  //                       SizedBox(
  //                         height: 16,
  //                       ),
  //                       CustomTextFormField(
  //                         textAlign: TextAlign.center,
  //                         isHaveBorder: false,
  //                         isCenterLabel: true,
  //                         flex: 0,
  //                         suffixIcon: null,
  //                         fillColor: Color(0xffF5F5F5),
  //                         readOnly: true,
  //                         label: "Intestare bonifico a",
  //                         controller: TextEditingController(
  //                           text: "Newarc Srl",
  //                         ),
  //                       ),
  //                       SizedBox(
  //                         height: 10,
  //                       ),
  //                       CustomTextFormField(
  //                         textAlign: TextAlign.center,
  //                         suffixIcon: null,
  //                         isHaveBorder: false,
  //                         isCenterLabel: true,
  //                         flex: 0,
  //                         fillColor: Color(0xffF5F5F5),
  //                         readOnly: true,
  //                         label: "IBAN",
  //                         controller: TextEditingController(
  //                           text: "***************************",
  //                         ),
  //                       ),
  //                       SizedBox(
  //                         height: 10,
  //                       ),
  //                       CustomTextFormField(
  //                         textAlign: TextAlign.center,
  //                         suffixIcon: null,
  //                         isCenterLabel: true,
  //                         isHaveBorder: false,
  //                         flex: 0,
  //                         fillColor: Color(0xffF5F5F5),
  //                         readOnly: true,
  //                         label: "Somma da bonificare",
  //                         controller: TextEditingController(
  //                           text: "${
  //                               (value['price'] ?? 0) * 1.22 == 0
  //                               ? "" 
  //                               : ((value['price'] ?? 0) * 1.22).toStringAsFixed(2)
  //                             }€",
  //                         ),
  //                       ),
  //                       SizedBox(
  //                         height: 10,
  //                       ),
  //                       CustomTextFormField(
  //                         textAlign: TextAlign.center,
  //                         isCenterLabel: true,
  //                         isHaveBorder: false,
  //                         flex: 0,
  //                         suffixIcon: null,
  //                         fillColor: Color(0xffF5F5F5),
  //                         readOnly: true,
  //                         label: "Causale",
  //                         controller: TextEditingController(
  //                           text: "Acquisto Abbonamento Immagina",
  //                         ),
  //                       ),
  //                       SizedBox(
  //                         height: 20,
  //                       ),
  //                       NarFormLabelWidget(
  //                         label:
  //                             'Istituto bancario: Intesa San Paolo\nPiazza Massua 5, 10141, Torino',
  //                         fontSize: 12,
  //                         fontWeight: '600',
  //                         textAlign: TextAlign.center,
  //                         textColor: Color(0xff818181),
  //                       ),
  //                     ],
  //                   ),
  //                 ),
  //                 SizedBox(height: 48),
  //                 NarFormLabelWidget(
  //                   label:
  //                       'Le stesse informazioni sono state inviate via email.',
  //                   fontSize: 12,
  //                   fontWeight: '600',
  //                   textColor: Color(0xff818181),
  //                 ),
  //                 SizedBox(height: 10),
  //                 BaseNewarcButton(
  //                   width: 142,
  //                   textColor: AppColor.white,
  //                   color: Theme.of(context).primaryColor,
  //                   buttonText: "Chiudi",
  //                   onPressed: () async {
  //                     Navigator.pop(context);
  //                     _successPaymentDialog();
  //                   },
  //                 ),
  //               ],
  //             ),
  //           ),
  //         ),
  //       );
  //     },
  //   );
  // }

  Future<void> _alreadyHaveSubscriptionDialog() {
    return showDialog(
      context: context,
      builder: (context) {
        return Center(
          child: BaseNewarcPopup(
            noButton: false,
            onPressed: () {},
            buttonText: "Chiudi",
            title: "Acquista il tuo abbonamento",
            column: Container(
              width: 400,
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 30.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    NarFormLabelWidget(
                      label: 'Hai già un abbonamento attivo!',
                      fontSize: 20,
                      fontWeight: '600',
                      textColor: AppColor.black,
                    ),
                    SizedBox(
                      height: 13,
                    ),
                    NarFormLabelWidget(
                      label:
                          '<NAME_EMAIL> per modificare il tuo piano.',
                      fontSize: 16,
                      fontWeight: '600',
                      textAlign: TextAlign.center,
                      textColor: Color(0xff616161),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Future<void> _buySubsciptionDialog({dynamic value, bool isWithFee = true}) {
    return showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Stack(
              children: [
                Center(
                  child: BaseNewarcPopup(
                    noButton: true,
                    title: "Acquista il tuo abbonamento",
                    column: Container(
                      // height: 305,
                      width: 489,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Image.asset(
                            height: 50,
                            "assets/logo_newarc_immagina.png",
                          ),
                          SizedBox(height: 26),
                          Container(
                            margin: EdgeInsets.symmetric(horizontal: 10),
                            padding: EdgeInsets.all(18),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              color: Color(0xffEEF7FF),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Expanded(
                                  flex: 2,
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      NarFormLabelWidget(
                                        label: 'Abbonamento selezionato',
                                        fontSize: 13,
                                        fontWeight: '600',
                                        textColor: Color(0xff789BBA),
                                      ),
                                      SizedBox(height: 6),
                                      NarFormLabelWidget(
                                        label:
                                            // "${value['planType']} ${(value["successFee"] != null && value["successFee"] != "0") ? "+ Success Fee" : ""}",
                                            "${value['planType']}",
                                        fontSize: 15,
                                        fontWeight: '700',
                                        textColor: Theme.of(context).primaryColor,
                                      ),
                                      SizedBox(height: 3),
                                      NarFormLabelWidget(
                                        label: 'Durata: ${value['duration']}',
                                        fontSize: 12,
                                        fontWeight: '500',
                                        textColor: Theme.of(context).primaryColor,
                                      ),
                                    ],
                                  ),
                                ),
                                SizedBox(
                                  height: 60,
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 8.0),
                                    child: VerticalDivider(
                                      color: Color(0xff8BADCB),
                                    ),
                                  ),
                                ),
                                Expanded(
                                  flex: 1,
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      NarFormLabelWidget(
                                        label: 'Crediti disponibili',
                                        fontSize: 13,
                                        fontWeight: '600',
                                        textColor: Color(0xff789BBA),
                                      ),
                                      SizedBox(height: 6),
                                      NarFormLabelWidget(
                                        label: '${value['serviceCount']}',
                                        fontSize: 15,
                                        fontWeight: '600',
                                        textColor: Theme.of(context).primaryColor,
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(height: 30),
                          paymentError
                              ? Padding(
                                  padding: const EdgeInsets.only(bottom: 10.0),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text(
                                        "Qualcosa è andato storto con il pagamento, riprova.",
                                        textAlign: TextAlign.center,
                                        style: TextStyle(
                                            fontFamily: 'Raleway-500',
                                            fontSize: 14,
                                            color: Colors.black),
                                      ),
                                    ],
                                  ),
                                )
                              : SizedBox(),
                          loading
                              ? CircularProgressIndicator(
                                  color: Theme.of(context).primaryColor)
                              : Row(
                                  children: [
                                    Expanded(
                                      child: Padding(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 20),
                                        child: BaseNewarcButton(
                                          buttonText: "Paga ora",
                                          onPressed: () async {
                                            setState(() {
                                              loading = true;
                                              _isFrozen = true;
                                            });

                                            // open tab before async calls so to avoid iOS popup blocking
                                            final newTab = html.window.open('', '_blank');

                                            final docRef = FirebaseFirestore.instance
                                                .collection(appConfig.COLLECT_AGENCIES)
                                                .doc(widget.agencyUser!.id);

                                            var element = await docRef.get();
                                            final currentSubscriptionId = element.data()?['subscriptionId'];
                                            List<String> stripePriceIds = [value['stripePriceId']];

                                            bool wasPaymentDetected = false;
                                            bool isInitialSnapshot = true;

                                            try {
                                              var linkMap = await getStripeCheckoutLink(
                                                stripePriceIds,
                                                null,
                                                widget.agencyUser!.id!,
                                                origin: "abbonamento",
                                              );

                                              if (linkMap['link'] == null) {
                                                setState(() {
                                                  loading = false;
                                                  paymentError = true;
                                                  _isFrozen = false;
                                                });
                                                newTab.close();
                                                return;
                                              }

                                              // Assign the Stripe payment link to the tab
                                              newTab.location.href = linkMap['link']!;

                                              // Check if the user closed the tab without completing payment
                                              Future.doWhile(() async {
                                                await Future.delayed(Duration(seconds: 1));
                                                return !wasPaymentDetected && newTab.closed == false;
                                              }).then((_) {
                                                if (!wasPaymentDetected) {
                                                  setState(() {
                                                    _isFrozen = false;
                                                    loading = false;
                                                    paymentError = true;
                                                  });
                                                }
                                              });

                                              // Firestore listener for successful payment
                                              Future.delayed(Duration(seconds: 1), () {
                                                StreamSubscription? subscription;
                                                subscription = docRef
                                                    .snapshots()
                                                    .listen((event) async {
                                                  if (isInitialSnapshot) {
                                                    isInitialSnapshot = false;
                                                    return;
                                                  }

                                                  if (event.data()?['subscriptionId'] != currentSubscriptionId) {
                                                    sendEmail(
                                                      templateId: CommonUtils.subscriptionActivatedEmailTemplateId,
                                                      agency: widget.agencyUser!,
                                                      subject: CommonUtils.subscriptionActivatedEmailSubject,
                                                    );
                                                    // send email to masters and amministrazione
                                                    if (!wasPaymentDetected) {  
                                                      Map<String, dynamic> workEmailVariables = {
                                                        'agencyname': widget.agencyUser!.name,
                                                        'subscriptionname': "${value['planType']} ${(value["successFee"] != null && value["successFee"] != "0") ? "+ Success Fee" : ""}",
                                                      };
                                                      final masterUsers = await FirebaseFirestore.instance
                                                        .collection(appConfig.COLLECT_USERS)
                                                        .where('type', isEqualTo: 'newarc')
                                                        .where('role', isEqualTo: 'master')
                                                        .where('isActive', isEqualTo: true)
                                                        .get();
                                                      for (var userDoc in masterUsers.docs) {
                                                        final userData = userDoc.data();
                                                        if (userData['email'] != null) {
                                                          sendEmail(
                                                            agency: widget.agencyUser!,
                                                            templateId: CommonUtils.subscriptionActivatedForWorkSideEmailTemplateId,
                                                            subject: CommonUtils.subscriptionActivatedForWorkSideEmailSubject,
                                                            variables: workEmailVariables,
                                                            recipientEmail: userData['email'],
                                                            recipientName: userData['firstName'] != null
                                                                ? "${userData['firstName']} ${userData['lastName'] ?? ''}"
                                                                : "Master",
                                                          );
                                                        }
                                                      }
                                                      if (appConfig.isProduction) {
                                                        sendEmail(
                                                          agency: widget.agencyUser!,
                                                          templateId: CommonUtils.subscriptionActivatedForWorkSideEmailTemplateId,
                                                          subject: CommonUtils.subscriptionActivatedForWorkSideEmailSubject,
                                                          variables: workEmailVariables,
                                                          recipientEmail: "<EMAIL>",
                                                          recipientName: "Amministrazione",
                                                        );
                                                      }
                                                    }

                                                    wasPaymentDetected = true;

                                                    setState(() {
                                                      loading = false;
                                                      _isFrozen = false;
                                                      paymentError = false;
                                                    });

                                                    widget.agencyUser =
                                                        Agency.fromDocument(event.data()!, event.id);

                                                    subscription?.cancel();

                                                    Navigator.of(context).pop();
                                                    _successPaymentDialog();
                                                  }
                                                }, 
                                                onError: (error) {
                                                  log("-------ERROR ${error.toString()}");

                                                  setState(() {
                                                    loading = false;
                                                    _isFrozen = false;
                                                    paymentError = false;
                                                  });

                                                  subscription?.cancel();
                                                });
                                              });
                                            } catch (e, stackTrace) {
                                              log("-------CATCH ERROR ${e.toString()}");
                                              log("-------CATCH stackTrace ${stackTrace.toString()}");
                                              setState(() {
                                                loading = false;
                                                paymentError = true;
                                                _isFrozen = false;
                                              });
                                              newTab.close();
                                            }
                                          }
                                        ),
                                      ),
                                    ),
                                    // Expanded(
                                    //   child: Padding(
                                    //     padding: const EdgeInsets.symmetric(
                                    //         horizontal: 20),
                                    //     child: BaseNewarcButton(
                                    //       textColor: Theme.of(context).primaryColor,
                                    //       color: AppColor.white,
                                    //       borderColor: Theme.of(context).primaryColor,
                                    //       buttonText: "Paga con bonifico",
                                    //       onPressed: () async{
                                    //         setState(() {
                                    //           loading = true;
                                    //         });
                                    //         try {
                                    //           final collectionRef = FirebaseFirestore.instance.collection(appConfig.COLLECT_AGENCIES);
                                    //           // Update the field
                                    //           await collectionRef.doc(widget.agencyUser?.id).update({
                                    //             'subscriptionPaymentDate': null,
                                    //             'subscriptionId': value["id"],
                                    //             'subscriptionEndDate': DateTime.now().add(Duration(days: 365)).millisecondsSinceEpoch,
                                    //             'subscriptionServiceCount': value["service_count"],
                                    //             'subscriptionServiceCountLeft': value["service_count"],
                                    //             'subscriptionStartDate': DateTime.now().millisecondsSinceEpoch,
                                    //           });
                                    //           // bank transfer and subscription activate send notification mail
                                    //           Map<String,dynamic> emailVariable = {
                                    //             'subscriptionname':value["plan_type"],
                                    //             'subscriptionprice':"${
                                    //               (value['price'] ?? 0) * 1.22 == 0
                                    //               ? "" 
                                    //               : ((value['price'] ?? 0) * 1.22).toStringAsFixed(2)
                                    //             }€",
                                    //           };
                                    //           sendEmail(templateId: CommonUtils.bankTransferEmailTemplateId, agency: widget.agencyUser!, subject: CommonUtils.bankTransferEmailSubject,variables: emailVariable);
                                    //           sendEmail(templateId: CommonUtils.subscriptionActivatedEmailTemplateId, agency: widget.agencyUser!, subject: CommonUtils.subscriptionActivatedEmailSubject);
                                    //           // send email to masters and amministrazione
                                    //           Map<String, dynamic> workEmailVariables = {
                                    //             'agencyname': widget.agencyUser!.name,
                                    //             'subscriptionname': "${value['plan_type']} ${(value["success_fee"] != null && value["success_fee"] != "0") ? "+ Success Fee" : ""}",
                                    //           };
                                    //           final masterUsers = await FirebaseFirestore.instance
                                    //             .collection(appConfig.COLLECT_USERS)
                                    //             .where('type', isEqualTo: 'newarc')
                                    //             .where('role', isEqualTo: 'master')
                                    //             .where('isActive', isEqualTo: true)
                                    //             .get();
                                    //           for (var userDoc in masterUsers.docs) {
                                    //             final userData = userDoc.data();
                                    //             if (userData['email'] != null) {
                                    //               sendEmail(
                                    //                 agency: widget.agencyUser!,
                                    //                 templateId: CommonUtils.subscriptionActivatedForWorkSideEmailTemplateId,
                                    //                 subject: CommonUtils.subscriptionActivatedEmailSubject,
                                    //                 variables: workEmailVariables,
                                    //                 recipientEmail: userData['email'],
                                    //                 recipientName: userData['firstName'] != null
                                    //                     ? "${userData['firstName']} ${userData['lastName'] ?? ''}"
                                    //                     : "Master",
                                    //               );
                                    //             }
                                    //           }
                                    //           sendEmail(
                                    //             agency: widget.agencyUser!,
                                    //             templateId: CommonUtils.subscriptionActivatedForWorkSideEmailTemplateId,
                                    //             subject: CommonUtils.subscriptionActivatedEmailSubject,
                                    //             variables: workEmailVariables,
                                    //             recipientEmail: "<EMAIL>",
                                    //             recipientName: "Amministrazione",
                                    //           );
                                              
                                    //           Navigator.pop(context);
                                    //           Future.delayed(Duration(milliseconds: 100));
                                    //           _payByBankTransferDialog(
                                    //               value: value,
                                    //               agency: widget.agencyUser!);
                                    //           log("Document updated subscription count successfully!");
                                    //         } catch (e) {
                                    //           log("Error updating subscription count document: $e");
                                    //           setState(() {
                                    //             loading = false;
                                    //           });
                                    //         }finally{
                                    //           setState(() {
                                    //             loading = false;
                                    //           });
                                    //         }

                                    //       },
                                    //     ),
                                    //   ),
                                    // ),
                                  ],
                                ),
                          SizedBox(height: 22),
                          value['successFee'] != '0' ?
                          NarFormLabelWidget(
                            label:
                                'La Success Fee sarà fatturata dopo la chiusura di ogni operazione.',
                            fontSize: 12,
                            fontWeight: '600',
                            textColor: Color(0xff818181),
                          ) : SizedBox(),
                        ],
                      ),
                    ),
                  ),
                ),
                if (_isFrozen)
                  Positioned.fill(
                    child: Container(
                      color: Colors.black54, // Semi-transparent overlay
                    ),
                  ),
              ],
            );
          },
        );
      },
    );
  }

  Widget _plans() {
    return SizedBox(
      key: _planKey,
      height: 455,
      child: Padding(
        padding: const EdgeInsets.all(30.0),
        child: ListView.builder(
          shrinkWrap: true,
          scrollDirection: Axis.horizontal,
          itemCount: finalSubscriptionsList.length,
          itemBuilder: (context, index) {
            final subscription = finalSubscriptionsList[index];
            return SubscriptionCard(
              agencyUser: widget.agencyUser,
              selectedTab: selectedTab,
              subscription: subscription,
              isSelected: selectedCardIndex == index,
              onTap: widget.agencyUser?.subscriptionId == null
              ?  () {
                  errorMsg = "";
                  selectedPlan = subscription;
                  selectedCardIndex = index;
                  setState(() {});
                }
              : (){},
            );
          },
        ),
      ),
    );
  }

  Container _subscrionTabBar() {
    return Container(
      padding: EdgeInsets.all(2),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Color(0xffF2F2F2),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: tabList.map((element) {
          int index = tabList.indexOf(element);
          return MouseRegion(
            cursor: SystemMouseCursors.click,
            child: GestureDetector(
              onTap: () {
                selectedTab = index;
                selectedCardIndex = -1;
                selectedPlan = {};
                if (selectedTab == 0) {
                  finalSubscriptionsList = subscriptions
                      .where((test) => test['successFee'] != '0')
                      .toList();
                } else {
                  finalSubscriptionsList = subscriptions
                      .where((test) => test['successFee'] == '0')
                      .toList();
                }
                sortListAndFill();
                setState(() {});
              },
              child: Container(
                width: 170,
                height: 44,
                decoration: BoxDecoration(
                  color:
                      selectedTab == index ? AppColor.white : Color(0xffF2F2F2),
                  border: selectedTab == index
                      ? Border.all(
                          width: 1,
                          color: Color(0xffDEDEDE),
                        )
                      : null,
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Center(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      NarFormLabelWidget(
                        label: element.split(' ').first,
                        fontSize: 15,
                        fontWeight: '700',
                        textColor: selectedTab == index
                            ? Theme.of(context).primaryColor
                            : Color(0xff7C7C7C),
                      ),
                      NarFormLabelWidget(
                        label: ' ${element.split(' ').sublist(1).join(' ')}',
                        fontSize: 15,
                        fontWeight: '700_italic',
                        textColor: selectedTab == index
                            ? Theme.of(context).primaryColor
                            : Color(0xff7C7C7C),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget pricesForSingleRequest(){
    final pricingDataForSingleRequest = ImmaginaProjectEconomics.generatePricingData();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
      NarFormLabelWidget(
        label: 'Prezzi richiesta singola',
        fontSize: 19,
        fontWeight: '700',
        textColor: Colors.black,
      ),
      SizedBox(height: 15,),
        Center(
          child: SizedBox(
            width: 800,
            child: ListView.builder(
              itemCount: ImmaginaProjectEconomics.generatePricingData().length,
              shrinkWrap: true,
              itemBuilder: (context, index) {
                return PricingCard(
                  title: pricingDataForSingleRequest[index]["title"],
                  prices: pricingDataForSingleRequest[index]["prices"],
                  extraPrices: pricingDataForSingleRequest[index]["extraPrices"],
                );
              },
            ),
          ),
        ),
    ],);
  }

  StreamBuilder _yourSubscription() {
    return StreamBuilder<DocumentSnapshot<Map<String, dynamic>>>(
      stream: FirebaseFirestore.instance
          .collection(appConfig.COLLECT_AGENCIES)
          .doc(widget.agencyUser?.id)
          .snapshots(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return SizedBox();
        }

        if (snapshot.hasError) {
          return SizedBox();
        }

        if (!snapshot.hasData || !snapshot.data!.exists) {
          return SizedBox();
        }

        final agencyData = snapshot.data!.data();
        final subscriptionServiceCountLeft =
            agencyData?['subscriptionServiceCountLeft'] ?? 0;
        final subscriptionServiceCount =
            agencyData?['subscriptionServiceCount'] ?? 0;
        DateTime expiryDate = DateTime.fromMillisecondsSinceEpoch(
            agencyData?['subscriptionEndDate'] ?? 0);
        DateTime now = DateTime.now();
        bool isExpired = now.isAfter(expiryDate);
        bool isServiceOver = subscriptionServiceCountLeft <= 0;
        bool hasHadSubscription = agencyData?['subscriptionId'] != null;
        return Center(
          child: Container(
            width: 460,
            padding: EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: AppColor.white,
              borderRadius: BorderRadius.circular(15),
              border: Border.all(
                width: 1,
                color: Color(0xffCFCFCF),
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(top: 10.0),
                      child: NarFormLabelWidget(
                        label: 'Il tuo abbonamento',
                        fontSize: 17,
                        fontWeight: '700',
                        textColor: Colors.black,
                      ),
                    ),
                    Image.asset(
                      height: 50,
                      "assets/logo_newarc_immagina.png",
                    ),
                  ],
                ),
                if (!hasHadSubscription) ...[
                  SizedBox(
                    height: 30,
                  ),
                  Column(
                    children: [
                      NarFormLabelWidget(
                        label: 'Nessun abbonamento attivo',
                        fontSize: 14,
                        fontWeight: '600',
                        textColor: Color(0xff7A7979),
                      ),
                      SizedBox(
                        height: 12,
                      ),
                      MouseRegion(
                        cursor: SystemMouseCursors.click,
                        child: GestureDetector(
                          onTap: () {
                            scrollToSection(_planKey);
                          },
                          child: Container(
                            height: 43,
                            width: 150,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              color: Theme.of(context).primaryColor,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 20.0),
                              child: NarFormLabelWidget(
                                label: 'Acquista',
                                fontSize: 15,
                                fontWeight: '600',
                                textColor: AppColor.white,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 30,
                  ),
                ] else ...[
                  SizedBox(
                    height: 30,
                  ),
                  Container(
                    margin: EdgeInsets.symmetric(horizontal: 10),
                    padding: EdgeInsets.all(18),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: Color(0xffEEF7FF),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          flex: 2,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              NarFormLabelWidget(
                                label: 'Abbonamento acquistato',
                                fontSize: 13,
                                fontWeight: '600',
                                textColor: Color(0xff789BBA),
                              ),
                              SizedBox(
                                height: 6,
                              ),
                              FutureBuilder<
                                  DocumentSnapshot<Map<String, dynamic>>>(
                                future: FirebaseFirestore.instance
                                    .collection(appConfig.COLLECT_IMMAGINA_SUBSCRIPTION)
                                    .doc(agencyData?['subscriptionId'])
                                    .get(),
                                builder: (context, futureSnapshot) {
                                  if (futureSnapshot.connectionState ==
                                      ConnectionState.waiting) {
                                    return SizedBox();
                                  }

                                  if (futureSnapshot.hasError ||
                                      !futureSnapshot.hasData ||
                                      !futureSnapshot.data!.exists) {
                                    return SizedBox();
                                  }

                                  final subscriptionData = futureSnapshot.data!.data();
                                  String planName = subscriptionData?['planType'] ?? 'Unknown Plan';
                                  // bool isSuccessFee = subscriptionData?['successFee'] != '0';
                                  // String planType = isSuccessFee ? ' + Success Fee' : '';
                                  return NarFormLabelWidget(
                                    label: planName, //  + planType,
                                    fontSize: 15,
                                    fontWeight: '700',
                                    textColor: Theme.of(context).primaryColor,
                                  );
                                },
                              ),
                              SizedBox(
                                height: 3,
                              ),
                              NarFormLabelWidget(
                                label:
                                    'Valido fino al: ${DateFormat('dd/MM/yyyy').format(expiryDate)}',
                                fontSize: 12,
                                fontWeight: '500',
                                textColor: isExpired
                                    ? Color(0xffE82525)
                                    : Theme.of(context).primaryColor,
                              ),
                            ],
                          ),
                        ),
                        SizedBox(
                          height: 60,
                          child: Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 8.0),
                            child: VerticalDivider(
                              color: Color(0xff8BADCB),
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 1,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              NarFormLabelWidget(
                                label: 'Crediti disponibili',
                                fontSize: 13,
                                fontWeight: '600',
                                textColor: isServiceOver
                                    ? Color(0xffE82525)
                                    : Color(0xff789BBA),
                              ),
                              SizedBox(
                                height: 6,
                              ),
                              NarFormLabelWidget(
                                label:
                                    '${subscriptionServiceCountLeft}/${subscriptionServiceCount}',
                                fontSize: 15,
                                fontWeight: '600',
                                textColor: isServiceOver
                                    ? Color(0xffE82525)
                                    : Theme.of(context).primaryColor,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(
                    height: 15,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(top: 15.0),
                        child: MouseRegion(
                          cursor: SystemMouseCursors.click,
                          child: GestureDetector(
                            onTap: () {
                              // scrollToSection(_planKey);
                              String url = appConfig.isProduction 
                                ? "https://billing.stripe.com/p/login/5kAg1T0ekex60iAeUU"
                                : "https://billing.stripe.com/p/login/test_28E14m3Xtd9FasS5ktfjG00";
                              // redirect to url in new tab
                              html.window.open(url, "");
                            },
                            child: Container(
                              height: 43,
                              // width: 150,
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                color: Theme.of(context).primaryColor,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 20.0),
                                child: NarFormLabelWidget(
                                  label: 'Gestisci abbonamento',
                                  fontSize: 15,
                                  fontWeight: '600',
                                  textColor: AppColor.white,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }
}

class PricingCard extends StatelessWidget {
  final String title;
  final List<Map<String, String>> prices;
  final List<Map<String, String>> extraPrices;

  const PricingCard({
    required this.title,
    required this.prices,
    required this.extraPrices,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 20),
      decoration: BoxDecoration(
        border: Border.all(color: Color(0xffCFCFCF)),
        borderRadius: BorderRadius.circular(15)
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            NarFormLabelWidget(
              label: title,
              fontSize: 19,
              fontWeight: '700',
              textColor: Colors.black,
            ),
            SizedBox(height: 12),
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildPriceList(prices),
                Container(height: 90,color: Color(0xFFDBDBDB),width: 1,),
                _buildPriceList(extraPrices),
              ],
            ),
            SizedBox(height: 10),
            Center(
              child: NarFormLabelWidget(
                label: "Immobili oltre i 299mq - preventivo personalizzato",
                fontSize: 13,
                fontWeight: '500',
                textColor: Color(0xFF616161),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPriceList(List<Map<String, String>> priceList) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: priceList.map((item) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 8.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              NarFormLabelWidget(
                label: "${item['label'] ?? ''} - ",
                fontSize: 13,
                fontWeight: '700',
                textColor: AppColor.black,
              ),
              NarFormLabelWidget(
                label: item['price'] ?? '',
                fontSize: 13,
                fontWeight: '500',
                textColor: AppColor.black,
              ),
            ],
          ),
        );
      }).toList(),
    );
  }
}

class SubscriptionCard extends StatefulWidget {
  final int selectedTab;
  final bool isSelected;
  final VoidCallback onTap;
  final Map<String, dynamic> subscription;
  final Agency? agencyUser;

  const SubscriptionCard({
    Key? key,
    required this.isSelected,
    required this.onTap,
    required this.subscription,
    required this.selectedTab,
    this.agencyUser,
  }) : super(key: key);

  @override
  _SubscriptionCardState createState() => _SubscriptionCardState();
}

class _SubscriptionCardState extends State<SubscriptionCard> {
  bool isHovered = false;
  final NumberFormat formatCurrency = NumberFormat.currency(
    locale: 'de_DE',
    symbol: '€',
    decimalDigits: 0,
  );

  @override
  Widget build(BuildContext context) {
    List<dynamic> services = widget.subscription['services'];
    final double price =
        double.tryParse(widget.subscription['price'].toString()) ?? 0.0;
    final double serviceCount =
        double.tryParse(widget.subscription['serviceCount'].toString()) ?? 1.0;
    final double successFee =
        double.tryParse(widget.subscription['successFee'].toString()) ?? 0.0;

    final double pricePerService = price / serviceCount;

    final String formattedPricePerService =
        formatCurrency.format(pricePerService);
    final String successFeeLabel = '$successFee% success fee';

    bool hasActiveSubscription =
        widget.subscription['id'] == widget.agencyUser?.subscriptionId;
    bool canPurchase = false;

    if (hasActiveSubscription &&
        (widget.agencyUser?.subscriptionServiceCountLeft ?? 0) <= 0) {
      canPurchase = true;
    } else if (widget.agencyUser?.subscriptionEndDate == null ||
        DateTime.parse(DateTime.fromMillisecondsSinceEpoch(
                    widget.agencyUser!.subscriptionEndDate ?? 0)
                .toString())
            .isBefore(DateTime.now())) {
      canPurchase = true;
    } else if (hasActiveSubscription &&
        (widget.agencyUser?.subscriptionServiceCountLeft ?? 0) > 0) {
      canPurchase = false;
    } else {
      canPurchase = true;
    }

    if (widget.subscription['id'] == 'custom') {
      canPurchase = false;
    }

    // return IgnorePointer(
    //   ignoring: !canPurchase,
    //   child: Opacity(
    return Opacity(
        opacity: 1,
        child: MouseRegion(
          onEnter: widget.subscription['id'] == 'custom' ? (_) {}
          : (_) {
            setState(() {
              isHovered = true;
            });
          },
          onExit: 
          widget.subscription['id'] == 'custom' ? (_) {}
          : (_) {
            setState(() {
              isHovered = false;
            });
          },
          child: InkWell(
            highlightColor: Colors.transparent,
            splashColor: Colors.transparent,
            splashFactory: NoSplash.splashFactory,
            hoverColor: Colors.transparent,
            onTap: canPurchase ? widget.onTap : () {},
            mouseCursor: canPurchase ? SystemMouseCursors.click : SystemMouseCursors.basic,
            child: Align(
              alignment: Alignment.center,
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                margin: const EdgeInsets.symmetric(horizontal: 8),
                width: isHovered ? 260 : 250,
                height: isHovered ? 405 : 395,
                padding: EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(18),
                  border: Border.all(
                    color: widget.isSelected
                        ? Theme.of(context).primaryColor
                        : Color(0xffCFCFCF),
                    width: widget.isSelected ? 3 : 1,
                  ),
                  boxShadow: isHovered
                      ? [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 15,
                            offset: const Offset(0, 15),
                          )
                        ]
                      : [],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    NarFormLabelWidget(
                      label: widget.subscription['planType'] ?? "",
                      fontSize: 25,
                      fontWeight: '700',
                      textColor: Theme.of(context).primaryColor,
                    ),
                    NarFormLabelWidget(
                      label: widget.subscription['servicePerYear'] ?? "",
                      fontSize: 14,
                      fontWeight: '800',
                      textColor: AppColor.black,
                    ),
                    SizedBox(height: 5,),
                    NarFormLabelWidget(
                      label: "Case fino a ${gSFUpperLimit - 1}mq",
                      fontSize: 11,
                      fontWeight: '600',
                      textColor: AppColor.black,
                    ),
                    SizedBox(
                      height: 17,
                    ),
                    ListView.separated(
                      padding: EdgeInsets.zero,
                      shrinkWrap: true,
                      physics: ScrollPhysics(),
                      itemCount: services.length,
                      itemBuilder: (context, i) {
                        return Row(
                          children: [
                            SvgPicture.asset(
                              "assets/icons/true.svg",
                              height: 10,
                              color: Theme.of(context).primaryColor,
                            ),
                            SizedBox(
                              width: 8,
                            ),
                            NarFormLabelWidget(
                              label: services[i],
                              fontSize: 12,
                              fontWeight: '500',
                              textColor: Color(0xff575757),
                            ),
                          ],
                        );
                      },
                      separatorBuilder: (BuildContext context, int index) {
                        return SizedBox(
                          height: 8,
                        );
                      },
                    ),
                    SizedBox(
                      height: 2,
                    ),
                    Spacer(),
                    NarFormLabelWidget(
                      label:
                        widget.subscription['price'] == 0 ? "Contattaci!" :
                          formatCurrency.format(widget.subscription['price']) + " + IVA/" + (widget.subscription['monthsBillingFrequency'] == 12 ? "anno" : widget.subscription['monthsBillingFrequency'] == 2 ? "2 mesi" : "mese"),
                      fontSize: 20,
                      fontWeight: '700',
                      textColor: AppColor.black,
                    ),
                    SizedBox(
                      height: 2,
                    ),
                    if (widget.selectedTab == 0 &&  widget.subscription['price'] != 0) ...[
                      NarFormLabelWidget(
                        label:
                            '+ ${widget.subscription['successFee']}% success fee',
                        fontSize: 12,
                        fontWeight: '500',
                        textColor: AppColor.black,
                      ),
                    ] else if (widget.subscription['price'] == 0) ...[
                      NarFormLabelWidget(
                        label: '<EMAIL>',
                        fontSize: 12,
                        fontWeight: '500',
                        textColor: AppColor.black,
                      ),
                    ] else ...[
                      NarFormLabelWidget(
                        label: '',
                        fontSize: 12,
                        fontWeight: '500',
                        textColor: AppColor.black,
                      ),
                    ],
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8.0),
                      child: Divider(
                        height: 1,
                        color: widget.subscription['price'] != 0 ? Color(0xffDBDBDB) : Colors.transparent,
                      ),
                    ),
                    if (widget.selectedTab == 0 &&  widget.subscription['price'] != 0) ...[
                      NarFormLabelWidget(
                        label:
                            '$formattedPricePerService/servizio + $successFeeLabel',
                        fontSize: 12,
                        fontWeight: '600',
                        textColor: Color(0xff9A9A9A),
                      ),
                    ] else if (widget.subscription['price'] == 0) ...[
                      NarFormLabelWidget(
                        label: '',
                        fontSize: 12,
                        fontWeight: '600',
                        textColor: Color(0xff9A9A9A),
                      ),
                    ] else ...[
                      NarFormLabelWidget(
                        label:
                            '${widget.subscription['price'] / widget.subscription['serviceCount']}€/servizio',
                        fontSize: 12,
                        fontWeight: '600',
                        textColor: Color(0xff9A9A9A),
                      ),
                    ]
                  ],
                ),
              ),
            ),
          ),
        ),
      // ),
    );
  }
}
