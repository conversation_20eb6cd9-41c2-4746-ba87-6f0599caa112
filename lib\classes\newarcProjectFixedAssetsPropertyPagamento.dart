import 'dart:developer';

class NewarcProjectFixedAssetsPropertyPagamento{
  String? newarcProjectFixedAssetsPropertyCategoryId;
  List<Rate>? rate;
  double? total;
  double? totalIVA;


  //--for UI
  String? categoryName;


  Map<String, Object?> toMap() {
    categoryName = categoryName;
    return {
      'newarcProjectFixedAssetsPropertyCategoryId': newarcProjectFixedAssetsPropertyCategoryId,
      'rate': rate?.map((val) => val.toMap()).toList(),
      'total': total,
      'totalIVA': totalIVA,
    };
  }

  NewarcProjectFixedAssetsPropertyPagamento.empty() {
    this.newarcProjectFixedAssetsPropertyCategoryId = '';
    this.rate = [];
    this.total = 0.0;
    this.totalIVA = 0.0;
    this.categoryName = '';

  }

  NewarcProjectFixedAssetsPropertyPagamento.fromDocument(Map<String, dynamic> data, String id) {
    categoryName = categoryName;
    try {
      this.newarcProjectFixedAssetsPropertyCategoryId = data['newarcProjectFixedAssetsPropertyCategoryId'];
      this.total = data['total'];
      this.totalIVA = data['totalIVA'];
      this.rate = [];
      if (data['rate'] != null) {
        for (var i = 0; i < data['rate'].length; i++) {
          this.rate?.add(Rate.fromDocument(data['rate'][i],''));
        }
      }
    } catch (e, s) {
      print({ 'NewarcProjectFixedAssetsPercentage Class Error ------->', e, s});
    }
  }
}


class Rate{
  String? uniqueId;
  String? newarcProjectFixedAssetsPercentageId;
  int? index;
  double? rate;
  double? paidAmount;
  int? ivaPercentage;
  double? ivaAmount;
  Future<List>? fetchPercentageFuture;
  bool? isPaid;
  int? paidDate;
  Map? invoicePath;

  // for UI
  int? percentage;
  List? invoiceImages;
  bool? isInvoiceUploaded;




  Map<String, Object?> toMap() {
    percentage = percentage;
    invoiceImages = invoiceImages;
    isInvoiceUploaded = isInvoiceUploaded;
    return {
      'newarcProjectFixedAssetsPercentageId': newarcProjectFixedAssetsPercentageId,
      'uniqueId': uniqueId,
      'index': index,
      'rate': rate,
      'ivaPercentage': ivaPercentage,
      'ivaAmount': ivaAmount,
      'isPaid': isPaid,
      'paidDate': paidDate,
      'invoicePath': invoicePath,
      'paidAmount': paidAmount,
    };
  }

  Rate.empty() {
    this.newarcProjectFixedAssetsPercentageId = '';
    this.index = 0;
    this.uniqueId = "";
    this.rate = 0.0;
    this.ivaPercentage = 0;
    this.ivaAmount = 0;
    this.isPaid = false;
    this.paidDate = null;
    this.percentage = 0;
    this.invoicePath = null;
    this.paidAmount = 0;
    this.invoiceImages = [];
    isInvoiceUploaded = false;
  }

  Rate.fromDocument(Map<String, dynamic> data, String id) {
    percentage = percentage;
    try {
      this.newarcProjectFixedAssetsPercentageId = data['newarcProjectFixedAssetsPercentageId'];
      this.uniqueId = data['uniqueId'];
      this.index = data['index'];
      this.rate = data['rate'];
      this.ivaPercentage = data['ivaPercentage'];
      this.isPaid = data['isPaid'];
      this.paidDate = data['paidDate'];
      this.ivaAmount = data['ivaAmount'];
      this.invoicePath = data['invoicePath'];
      this.paidAmount = data['paidAmount'];
      invoiceImages = (data['invoicePath'] != null && (data['invoicePath'] as Map).isNotEmpty) ? [data['invoicePath']["filename"]] : [];
      isInvoiceUploaded = (data['invoicePath'] != null && (data['invoicePath'] as Map).isNotEmpty) ? true : false;
    } catch (e, s) {
      print({ 'Rate Class Error ------->', e, s});
    }
  }
}
