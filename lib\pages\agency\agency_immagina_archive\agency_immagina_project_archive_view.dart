import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/classes/immaginaProject.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/utils/common_utils.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/filter.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/newarc_data_table.dart';
import 'package:newarc_platform/widget/UI/select-box.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'agency_immagina_project_archive_controller.dart';
import 'agency_immagina_project_archive_request_source.dart';

class AgencyImmaginaProjectArchiveView extends StatefulWidget {
  AgencyImmaginaProjectArchiveView({
    super.key,
    this.updateViewCallback,
    required this.agencyUser,
    this.projectArguments = const {},
  });

  final Function? updateViewCallback;
  final AgencyUser agencyUser;
  final Map? projectArguments;

  @override
  State<AgencyImmaginaProjectArchiveView> createState() => _AgencyImmaginaProjectArchiveViewState();
}

class _AgencyImmaginaProjectArchiveViewState extends State<AgencyImmaginaProjectArchiveView> {
  final controller = Get.put<AgencyImmaginaProjectArchiveController>(AgencyImmaginaProjectArchiveController());

  @override
  void initState() {
    initialFetch(force: true);
    super.initState();
  }


  void reloadAfterPop({bool force = false}) {
    controller.projects = [];
    controller.documentList = [];
    controller.filters = [];
    controller.cacheFireStore = [];
    controller.totalRecords = 0;
    controller.currentlyShowing = '';
    controller.recordsPerPage = 12;
    controller.pageCounter = 1;
    controller.totalPages = 0;
    controller.disablePreviousButton = true;
    controller.disableNextButton = false;
    controller.statusFilterController.clear();
    controller.cacheFireStore.clear();
    initialFetch(force: force);
  }

  Widget dataTablePagination() {
    return Visibility(
      visible: controller.currentlyShowing.isNotEmpty,
      child: Padding(
          padding: EdgeInsets.symmetric(vertical: 10),
          child: IconTheme.merge(
            data: const IconThemeData(opacity: 0.54),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  "${controller.currentlyShowing} di  ${controller.totalRecords.toString()}",
                  style: TextStyle(
                    fontFamily: '',
                    fontSize: 12.0,
                    color: Colors.black.withOpacity(0.54),
                  ),
                ),
                SizedBox(width: 32.0),
                IconButton(
                  icon: const Icon(Icons.chevron_left),
                  padding: EdgeInsets.zero,
                  onPressed: () {
                    if (controller.disablePreviousButton == true) return;
                    if (controller.loadingProperties == true) return;
                    fetchPrevProperties();
                  },
                ),
                SizedBox(width: 24.0),
                IconButton(
                  padding: EdgeInsets.zero,
                  icon: const Icon(Icons.chevron_right),
                  onPressed: () {
                    if (controller.disableNextButton == true) return;
                    if (controller.loadingProperties == true) return;

                    fetchNextProperties();
                  },
                ),
                SizedBox(width: 14.0),
              ],
            ),
          )),
    );
  }

  _initialFetch({bool force = false}) {
    initialFetch(force: force);
  }

  Future<void> initialFetch({bool force = false,bool reloadAll = false}) async {
    if (controller.projects.isNotEmpty && !force) return;

    controller.pageCounter = 1;

    setState(() {
      controller.loadingProperties = true;
      controller.projects = [];
    });

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshotCounter;
      Query<Map<String, dynamic>> collectionSnapshotCounterQuery;

      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      collectionSnapshotQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS);
      collectionSnapshotCounterQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS);

      if (controller.filters.length > 0) {
        for (var i = 0; i < controller.filters.length; i++) {
          if (controller.filters[i]['search'] == 'equal') {
            collectionSnapshotQuery = collectionSnapshotQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
            collectionSnapshotCounterQuery = collectionSnapshotCounterQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
          }
        }
      }

      if(reloadAll){
        collectionSnapshot = await collectionSnapshotQuery
            .where('agencyId', isEqualTo: widget.agencyUser.agencyId)
            .where("isArchived", isEqualTo: false)
            .where("isAgencyArchived", isEqualTo: true)
            .orderBy('projectId', descending: true)
            .orderBy('requestStatus', descending: true)
            .orderBy('insertionTimestamp', descending: true)
            .get();
      }else{
        collectionSnapshot = await collectionSnapshotQuery
            .where('agencyId', isEqualTo: widget.agencyUser.agencyId)
            .where("isArchived", isEqualTo: false)
            .where("isAgencyArchived", isEqualTo: true)
            .orderBy('projectId', descending: true)
            .orderBy('requestStatus', descending: true)
            .orderBy('insertionTimestamp', descending: true)
            .limit(controller.recordsPerPage)
            .get();
      }



      collectionSnapshotCounter = await collectionSnapshotCounterQuery
          .where('agencyId', isEqualTo: widget.agencyUser.agencyId)
          .where("isArchived", isEqualTo: false)
          .where("isAgencyArchived", isEqualTo: true)
          .orderBy('projectId', descending: true)
          .orderBy('requestStatus', descending: true)
          .orderBy('insertionTimestamp', descending: true)
          .get();

      controller.totalRecords = collectionSnapshotCounter.docs.length;

      controller.totalPages = (controller.totalRecords / controller.recordsPerPage).ceil();

      if (controller.totalRecords > controller.recordsPerPage) {
        controller.currentlyShowing = '1-' + controller.recordsPerPage.toString();
      } else {
        controller.currentlyShowing = '1-' + controller.recordsPerPage.toString();
      }
      controller.documentList = collectionSnapshot.docs;

      await generateDataRows(collectionSnapshot);

      setState(() {
        controller.loadingProperties = false;
      });
    } catch (e) {
      if (mounted)
        setState(() {
          controller.loadingProperties = false;
          print(e);
        });
    }
  }

  fetchNextProperties() async {
    setState(() {
      controller.loadingProperties = true;
    });

    controller.pageCounter++;

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;
      int indexOfSnapshot = isRecordExists(controller.pageCounter);

      if (indexOfSnapshot > -1) {
        collectionSnapshot = controller.cacheFireStore[indexOfSnapshot]['snapshot'];
      } else {
        collectionSnapshotQuery = await FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS);

        if (controller.filters.length > 0) {
          for (var i = 0; i < controller.filters.length; i++) {
            collectionSnapshotQuery = collectionSnapshotQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
          }
        }
        collectionSnapshot = await collectionSnapshotQuery
            .where('agencyId', isEqualTo: widget.agencyUser.agencyId)
            .where("isArchived", isEqualTo: false)
            .where("isAgencyArchived", isEqualTo: true)
            .orderBy('projectId', descending: true)
            .orderBy('requestStatus', descending: true)
            .orderBy('insertionTimestamp', descending: true)
            .startAfterDocument(controller.documentList[controller.documentList.length - 1])
            .limit(controller.recordsPerPage)
            .get();
      }

      controller.documentList = collectionSnapshot.docs;
      generateDataRows(collectionSnapshot);
    } catch (e) {
      print(e);
      setState(() {
        controller.loadingProperties = false;
      });
    }
  }

  fetchPrevProperties() async {
    setState(() {
      controller.loadingProperties = true;
    });

    controller.pageCounter--;

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      int indexOfSnapshot = isRecordExists(controller.pageCounter);
      if (indexOfSnapshot > -1) {
        collectionSnapshot = controller.cacheFireStore[indexOfSnapshot]['snapshot'];
      } else {
        collectionSnapshotQuery = await FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS);
        if (controller.filters.length > 0) {
          for (var i = 0; i < controller.filters.length; i++) {
            collectionSnapshotQuery = collectionSnapshotQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
          }
        }

        collectionSnapshot = await collectionSnapshotQuery
            .where('agencyId', isEqualTo: widget.agencyUser.agencyId)
            .where("isArchived", isEqualTo: false)
            .where("isAgencyArchived", isEqualTo: true)
            .orderBy('projectId', descending: true)
            .orderBy('requestStatus', descending: true)
            .orderBy('insertionTimestamp', descending: true)
            .endBeforeDocument(controller.documentList[controller.documentList.length - 1])
            .limit(controller.recordsPerPage)
            .get();
      }

      controller.documentList = collectionSnapshot.docs;

      generateDataRows(collectionSnapshot);
    } catch (e) {
      setState(() {
        controller.loadingProperties = false;
      });
    }
  }

  int isRecordExists(int pageCounter) {
    String filtersKey = controller.filters.map((f) => '${f['field']}:${f['value']}').join('|');
    return controller.cacheFireStore.indexWhere(
          (record) => record['key'] == pageCounter && record['filtersKey'] == filtersKey,
    );
  }

  generateDataRows(QuerySnapshot<Map<String, dynamic>> collectionSnapshot) {
    if (isRecordExists(controller.pageCounter) < 0) {
      String filtersKey = controller.filters.map((f) => '${f['field']}:${f['value']}').join('|');
      controller.cacheFireStore.add({
        'key': controller.pageCounter,
        'snapshot': collectionSnapshot,
        'filtersKey': filtersKey,
      });
    }
    if (controller.pageCounter == 1) {
      controller.disablePreviousButton = true;
    } else {
      controller.disablePreviousButton = false;
    }

    if (controller.pageCounter == controller.totalPages) {
      controller.disableNextButton = true;
    } else {
      controller.disableNextButton = false;
    }

    List<ImmaginaProject> _projects = [];


    for (var element in collectionSnapshot.docs) {
      ImmaginaProject _tmp = ImmaginaProject.fromDocument(element.data(), element.id);
      _projects.add(_tmp);
    }

    int lastRecordNumber = controller.pageCounter * controller.recordsPerPage;
    if (_projects.length == controller.recordsPerPage) {
      controller.currentlyShowing = (lastRecordNumber - (controller.recordsPerPage - 1)).toString() + '-' + lastRecordNumber.toString();
    } else if (_projects.length > 0 && _projects.length < controller.recordsPerPage) {
      int prevLastRecordNumber = (controller.pageCounter - 1) * controller.recordsPerPage;

      controller.currentlyShowing = (lastRecordNumber - (controller.recordsPerPage - 1)).toString() + '-' + (prevLastRecordNumber + _projects.length).toString();
    }

    setState(() {
      controller.projects = _projects;
      controller.loadingProperties = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    double pageWidth = MediaQuery.of(context).size.width - 260;
    if (pageWidth < 1800) {
      pageWidth = 1800;
    }
    return LayoutBuilder(
      builder: (context, constraints) {
        return Column(
          children: [
            Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                _headerTitle(),
              ],
            ),
             _filter(),
            SizedBox(height: 10),
            Container(
              height: constraints.maxHeight / 1.2,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: AppColor.white,
                border: Border.all(width: 1.5, color: AppColor.borderColor),
              ),
              child: Column(
                children: [
                  // _tabBar(),
                  Expanded(
                    child: Stack(
                      children: [
                        Opacity(
                            opacity: 1,
                            child: NewarcDataTable(
                              rowsPerPage: 20,
                              isHasDecoration: false,
                              hidePaginator: true,
                              onPageChanged: (val) {
                                print("page : $val");
                              },
                              source: AgencyImmaginaProjectArchiveRequestSource(
                                projects: controller.projects,
                                context: context,
                                onAddressTap: (project) {
                                  widget.projectArguments!.clear();
                                  widget.projectArguments!.addAll({
                                    'projectFirebaseId': project.id,
                                    'property': null,
                                    'agencyUser': widget.agencyUser,
                                    'updateViewCallback': widget.updateViewCallback,
                                    'initialFetchProperties': _initialFetch(force: true),
                                    'isFromRequest':false,
                                    'isFromProjectArchive': true,
                                  });

                                  widget.updateViewCallback!('inside-project', projectArguments: widget.projectArguments);
                                },
                              ),
                              columns: [
                                DataColumn2(label: Text("Indirizzo")),
                                DataColumn2(label: Text("Codice")),
                                DataColumn2(label: Text("Atto di vendita")),
                                DataColumn2(label: Text("Acquirente")),
                                DataColumn2(label: Text("Vendita")),
                                DataColumn2(label: Text("Acquisto")),
                                DataColumn2(label: Text("Success Fee")),
                              ],
                            )),
                        if (controller.loadingProperties)
                          Positioned.fill(
                            child: Center(
                              child: CircularProgressIndicator(
                                color: Theme.of(context).primaryColor,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                  Align(
                    alignment: Alignment.centerRight,
                    child: dataTablePagination(),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  NarFilter _filter() {
    return NarFilter(
      showSearchInput: true,
      isFilterHide: true,
      searchHintText: "Cerca per indirizzo o per codice...",
      onChanged: (String? searchQuery)async{
        if(searchQuery?.isNotEmpty ?? false){
          if (searchQuery != null && searchQuery.isNotEmpty) {
            List<ImmaginaProject> filtered = controller.projects.where((project) {
              final address = project.addressInfo;
              final code = project.projectId.toLowerCase() ?? "";
              final city = address?.city?.toLowerCase() ?? project.city?.toLowerCase() ?? "";
              final streetName = address?.streetName?.toLowerCase() ?? '';
              final fullAddress = address?.fullAddress?.toLowerCase() ?? project.streetName?.toLowerCase() ?? "";
              return code.contains(searchQuery.toLowerCase()) ||
                  city.contains(searchQuery.toLowerCase()) ||
                  streetName.contains(searchQuery.toLowerCase()) ||
                  fullAddress.contains(searchQuery.toLowerCase());
            }).toList();

            setState(() {
              controller.projects = filtered;
            });
          }
        }else{
          await initialFetch(force: true);
        }
      },
      suffixIconOnTap: ()async{
        await initialFetch(force: true,reloadAll: true);
        if(controller.searchTextController.text.trim().isNotEmpty){
          List<ImmaginaProject> filtered = controller.projects.where((project) {
            final address = project.addressInfo;
            final code = project.projectId.toLowerCase() ?? "";
            final city = address?.city?.toLowerCase() ?? project.city?.toLowerCase() ?? "";
            final streetName = address?.streetName?.toLowerCase() ?? '';
            final fullAddress = address?.fullAddress?.toLowerCase() ?? project.streetName?.toLowerCase() ?? "";
            return code.contains(controller.searchTextController.text.toLowerCase()) ||
                city.contains(controller.searchTextController.text.toLowerCase()) ||
                streetName.contains(controller.searchTextController.text.toLowerCase()) ||
                fullAddress.contains(controller.searchTextController.text.toLowerCase());
          }).toList();

          setState(() {
            controller.projects = filtered;
          });
        }else{
          await initialFetch(force: true);
        }
      },
      searchTextEditingControllers: controller.searchTextController,
      selectedFilters: [controller.statusFilterController.text],
      textEditingControllers: [controller.statusFilterController],
      filterFields: [],
      onSubmit: () async {
        await initialFetch(force: true);
      },
      onReset: () async {
        await initialFetch(force: true);
      },
    );
  }

  NarFormLabelWidget _headerTitle() {
    return NarFormLabelWidget(
      label: 'Progetti completati',
      fontSize: 19,
      fontWeight: '700',
      textColor: Colors.black,
    );
  }
}
