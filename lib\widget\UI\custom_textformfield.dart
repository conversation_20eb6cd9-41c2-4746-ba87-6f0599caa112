import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';

import 'package:flutter/services.dart';
import 'package:intl/intl.dart';

class MoneyInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(TextEditingValue oldValue, TextEditingValue newValue) {
    // Remove all non-numeric characters except the comma and dot
    String newText = newValue.text.replaceAll(RegExp(r'[^0-9,]'), '');

    if (newText.isEmpty) {
      return TextEditingValue();
    }

    // Handle the decimal part with a comma
    if (newText.contains(',')) {
      int commaIndex = newText.indexOf(',');
      String beforeComma = newText.substring(0, commaIndex);
      String afterComma = newText.substring(commaIndex + 1);

      // Allow only two digits after the decimal point
      if (afterComma.length > 2) {
        afterComma = afterComma.substring(0, 2);
      }

      // Format the part before the comma
      String formattedValue = NumberFormat('#,##0', 'it_IT').format(int.parse(beforeComma.replaceAll('.', '')));
      newText = '$formattedValue,$afterComma';
    } else {
      // Format as a normal number (with dots for thousands separators)
      newText = NumberFormat('#,##0', 'it_IT').format(int.parse(newText.replaceAll('.', '')));
    }

    // Add the Euro symbol
    //String formattedValueWithSymbol = '€$newText';

    // Set the new cursor position
    return TextEditingValue(
      text: newText,
      selection: TextSelection.collapsed(offset: newText.length),
    );
  }
}

class CustomTextFormField extends StatefulWidget {
  final Key? key;
  final String? initialValue;
  final String? label;
  final Color? labelColor;
  final Color? fillColor;
  final Color? disabledColor;
  final String? hintText;
  final Function? onChangedCallback;
  final Function? onFieldSubmitted;
  final Function? validator;
  final Function? onTap;
  final Function? onTapOutside;
  final bool isMoney;
  final bool isShowPrefillMoneyIcon;
  final bool isCenterLabel;
  final bool isNumber;
  final bool isPercentage;
  final bool isNullable;
  final bool isHaveBorder;
  final TextEditingController? controller;
  final String? validationMessage;
  final bool? isObscureText;
  final Widget? suffixIcon;
  final List<TextInputFormatter>? inputFormatters;
  final TextCapitalization? textCapitalization;
  final int? flex;
  final int? minLines;
  List<String>? autoFillHints;
  bool? enabled;
  bool? readOnly;
  double? controllerFontSize, labelFontSize;
  final EdgeInsets? contentPadding;
  final TextAlign? textAlign;
  final bool isExpanded;
  final bool expands;

  CustomTextFormField({
    this.key,
    this.initialValue,
    this.controller,
    this.onChangedCallback,
    this.label,
    this.hintText,
    this.isMoney = false,
    this.isShowPrefillMoneyIcon = true,
    this.isCenterLabel = false,
    this.isNumber = false,
    this.isPercentage = false,
    this.isNullable = false,
    this.readOnly = false,
    this.isHaveBorder = true,
    this.validationMessage,
    this.isObscureText = false,
    this.suffixIcon = const SizedBox(height: 0),
    this.flex = 1,
    this.onFieldSubmitted,
    this.onTap,
    this.onTapOutside,
    this.validator,
    this.labelColor = const Color(0xff696969),
    this.fillColor = Colors.white,
    this.disabledColor = const Color(0xFFF2F2F2),
    this.inputFormatters = const [],
    this.textCapitalization,
    this.autoFillHints,
    this.enabled = true,
    this.minLines = 1,
    this.controllerFontSize = 15,
    this.isExpanded = true,
    this.labelFontSize = 13,
    this.contentPadding = const EdgeInsets.only(top: 17, bottom: 17, right: 2, left: 10), this.textAlign,
    this.expands = false,
  });

  @override
  _CustomTextFormFieldState createState() => _CustomTextFormFieldState();
}

class _CustomTextFormFieldState extends State<CustomTextFormField> {
  bool isObscure = false;
  String label = '';

  @override
  void initState() {
    if (widget.autoFillHints == null) {
      widget.autoFillHints = [];
    }
    setInitValues();
    super.initState();
  }

  @protected
  void didUpdateWidget(CustomTextFormField oldWidget) {
    super.didUpdateWidget(oldWidget);
    setInitValues();
  }

  setInitValues() {
    isObscure = widget.isObscureText!;
    label = widget.label!;
  }

  @override
  Widget build(BuildContext context) {
    var opacity = Opacity(
        opacity: widget.enabled! ? 1 : 0.6,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            label != ''
                ? Column(
                    crossAxisAlignment: widget.isCenterLabel ? CrossAxisAlignment.center : CrossAxisAlignment.start,
                    children: [
                      if(widget.isCenterLabel)...[
                        Center(
                          child: NarFormLabelWidget(
                            label: label,
                            textAlign: TextAlign.center,
                            textColor: widget.labelColor,
                            fontSize: widget.labelFontSize,
                            fontWeight: '500',
                          ),
                        ),
                      ]else...[
                        NarFormLabelWidget(
                          label: label,
                          textAlign: TextAlign.center,
                          textColor: widget.labelColor,
                          fontSize: widget.labelFontSize,
                          fontWeight: '500',
                        ),
                      ],

                      if (widget.isCenterLabel == false) SizedBox(height: 4)
                    ],
                  )
                : SizedBox(height: 0),
            TextFormField(
              expands: widget.expands,
              minLines: widget.expands ? null : widget.minLines,
              maxLines: widget.expands ? null : widget.minLines,
              key: widget.key,
              textCapitalization: widget.textCapitalization ?? TextCapitalization.none,
              obscureText: isObscure,
              initialValue: widget.initialValue,
              cursorColor: Colors.black,
              controller: widget.controller,
              autofillHints: widget.autoFillHints,
              readOnly: widget.readOnly ?? false,
              textAlign: widget.textAlign ??  TextAlign. start,
              inputFormatters: widget.isMoney
                  ? [
                      FilteringTextInputFormatter.allow(RegExp(r'[0-9,]')), // Allow only numbers and dot
                      MoneyInputFormatter(),
                    ]
                  : widget.inputFormatters,
              enabled: widget.enabled,
              autovalidateMode: AutovalidateMode.onUserInteraction,
              style: TextStyle(
                color: Colors.black,
                fontSize: widget.controllerFontSize,
                fontFamily: 'Raleway-700',
              ),
              decoration: InputDecoration(
                hintText: widget.hintText,
                prefixText: widget.isMoney ? widget.isShowPrefillMoneyIcon ? "€" : null : null,
                suffixText: widget.isPercentage ? "%" : null,
                suffixIcon: widget.suffixIcon,
                fillColor: widget.readOnly ?? false ? widget.disabledColor : widget.fillColor,
                contentPadding: widget.contentPadding,
                isCollapsed: true,
                hoverColor: Colors.transparent,
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.0),
                  borderSide: widget.readOnly ?? false ? BorderSide.none : widget.isHaveBorder ? BorderSide(color: Color(0xffdbdbdb)) : BorderSide.none,
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.0),
                  borderSide: widget.readOnly ?? false ? BorderSide.none : widget.isHaveBorder ? BorderSide(color: Color(0xffdbdbdb)) : BorderSide.none,
                ),
                disabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.0),
                  borderSide: widget.readOnly ?? false ? BorderSide.none : widget.isHaveBorder ? BorderSide(color: Color(0xffdbdbdb)) : BorderSide.none,
                ),
              ),
              onTap: () {
                if (widget.onTap != null) {
                  return widget.onTap!();
                }
              },
              onTapOutside: (pointDownEvent) {
                if (widget.onTapOutside != null) {
                  return widget.onTapOutside!();
                }
              },
              validator: (value) {
                if (widget.validator != null) {
                  if (widget.isMoney) {
                    value = parseMoney(value!);
                  }
                  return widget.validator!(value);
                }
              },
              onChanged: (value) {
                if (widget.isMoney) {
                  String parsedValue = parseMoney(value);
                  if (widget.onChangedCallback != null) {
                    widget.onChangedCallback!(parsedValue);
                  }
                } else {
                  if (widget.onChangedCallback != null) {
                    widget.onChangedCallback!(value);
                  }
                }
              },
              onFieldSubmitted: (value) {
                if (widget.onFieldSubmitted != null) {
                  if (widget.isMoney) {
                    value = parseMoney(value);
                  }
                  widget.onFieldSubmitted!(value);
                }
              },
            ),
            widget.validationMessage != null && widget.validationMessage!.isNotEmpty
                ? NarFormLabelWidget(
                    label: widget.validationMessage!,
                    fontSize: 12,
                    textColor: Colors.red,
                  )
                : SizedBox(height: 0)
          ],
        ),
      );
    var expanded =
    widget.isExpanded
        ? Expanded(
      flex: widget.flex!,
      child: opacity,
    ) : opacity;
    return expanded;
  }

  String parseMoney(String value) {
    // Remove everything except digits and decimal points
    //String cleanedValue = value.replaceAll(RegExp(r'[^\d.]'), '');
    String cleanedValue = value;
    // Convert to double
    return cleanedValue;
  }
}
