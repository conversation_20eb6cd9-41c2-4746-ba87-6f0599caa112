<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base
  -->
  <base href="/">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="Newarc Platform">
  
  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="Newarc Platform">

  <link id="favicon" rel="icon" type="image/x-icon" href="">


  <script>
    const subdomain = window.location.hostname.split('.')[0];

    let favicon;
    if(subdomain === 'work') {
      favicon = 'favicon.png'; 
    } else if(subdomain === 'agenzie') {
      favicon = 'favicon_agenzie.png';
    }

    document.getElementById('favicon').href = favicon;
  </script>
  <script src="//cdnjs.cloudflare.com/ajax/libs/pdf.js/2.4.456/pdf.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
  <script src="pdf_to_jpeg.js"></script>

  <script type="text/javascript">
     pdfjsLib.GlobalWorkerOptions.workerSrc = "//cdnjs.cloudflare.com/ajax/libs/pdf.js/2.4.456/pdf.worker.min.js";
  </script>
  <!--<link rel="apple-touch-icon" href="favicon.png">
  <link rel="icon" type="image/png" href="favicon.png"/>-->

  <title>Newarc Platform</title>
  <link rel="manifest" href="manifest.json">
  <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCH2BcaOkNgjYZqigrewdxXyz5atnsfdRo"></script>
</head>
</head>
<body>
  <!-- This script installs service_worker.js to provide PWA functionality to
       application. For more information, see:
       https://developers.google.com/web/fundamentals/primers/service-workers -->

  <!-- HEIC to any converter -->
  <script src="https://unpkg.com/heic2any"></script>
  <script>
    async function convertHeicToJpeg(heicFile) {
      try {
        const convertedBlob = await heic2any({
          blob: heicFile,
          toType: "image/jpeg",
        });
        return convertedBlob;
      } catch (error) {
        console.error("HEIC to JPEG conversion failed:", error);
        throw error;
      }
    }
  </script>
      
  <script>
var serviceWorkerVersion = '{{flutter_service_worker_version}}';
var scriptLoaded = false;
    function loadMainDartJs() {
      if (scriptLoaded) {
        return;
      }
      scriptLoaded = true;
      var scriptTag = document.createElement('script');
      scriptTag.src = 'main.dart.js?version=1';
      scriptTag.type = 'application/javascript';
      document.body.append(scriptTag);
    }

    if ('serviceWorker' in navigator) {
      // Service workers are supported. Use them.
      window.addEventListener('load', function () {
        // Wait for registration to finish before dropping the <script> tag.
        // Otherwise, the browser will load the script multiple times,
        // potentially different versions.
        var serviceWorkerUrl = 'flutter_service_worker.js?v=' + serviceWorkerVersion;
        navigator.serviceWorker.register(serviceWorkerUrl)
          .then((reg) => {
            function waitForActivation(serviceWorker) {
              serviceWorker.addEventListener('statechange', () => {
                if (serviceWorker.state == 'activated') {
                  console.log('Installed new service worker.');
                  loadMainDartJs();
                }
              });
            }
            if (!reg.active && (reg.installing || reg.waiting)) {
              // No active web worker and we have installed or are installing
              // one for the first time. Simply wait for it to activate.
              waitForActivation(reg.installing ?? reg.waiting);
            } else if (!reg.active.scriptURL.endsWith(serviceWorkerVersion)) {
              // When the app updates the serviceWorkerVersion changes, so we
              // need to ask the service worker to update.
              console.log('New service worker available.');
              reg.update();
              waitForActivation(reg.installing);
            } else {
              // Existing service worker is still good.
              console.log('Loading app from service worker.');
              loadMainDartJs();
            }
          });

        // If service worker doesn't succeed in a reasonable amount of time,
        // fallback to plaint <script> tag.
        setTimeout(() => {
          if (!scriptLoaded) {
            console.warn(
              'Failed to load app from service worker. Falling back to plain <script> tag.',
            );
            loadMainDartJs();
          }
        }, 4000);
      });
    } else {
      // Service workers not supported. Just drop the <script> tag.
      loadMainDartJs();
    }
  </script>
  <!-- The core Firebase JS SDK is always required and must be listed first -->
  <script src="https://www.gstatic.com/firebasejs/10.12.3/firebase-app.js"></script>
  <script src="https://www.gstatic.com/firebasejs/10.12.3/firebase-functions.js"></script>
  <script src="https://www.gstatic.com/firebasejs/10.12.3/firebase-firestore.js"></script>
  <script src="https://www.gstatic.com/firebasejs/10.12.3/firebase-auth.js"></script>
  
  <script type="application/javascript">
    Element.prototype._attachShadow = Element.prototype.attachShadow;
    Element.prototype.attachShadow = function (init) {
      if (this.tagName == "FLT-GLASS-PANE" && window.location.pathname == '/login') {

        _element = document.createElement('flt-element-host-node');
        this.appendChild(_element);

        return _element;
      }

      return this._attachShadow(init);
    };
    </script>
</body>
</html>
