import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/classes/newarcProject.dart';
import 'package:newarc_platform/classes/process.dart';
import 'package:newarc_platform/classes/projectEconomic.dart';
import 'package:newarc_platform/classes/projectJob.dart';
import 'package:newarc_platform/classes/supplier.dart';
import 'package:newarc_platform/classes/user.dart';
import 'package:newarc_platform/utils/storage.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:intl/intl.dart';

import '../../../utils/color_schema.dart';
import '../../UI/tab/common_icon_button.dart';

class ProjectVendorAndProfessional extends StatefulWidget {
  final NewarcProject? project;
  final List<Supplier>? suppliers;
  final Function? updateProject;

  const ProjectVendorAndProfessional(
      {Key? key, this.project, this.suppliers, this.updateProject})
      : super(key: key);

  @override
  State<ProjectVendorAndProfessional> createState() =>
      _ProjectVendorAndProfessionalState();
}

class _ProjectVendorAndProfessionalState
    extends State<ProjectVendorAndProfessional> with TickerProviderStateMixin {
  NumberFormat localCurrencyFormat =
      NumberFormat.currency(locale: 'it_IT', symbol: '\€', decimalDigits: 0);
  NumberFormat localCurrencyFormatMain =
      NumberFormat.currency(locale: 'it_IT', symbol: '', decimalDigits: 0);

  TextEditingController contIndexPlace = new TextEditingController();
  TextEditingController contVendor = new TextEditingController();
  TextEditingController contActivity = new TextEditingController();
  TextEditingController contCost = new TextEditingController();
  TextEditingController contAgreedMilestones = new TextEditingController();
  bool isMainContractor = true;
  bool hasPenalty = true;
  bool isSingle = true;
  TextEditingController contPenalty = new TextEditingController();
  TextEditingController contJobStart = new TextEditingController();
  TextEditingController contJobEnd = new TextEditingController();
  int? jobStart;
  int? jobEnd;

  List<String> activities = [];

  List<Process>? processes = [];
  List<Map> _suppliers = [];
  List<Map> _initSuppliers = [];
  List<Map> installmentsInputs = [];

  List<bool> isAnimated = [];
  String progressMessage = '';
  double budget = 1;
  double spent = 0;
  Color? spendColor;

  bool disableIsContractorSwitch = false;
  bool disableIsUnique = false;

  String? tmpTitle;
  bool? showTitle;

  /* 
  * This is a Hard coded value to test the Delete 
  * true: if wanted to allow to delete
  * false: if wanted to deny 
  */
  bool canDeleteProcess = true;

  @override
  void initState() {
    super.initState();

    try {
      if (widget.project!.provisionalAccountId != '') {
        fetchProvisional();
        // loading = true;
      }

      setInitialValues();
    } catch (e, s) {
      print({e, s});
    }

    _initSuppliers = widget.suppliers!.map((e) {
      return {'value': e.id, 'label': e.name! + ' ' + e.formationType!};
    }).toList();


    _suppliers.add({'value': '', 'label': ''});

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      // fetchCategoryActivities();
    });

  }

  @protected
  void didUpdateWidget(ProjectVendorAndProfessional oldWidget) {
    super.didUpdateWidget(oldWidget);

    setInitialValues();
  }

  fetchCategoryActivities() async {

    activities.clear();
    try {
      final FirebaseFirestore _db = FirebaseFirestore.instance;
      QuerySnapshot<Map<String, dynamic>> snapshot = await _db
          .collection(appConfig.COLLECT_CATEGORY_ACTIVITY)
          .get();

      if( snapshot.docs.length > 0 ) {
        
        for (var i = 0; i < snapshot.docs.length; i++) {
          activities.add(snapshot.docs[i].data()['categoryName']);
        }

      }
      setState(() {
        
      });
    } catch (e,s) {
      print({'fetchCategoryActivities',e,s});
    }

  }

  setInitialValues() {
    print({'setInitialValues'});
    isAnimated.clear();

    log("widget.project ==> ${widget.project?.id}");

    // budget = 10000;
    spent = 0;

    tmpTitle = '';
    showTitle = true;

    widget.project!.vendorAndProfessionals!
        .sort((a, b) => a.vendorUserId!.compareTo(b.vendorUserId!));

    if (widget.project!.vendorAndProfessionals!.length > 0) {
      for (var i = 0; i < widget.project!.vendorAndProfessionals!.length; i++) {
        widget.project!.vendorAndProfessionals![i].indexPlace = i;

        spent += widget.project!.vendorAndProfessionals![i].cost!;
        isAnimated.add(false);
      }
    } else {
      spent = 0;
    }

    if(widget.project?.type == "Ristrutturazione"){
      budget = widget.project?.budgetDitteProfRistrutturazione ?? 1.0 ;
    }

    double spendPercent = (spent / budget) * 100;
    if (spendPercent <= 25) {
      spendColor = Color(0xff4E9A7A);
    } else if (spendPercent > 25 && spendPercent <= 100) {
      spendColor = Color(0xffFFC702);
    } else if (spendPercent > 100) {
      spendColor = Color(0xffE82525);
    }

    setState(() {});
  }


  fetchProvisional() async {

    DocumentSnapshot<Map<String, dynamic>> collectionSnapshot =
        await FirebaseFirestore.instance
            .collection(appConfig.COLLECT_PROVISIONAL_ECONOMIC_ACCOUNT)
            .doc(widget.project!.provisionalAccountId)
            .get();

    ProjectEconomic? tempEconomic = ProjectEconomic.empty();

    if (collectionSnapshot.exists) {
      tempEconomic = ProjectEconomic.fromDocument(
          collectionSnapshot.data()!, collectionSnapshot.id);

      if (tempEconomic.restorationAndMaterail!['lavoriRistrutturazione'].runtimeType.toString() != 'Null') {
        setState(() {
          tmpTitle = '';
          // showTitle = true;
          budget = double.tryParse(tempEconomic!.restorationAndMaterail!['lavoriRistrutturazione'])!;
        });
      }
    }

    tmpTitle = '';
    // showTitle = true;
  }

  Widget statusIcon(bool status) {
    return Container(
      height: 16,
      width: 16,
      decoration: BoxDecoration(
        color: status ? Theme.of(context).primaryColor : Color(0xffD1D1D1),
        borderRadius: BorderRadius.circular(14),
      ),
      child: Center(
        child: Icon(
          status ? Icons.check : Icons.close_rounded,
          color: Colors.white,
          size: 12,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: double.infinity,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: ListView(
              shrinkWrap: true,

              children: [
                SizedBox(height: 20),
                Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    NarFormLabelWidget(
                      label: 'Ditte e Professionisti',
                      fontSize: 20,
                      fontWeight: 'bold',
                    ),
                  ],
                ),
                SizedBox(height: 30),
                Container(
                  // color: Colors.grey,
                  // width: 200,
                  child: ListView(
                    shrinkWrap: true,
                    children: [
                      Row(
                        children: [
                          Container(
                            width:200,
                            constraints: BoxConstraints(minWidth: 160, ),
                            padding: EdgeInsets.symmetric(vertical: 7, horizontal: 15),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(
                                width: 1,
                                color: Color.fromRGBO(214, 214, 214, 1),
                              ),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  mainAxisSize: MainAxisSize.max,
                                  children: [
                                    NarFormLabelWidget(
                                        label: 'Budget',
                                        fontSize: 12,
                                        fontWeight: '600',
                                        textColor: Colors.black),

                                    widget.project?.type == "Ristrutturazione" ?
                                    IconButtonWidget(
                                      onTap: () {
                                        showEditBudgetPopup();
                                      },
                                      iconPadding: EdgeInsets.zero,
                                      height: 15,
                                      width: 15,
                                      isSvgIcon: false,
                                      backgroundColor: AppColor.white,
                                      icon:
                                      'assets/icons/edit.png',
                                      iconColor: AppColor.greyColor,
                                    ) : SizedBox.fromSize(),
                                  ],
                                ),
                                SizedBox(
                                  height: 10,
                                ),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisSize: MainAxisSize.max,
                                  children: [
                                    NarFormLabelWidget(
                                        label: localCurrencyFormatMain.format(budget),
                                        fontSize: 25,
                                        fontWeight: 'bold',
                                        textColor: Colors.black),
                                    SizedBox(
                                      width: 20,
                                    ),
                                    NarFormLabelWidget(
                                        label: '€',
                                        fontSize: 17,
                                        fontWeight: 'bold',
                                        textColor: Colors.black),
                                  ],
                                )
                              ],
                            ),
                          ),
                          SizedBox(
                            width: 25,
                          ),
                          Container(
                            // width: 160,
                            constraints: BoxConstraints(
                              minWidth: 160
                            ),
                            padding: EdgeInsets.symmetric(
                                vertical: 7, horizontal: 15),
                            decoration: BoxDecoration(
                              color: spendColor!,
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(
                                width: 1,
                                color: spendColor!,
                              ),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                NarFormLabelWidget(
                                  label: 'Costo raggiunto',
                                  fontSize: 12,
                                  fontWeight: '600',
                                  textColor: Colors.white,
                                ),
                                SizedBox(
                                  height: 10,
                                ),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisSize: MainAxisSize.max,
                                  children: [
                                    NarFormLabelWidget(
                                      label: localCurrencyFormatMain.format(spent),
                                      fontSize: 25,
                                      fontWeight: 'bold',
                                      textColor: Colors.white,
                                    ),
                                    SizedBox(
                                      width: 20,
                                    ),
                                    NarFormLabelWidget(
                                      label: '€',
                                      fontSize: 17,
                                      fontWeight: 'bold',
                                      textColor: Colors.white,
                                    ),
                                  ],
                                )
                              ],
                            ),
                          )
                        ],
                      ),
                      SizedBox(height: 10,),
                      Column(
                        children:
                            widget.project!.vendorAndProfessionals!.map((e) {
                          return processWrapper(context, e);
                        }).toList(),
                      )
                    ],
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> showEditBudgetPopup() async {
    TextEditingController budgetController = TextEditingController(text: budget.toString());
    List<String> formErrorMessage = [];
    await showDialog(
        context: context,
        builder: (BuildContext _context) {
          return StatefulBuilder(builder: (__context, _setState) {
            return Center(
                child: BaseNewarcPopup(
                  title: "Inserisci Budget",
                  buttonText: "Salva",
                  formErrorMessage: formErrorMessage,
                  onPressed: () async {
                    _setState((){
                      formErrorMessage.clear();
                      formErrorMessage.add("Salvataggio in corso...");
                    });
                   try{
                     double priceOnchange = double.tryParse(budgetController.text.trim().replaceAll('.', '').replaceAll(',', '.').toString()) ?? 0.0;

                     double spendPercent = (spent / priceOnchange) * 100;
                     if (spendPercent <= 25) {
                       spendColor = Color(0xff4E9A7A);
                     } else if (spendPercent > 25 && spendPercent <= 100) {
                       spendColor = Color(0xffFFC702);
                     } else if (spendPercent > 100) {
                       spendColor = Color(0xffE82525);
                     }
                     setState(() {
                       budget = priceOnchange;
                     });

                     await FirebaseFirestore.instance
                         .collection(appConfig.COLLECT_NEWARC_PROJECTS)
                         .doc(widget.project!.id)
                         .update({"budgetDitteProfRistrutturazione" : priceOnchange});
                     _setState((){
                       formErrorMessage.clear();
                       formErrorMessage.add("Salvato");
                     });
                   }catch(e){
                     _setState((){
                       formErrorMessage.clear();
                       formErrorMessage.add("Si è verificato un errore.");
                     });
                     log("Error while editing budget ----> ${e.toString()}");
                   }
                  },
                  buttonColor: Theme.of(context).primaryColor,
                  column: Container(
                    width: 400,
                    child: CustomTextFormField(
                      isExpanded: false,
                      isMoney: true,
                      label: "Budget",
                      controller: budgetController,
                      validator: (value) {
                        if (value == '') {
                          return 'Required!';
                        }
                        return null;
                      },
                    ),
                  ),
                ));
          });
        });
  }

  Widget processWrapper(BuildContext context, Process processRow) {
    int count = widget.project!.vendorAndProfessionals!.indexOf(processRow);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(vertical: 12, horizontal: 12),
          margin: EdgeInsets.only(top: 10),
          decoration: BoxDecoration(
            color: Color.fromRGBO(242, 242, 242, 1),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Column(
            children: [
              Container(
                child: Stack(
                  clipBehavior: Clip.none,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          flex: 5,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              NarFormLabelWidget(
                                label: 'Ditta',
                                fontSize: 12,
                                textColor: Color(0xff696969),
                                fontWeight: '500',
                              ),
                              SizedBox(
                                height: 10,
                              ),
                              NarFormLabelWidget(
                                label: _initSuppliers
                                    .where((element) => element['value'] == processRow.vendorUserId)
                                    .first['label'],
                                fontSize: 14,
                                fontWeight: 'bold',
                                textColor: Colors.black,
                              )
                            ],
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              NarFormLabelWidget(
                                label: 'Costo',
                                fontSize: 12,
                                textColor: Color(0xff696969),
                                fontWeight: '500',
                              ),
                              SizedBox(
                                height: 10,
                              ),
                              NarFormLabelWidget(
                                label: localCurrencyFormat.format(
                                    processRow.cost), //.toString() +'€',
                                fontSize: 14,
                                fontWeight: 'bold',
                                textColor: Colors.black,
                              )
                            ],
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              NarFormLabelWidget(
                                label: 'Pagamento',
                                fontSize: 12,
                                textColor: Color(0xff696969),
                                fontWeight: '500',
                              ),
                              SizedBox(
                                height: 10,
                              ),
                              NarFormLabelWidget(
                                label:
                                    processRow.agreedInstallments.toString() +
                                        ' rate',
                                fontSize: 14,
                                fontWeight: 'bold',
                                textColor: Colors.black,
                              )
                            ],
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              NarFormLabelWidget(
                                label: 'Penale',
                                fontSize: 12,
                                textColor: Color(0xff696969),
                                fontWeight: '500',
                              ),
                              SizedBox(
                                height: 10,
                              ),
                              statusIcon(processRow.hasPenalty!)
                              // ? Icon(Icons.check_circle, size: 14, color: Theme.of(context).primaryColor,)
                              // : Icon(Icons.close, size: 14, color: Theme.of(context).primaryColor,)
                            ],
                          ),
                        ),
                        Expanded(
                          flex: 1,
                          child: Column(
                            mainAxisSize: MainAxisSize.max,
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              MouseRegion(
                                cursor: SystemMouseCursors.click,
                                child: GestureDetector(
                                  onTap: (){
                                    setState(() {
                                      progressMessage = '';
                                    });
                                    disableIsUnique = false;
                                    if (processRow.isUnique!) {
                                      disableIsUnique = false;
                                    } else if (widget.project! .vendorAndProfessionals!.length > 1) {
                                      disableIsUnique = true;
                                    }

                                    disableIsContractorSwitch = false;

                                    if (processRow.contractor!) {
                                      disableIsContractorSwitch = false;
                                    } else if (widget.project! .vendorAndProfessionals!.length >
                                        1) {
                                      for (var i = 0; i < widget.project! .vendorAndProfessionals!.length; i++) {
                                        if (i != processRow.indexPlace) {
                                          if (widget .project! .vendorAndProfessionals![i] .contractor == true) {
                                            disableIsContractorSwitch = true;
                                          }
                                        }
                                      }
                                    }

                                    addVendorPopup(context, processRow);
                                  },
                                  child: Container(
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(6),
                                      color: Colors.white
                                    ),
                                    padding: EdgeInsets.all(5),
                                    child: Image.asset(
                                      'assets/icons/edit.png',
                                      color: Color(0xff6D6D6D),
                                      height: 16
                                    ),
                                    height: 27,
                                    width: 27,
                                    
                                  ),
                                ),
                              ),
                            ],
                          ),
                        )
                      ],
                    ),
                    Positioned(
                      top: -15,
                      left: 40,
                      child: Row(
                        children: [
                          processRow.contractor!
                              ? Container(
                                  margin: EdgeInsets.only(left: 5),
                                  padding: EdgeInsets.symmetric(
                                      vertical: 3, horizontal: 5),
                                  decoration: BoxDecoration(
                                    color: Theme.of(context).primaryColor,
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: NarFormLabelWidget(
                                    label: 'Appaltatore',
                                    textColor: Colors.white,
                                    fontSize: 10,
                                  ))
                              : SizedBox(
                                  height: 0,
                                ),
                          processRow.isUnique!
                              ? Container(
                                  margin: EdgeInsets.only(left: 5),
                                  padding: EdgeInsets.symmetric(
                                      vertical: 3, horizontal: 5),
                                  decoration: BoxDecoration(
                                    color: Theme.of(context).primaryColor,
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: NarFormLabelWidget(
                                    label: 'Ditta unica',
                                    textColor: Colors.white,
                                    fontSize: 10,
                                  ))
                              : SizedBox(
                                  height: 0,
                                )
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              /*Container(
                width: double.infinity,
                height: 1,
                decoration: BoxDecoration(
                  color: Color(0xFFDCDCDC),
                ),
                child: SizedBox(height: 0),
              ),*/
              isAnimated[count] == false
                  ? SizedBox(height: 0)
                  : Container(
                      // sizeFactor: _animation![count],
                      // axis: Axis.vertical,
                      child: Column(children: <Widget>[
                        Padding(
                          padding: EdgeInsets.all(10),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Expanded(
                                  flex: 3,
                                  child: Container(
                                      padding: EdgeInsets.symmetric(
                                          vertical: 10, horizontal: 15),
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          NarFormLabelWidget(
                                            label: 'Pagamenti',
                                            fontSize: 13,
                                            textColor: Colors.black,
                                            letterSpacing: 0.01,
                                          ),
                                          SizedBox(
                                            height: 10,
                                          ),
                                          Wrap(
                                            runSpacing: 18.0,
                                            children: processRow.installments!.map((e) => paymentInstallment(e)) .toList()
                                          )
                                        ],
                                      ))),
                              SizedBox(
                                width: 15,
                              ),
                              Expanded(
                                  flex: 2,
                                  child: Container(
                                      padding: EdgeInsets.symmetric(
                                          vertical: 10, horizontal: 15),
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          NarFormLabelWidget(
                                            label: 'Penale',
                                            fontSize: 13,
                                            textColor: Colors.black,
                                            letterSpacing: 0.01,
                                          ),
                                          SizedBox(
                                            height: 10,
                                          ),
                                          Column(
                                            mainAxisAlignment: MainAxisAlignment.start,
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Column(
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                  Column(
                                                    crossAxisAlignment: CrossAxisAlignment .start,
                                                    children: [
                                                      NarFormLabelWidget(
                                                        label: 'Penale',
                                                        fontSize: 12,
                                                        textColor: Color(0xFF696969),
                                                        fontWeight: '600',
                                                        letterSpacing: 0.02,
                                                      ),
                                                      processRow.hasPenalty!
                                                          ? NarFormLabelWidget(
                                                              label: localCurrencyFormat.format( processRow .agreedPenalty) + '/giorno',
                                                              fontSize: 13,
                                                              textColor: Colors.black,
                                                              letterSpacing: 0.01,
                                                            )
                                                          : NarFormLabelWidget(
                                                              label: '-',
                                                              fontSize: 13,
                                                              textColor: Colors.black
                                                            ),
                                                    ],
                                                  ),
                                                ],
                                              ),
                                              SizedBox(
                                                height: 10,
                                              ),
                                              Row(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                // mainAxisAlignment: MainAxisAlignment.end,
                                                mainAxisSize: MainAxisSize.max,
                                                children: [
                                                  Expanded(
                                                    child: Column(
                                                      crossAxisAlignment: CrossAxisAlignment .start,
                                                      children: [
                                                        NarFormLabelWidget(
                                                          label: 'Inizio lavori',
                                                          fontSize: 12,
                                                          textColor: Color(0xFF696969),
                                                          fontWeight: '600',
                                                          letterSpacing: 0.02,
                                                        ),
                                                        processRow.hasPenalty!
                                                            ? NarFormLabelWidget(
                                                                label: processRow.jobStartTimestamp! > 0 ? getFormattedDate(processRow.jobStartTimestamp!) : '-',
                                                                fontSize: 13,
                                                                textColor: Colors .black,
                                                                letterSpacing: 0.01,
                                                              )
                                                            : NarFormLabelWidget(
                                                                label: '-',
                                                                fontSize: 13,
                                                                textColor: Colors.black
                                                              ),
                                                      ],
                                                    ),
                                                  ),
                                                  Expanded(
                                                    child: Column(
                                                      crossAxisAlignment: CrossAxisAlignment .start,
                                                      children: [
                                                        NarFormLabelWidget(
                                                          label: 'Fine lavori',
                                                          fontSize: 12,
                                                          textColor: Color(0xFF696969),
                                                          fontWeight: '600',
                                                          letterSpacing: 0.02,
                                                        ),
                                                        processRow.hasPenalty!
                                                            ? NarFormLabelWidget(
                                                                label: processRow.jobEndTimestamp! > 0 ? getFormattedDate(processRow.jobEndTimestamp!) : '-',
                                                                fontSize: 13,
                                                                textColor: Colors .black,
                                                                letterSpacing: 0.01,
                                                              )
                                                            : NarFormLabelWidget(
                                                                label: '-',
                                                                fontSize: 13,
                                                                textColor: Colors .black
                                                              ),
                                                      ],
                                                    ),
                                                  ),
                                                ],
                                              )
                                            ],
                                          )
                                        ],
                                      )))
                            ],
                          ),
                        )
                      ]),
                    ),
              
              Row(
                children: [
                  Expanded(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.end,
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 8),
                          child: MouseRegion(
                            cursor: SystemMouseCursors.click,
                            child: GestureDetector(
                              onTap: (){
                                setState(() {
                                  tmpTitle = '';
                                  showTitle = false;
                                  isAnimated[count] = !isAnimated[count];
                                });
                              },
                              child: isAnimated[count]
                                  ? Image.asset(
                                    'assets/icons/arrow_up.png',
                                    height: 13,
                                    color: Color(0xFF7d7d7d),
                                  ) 
                                  : Image.asset(
                                    'assets/icons/arrow_down.png',
                                    height: 13,
                                    color: Color(0xFF7d7d7d),
                                  )
                            )
                          ),
                        )
                      ],
                    ),
                  )
                ],
              ),
            ],
          ),
        )
      ],
    );
  }

  Widget paymentInstallment(PaymentInstallment paymentInstallmentRow) {
    String date = '';
    if (paymentInstallmentRow.paidOn! > 0 &&
        paymentInstallmentRow.paidOn != null) {
      DateTime paidOn = DateTime.fromMillisecondsSinceEpoch(paymentInstallmentRow.paidOn!);
      date = getFormattedDate(paymentInstallmentRow.paidOn!);
    }

    return Container(
      width: 82,
        margin: const EdgeInsets.only(right: 7),
        decoration: BoxDecoration(
          border: Border(
            right: BorderSide(
              width: 1,
              color: paymentInstallmentRow.isEndOfList!
                  ? Colors.transparent
                  : Color.fromRGBO(214, 214, 214, 1),
            )
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                NarFormLabelWidget(
                  label: 'Rata ' + paymentInstallmentRow.counter.toString(),
                  fontSize: 12,
                  textColor: Color(0xFF696969),
                  fontWeight: '600',
                  letterSpacing: 0.02,
                ),
                SizedBox(height:3),
                NarFormLabelWidget(
                    label: localCurrencyFormat .format(paymentInstallmentRow.amount),
                    fontSize: 13,
                    textColor: Colors.black,
                    letterSpacing: 0.01,
                  ),
              ],
            ),
            SizedBox(
              height: 10,
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                NarFormLabelWidget(
                  label: 'Pagata',
                  fontSize: 12,
                  textColor: Color(0xFF696969),
                  fontWeight: '600',
                  letterSpacing: 0.02,
                ),
                SizedBox(height:3),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Flexible(
                      child: Row(
                        children: [
                          statusIcon(paymentInstallmentRow.status!),
                          SizedBox(width: 5),
                          paymentInstallmentRow.status!
                              ? NarFormLabelWidget(
                                  label: date,
                                  fontSize: 10,
                                  textColor: Colors.black,
                                  letterSpacing: 0.01,
                                )
                              : SizedBox(
                                  height: 0,
                                )
                        ],
                      ),
                    )
                  ],
                ),
              ],
            ),
          ],
        ));
  }

  formatDateForParsing(String dateString) {
    List splittedDate = dateString.split('/');
    // print(splittedDate);
    return splittedDate[2] + '-' + splittedDate[1] + '-' + splittedDate[0];
  }

  messageDialog(BuildContext context, String title, String message) {
    return showDialog(
        context: context,
        builder: (BuildContext _bc1) {
          return StatefulBuilder(
              builder: (BuildContext _bc2, StateSetter setState) {
            return Center(
                child: BaseNewarcPopup(
              title: title,
              buttonText: 'Ok',
              onPressed: () async {
                // Navigator.pop(context);
                return true;
              },
              column: Container(
                  height: 99,
                  width: 465,
                  child: Center(
                    child: NarFormLabelWidget(
                        overflow: TextOverflow.visible,
                        label: message,
                        textAlign: TextAlign.center,
                        fontSize: 18,
                        fontWeight: '600',
                        height: 1.5,
                        textColor: Color(0xFF696969)),
                  )),
            ));
          });
        });
  }

  deleteDialog(BuildContext context, Process processRow) {
    return showDialog(
        context: context,
        builder: (BuildContext _bc1) {
          return StatefulBuilder(
              builder: (BuildContext _bc2, StateSetter setState) {
            return Center(
                child: BaseNewarcPopup(
              title: 'Elimina ditta',
              buttonText: 'Rimuovi',
              onPressed: () async {
                widget.project!.vendorAndProfessionals!
                    .removeAt(processRow.indexPlace!);
                if (widget.project!.vendorAndProfessionals!.length > 0) {
                  for (var i = 0;
                      i < widget.project!.vendorAndProfessionals!.length;
                      i++) {
                    widget.project!.vendorAndProfessionals![i].indexPlace = i;
                  }
                }

                final FirebaseFirestore _db = FirebaseFirestore.instance;

                try {
                  await _db
                      .collection(appConfig.COLLECT_NEWARC_PROJECTS)
                      .doc(widget.project!.id)
                      .update(widget.project!.toMap());

                  setState(() {
                    progressMessage = 'Saved!';
                  });
                } catch (e) {
                  setState(() {
                    progressMessage = 'Error';
                  });
                }

                setInitialValues();

                // Navigator.pop(context);
              },
              column: Container(
                  height: 99,
                  width: 465,
                  child: Center(
                    child: NarFormLabelWidget(
                        overflow: TextOverflow.visible,
                        label: 'Vuoi davvero eliminare questa ditta?',
                        textAlign: TextAlign.center,
                        fontSize: 18,
                        fontWeight: '600',
                        height: 1.5,
                        textColor: Color(0xFF696969)),
                  )),
            ));
          });
        });
  }

  resetPenalty() {
    contPenalty.text = '';
    jobStart = DateTime.now().millisecondsSinceEpoch;
    jobEnd = DateTime.now().millisecondsSinceEpoch;

    DateTime startDate = DateTime.fromMillisecondsSinceEpoch(jobStart!);
    DateTime endDate = DateTime.fromMillisecondsSinceEpoch(jobEnd!);
    contJobStart.text = (startDate.day > 9
            ? startDate.day.toString()
            : '0' + startDate.day.toString()) +
        '/' +
        (startDate.month > 9
            ? startDate.month.toString()
            : '0' + startDate.month.toString()) +
        '/' +
        startDate.year.toString();
    contJobEnd.text = (endDate.day > 9
            ? endDate.day.toString()
            : '0' + endDate.day.toString()) +
        '/' +
        (endDate.month > 9
            ? endDate.month.toString()
            : '0' + endDate.month.toString()) +
        '/' +
        endDate.year.toString();
  }

  int installmentCounter = 0;
  List<Map> installmentWidget = [];
  resetInstallmentWidgetVars() {
    installmentCounter = 0;
    // installmentsInputs.clear();
    installmentWidget.clear();
  }

  mystate(){
    setState(() {
      
    });
  }

  StateSetter? customSetState;
  
  addInstallmentField(PaymentInstallment installment, bool isEdit) {
  try {
    Map<String, dynamic> _temp = {
      'counter': installmentCounter,
      'controller': TextEditingController(),
      'labelController': TextEditingController(),
      'isRemoved': false,
      'label': 'Rata ' + (installmentCounter + 1).toString(),
      'widget': SizedBox.shrink(),
      'uniqueId': installment.uniqueId,
      'paidAmount': installment.paidAmount,
      'invoicePath': installment.invoicePath,
      'paidOn': installment.paidOn,
      'status': installment.status,
    };

    installmentWidget.add(_temp);
    int counter = installmentCounter;

    int tmpCounter = 0;
    for (var i = 0; i < installmentWidget.length; i++) {
        if (installmentWidget[i]['isRemoved'] == false) {
          installmentWidget[i]['labelController'].text =
                'Rata ' + (tmpCounter+1).toString();
          installmentWidget[i]['label'] = 'Rata ' + (tmpCounter + 1).toString();
          tmpCounter++;
        }
      }

    // Set the controller's text based on isEdit flag
    if (isEdit) {
      installmentWidget[counter]['controller'].text = installment.amount.toString();
      installmentWidget[counter]['uniqueId'] = installment.uniqueId;
      installmentWidget[counter]['paidAmount'] = installment.paidAmount;
      installmentWidget[counter]['invoicePath'] = installment.invoicePath;
      installmentWidget[counter]['paidOn'] = installment.paidOn;
      installmentWidget[counter]['status'] = installment.status;
    } else {
      installmentWidget[counter]['controller'].text = '';
    }

    // Define the widget with dynamic label
    Widget _widget = Container(
      width: 120,
      margin: EdgeInsets.only(right: 7),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          TextField(
              controller: installmentWidget[counter]['labelController'],
              enabled: false,
              
              decoration: InputDecoration(
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                disabledBorder: InputBorder.none,
                errorBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(vertical: 0, horizontal: 0),
                isDense: true, // Reduce height of the text field
              ),
              style: TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w500,
                fontFamily: 'Raleway-500',
                color: Color(0xff696969)
              ),
            ),
          SizedBox(height: 4),
          Row(
            children: [
              
              CustomTextFormField(
                label: '',
                controller: installmentWidget[counter]['controller'],
                suffixIcon: Column(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    NarFormLabelWidget(
                      label: '€',
                      fontSize: 14,
                      textColor: Color.fromRGBO(181, 181, 181, 1),
                    ),
                  ],
                ),
                validator: (value) {
                  if (value!.isEmpty) {
                    return 'Required!';
                  }
                  if (!isNumber(value)) {
                    return 'Not a valid value';
                  }
                  return null;
                },
              ),
            ],
          ),
          installmentCounter == 0
              ? SizedBox.shrink()
              : MouseRegion(
                  cursor: SystemMouseCursors.click,
                  child: GestureDetector(
                    child: NarFormLabelWidget(
                      label: 'Elimina',
                      textColor: Color(0xff8F8F8F),
                      fontSize: 11,
                      textDecoration: TextDecoration.underline,
                    ),
                    onTap: () {
                      try {
                        // Mark the installment as removed
                        installmentWidget[counter]['isRemoved'] = true;

                        // Update the labels for the remaining installments
                        int tmpCounter = 1;
                        
                        for (var i = 0; i < installmentWidget.length; i++) {
                          if ( installmentWidget[i]['isRemoved'] == false ) {
                            
                            customSetState!(() {
                              installmentWidget[i]['labelController'].text = 'Rata ' + tmpCounter.toString();
                              installmentWidget[i]['label'] = 'Rata ' + tmpCounter.toString();
                            });
                            
                            tmpCounter++;
                          }
                        }

                        setState(() {});
                      } catch (e, s) {
                        print({'e': e, 's': s});
                      }
                    },
                  ),
                ),
        ],
      ),
    );

    // Assign the created widget to the installment
    installmentWidget[counter]['widget'] = _widget;
    
    // Increment the counter for the next installment
    installmentCounter++;
  } catch (e, s) {
    print({'well': e, 's': s});
  }

  // Trigger a UI update
  setState(() {});
}


  

  addVendorPopup(BuildContext context, Process processRow) async {
    contIndexPlace.text = processRow.indexPlace!.toString();
    contVendor.text = processRow.vendorUserId!;
    contActivity.text = processRow.activity!;

    if( contActivity.text != '' ) {
      _suppliers.clear();
      _suppliers.add({'value': '', 'label': ''});
      
      widget.suppliers!.map((e) {
        if( e.activities!.contains(contActivity.text) ) {
          _suppliers.add({'value': e.id, 'label': e.name! + ' ' + e.formationType!});
        }
      }).toList();
    }
                                    


    contCost.text = processRow.cost!.toString();
    contAgreedMilestones.text = processRow.agreedInstallments!.toString();
    isMainContractor = processRow.contractor!;
    hasPenalty = processRow.hasPenalty!;
    isSingle = processRow.isUnique!;
    contPenalty.text = processRow.agreedPenalty!.toString();

    if( processRow.jobStartTimestamp! > 0 ) {
      contJobStart.text = getFormattedDate(processRow.jobStartTimestamp!);
    } else {
      contJobStart.text = '';
    }

    if( processRow.jobEndTimestamp! > 0 ) {
      contJobEnd.text = getFormattedDate(processRow.jobEndTimestamp!);
    } else {
      contJobEnd.text = '';
    }
    
    
    
    jobStart = processRow.jobStartTimestamp;
    jobEnd = processRow.jobEndTimestamp;

    installmentsInputs.clear();
    installmentWidget.clear();

    if (processRow.installments!.length > 0) {
      try {
        resetInstallmentWidgetVars();
        for (var i = 0; i < processRow.installments!.length; i++) {
          addInstallmentField(processRow.installments![i], true);
        }
      } catch (e, s) {
        print({ e, s});
      }
    }
    return showDialog(
        context: context,
        builder: (BuildContext _bc1) {
          return StatefulBuilder(
              builder: (BuildContext _bc2, StateSetter _setState) {

            customSetState = setState;
            return Center(
                child: BaseNewarcPopup(
              title: 'Opzioni ditta',
              buttonText: 'Salva',
              onPressed: () async {
                _setState(() {
                  progressMessage = 'Salvataggio in corso...';
                });

                int processIndex = int.parse(contIndexPlace.text);
                bool isEditMode = false;
                if (processIndex >= 0) {
                  isEditMode = true;
                }

                bool hasError = false;

                /* If there is already an existing Process then a Unique can not be added */
                if (isEditMode == false) {
                  if (widget.project!.vendorAndProfessionals!.length > 0 &&
                      isSingle == true) {
                    Navigator.pop(context);
                    messageDialog(context, 'Attenzione!',
                        "Non puoi applicare l’attributo “Ditta Unica” perchè\n sono già state inserite altre ditte nel progetto.\nSe possibile, rimuovi prima le altre ditte.");
                    return false;
                  }
                }

                widget.project!.vendorAndProfessionals?.forEach((process){});

                List<PaymentInstallment> installments = [];
                double instalmentTotal = 0;
                for (int i = 0; i < installmentWidget.length; i++) {
                  if (installmentWidget[i]['isRemoved'] == false) {
                    instalmentTotal += double.parse(installmentWidget[i]['controller'].text);

                    // Check if uniqueId already exists, if not, generate a new one
                    String uId = (installmentWidget[i]['uniqueId'] != null && installmentWidget[i]['uniqueId'] != "") ? installmentWidget[i]['uniqueId'] :generateRandomString(20);

                    PaymentInstallment pi = PaymentInstallment({
                      'uniqueId': uId,
                      'paidAmount': installmentWidget[i]['paidAmount'] ?? 0.0,
                      'amount': double.parse(installmentWidget[i]['controller'].text),
                      'status': installmentWidget[i]['status'] ?? false,
                      'paidOn': installmentWidget[i]['paidOn'] ?? 0,
                      'counter': i + 1,
                      'isEndOfList': i == installmentWidget.length - 1 ? true : false,
                      'invoicePath': installmentWidget[i]['invoicePath'] ?? {},
                      'invoiceImages': installmentWidget[i]['invoiceImages'] ?? [],
                      'isPaid': installmentWidget[i]['isPaid'] ?? false,
                    });

                    installments.add(pi);
                  }
                }

                contCost.text = instalmentTotal.toString();

                Process process = new Process({
                  'vendorUserId': contVendor.text,
                  'activity': contActivity.text,
                  'cost':
                      isNumber(contCost.text) ? double.parse(contCost.text) : 0,
                  'agreedInstallments': installments.length,
                  'installments': installments,
                  'contractor': isMainContractor,
                  'hasPenalty': hasPenalty,
                  'agreedPenalty': isNumber(contPenalty.text) == true
                      ? double.parse(contPenalty.text)
                      : 0,
                  'jobStartTimestamp': jobStart,
                  'jobEndTimestamp': jobEnd,
                  'isUnique': isSingle,
                });

                /* Add new mode */
                if (!isEditMode) {
                  process.indexPlace =
                      widget.project!.vendorAndProfessionals!.length;
                  widget.project!.vendorAndProfessionals!.add(process);
                } else {
                  /* Edit mode */
                  process.indexPlace = processIndex;
                  widget.project!.vendorAndProfessionals![processIndex] =
                      process;
                }

                final FirebaseFirestore _db = FirebaseFirestore.instance;

                try {
                  await _db
                      .collection(appConfig.COLLECT_NEWARC_PROJECTS)
                      .doc(widget.project!.id)
                      .update(widget.project!.toMap());

                  widget.updateProject!(widget.project);

                  _setState(() {
                    progressMessage = 'Saved!';
                  });
                } catch (e) {
                  _setState(() {
                    progressMessage = 'Error';
                  });
                }

                setInitialValues();

                return true;
              },
              column: Container(
                height: 500,
                width: 650,
                child: ListView(
                  children: [
                    
                    NarFormLabelWidget(
                      label: 'Pagamenti',
                      fontSize: 16,
                      fontWeight: 'bold',
                      textColor: Colors.black,
                    ),
                    SizedBox(height: 10),
                    Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          flex: 3,
                          child: Wrap(
                            runSpacing: 8.0,
                            // mainAxisSize: MainAxisSize.min,

                            children: installmentWidget
                                .map((e) {
                                  if( e['isRemoved'] == false ) 
                                    return e['widget'] as Widget;
                                  else 
                                    return SizedBox(width: 0,);

                                })
                                .toList(),
                          ) ,
                        ),
                        Expanded(
                          flex: 1,
                          child: Column(
                            // mainAxisSize: MainAxisSize.max,
                            crossAxisAlignment: CrossAxisAlignment.end,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SizedBox(height: 11,),
                              MouseRegion(
                                cursor: SystemMouseCursors.click,
                                child: GestureDetector(
                                  child: NarFormLabelWidget(
                                    label: 'Aggiungi rata',
                                    textColor: Theme.of(context).primaryColor,
                                    fontSize: 14,
                                    textDecoration: TextDecoration.underline,
                                  ),
                                  onTap: (){
                                    int milestones =
                                      int.tryParse(contAgreedMilestones.text)!;
                                    milestones++;
                                    contAgreedMilestones.text = milestones.toString();
                                    addInstallmentField(PaymentInstallment.empty(), false );
                                    _setState(() {});
                                  }
                                  )
                              )
                             
                              // NarSelectBoxWidget(
                              //   options: ['1', '2', '3'],
                              //   controller: contAgreedMilestones,
                              //   validationType: 'required',
                              //   parametersValidate: 'Required!',
                              //   onChanged: () {
                              //     List<PaymentInstallment>
                              //         tmpPaymentIntallment = [];
                              //     for (var t = 0;
                              //         t < int.parse(contAgreedMilestones.text);
                              //         t++) {
                              //       tmpPaymentIntallment
                              //           .add(PaymentInstallment.empty());
                              //     }
                              //     addInstallmentField(
                              //         tmpPaymentIntallment, true);
                              //     setState(() {});
                              //   },
                              // ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 20),
                    NarFormLabelWidget(
                      label: 'Opzioni',
                      fontSize: 16,
                      fontWeight: 'bold',
                      textColor: Colors.black,
                    ),
                    SizedBox(height: 10),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Padding(
                          // padding: const EdgeInsets.only( top: 6.0, bottom: 18, left: 0),
                          padding: const EdgeInsets.all(0),
                          child: Switch(
                            // This bool value toggles the switch.
                            value: isMainContractor,
                            activeColor: Theme.of(context).primaryColor,

                            onChanged: disableIsContractorSwitch
                                ? null
                                : (bool value) async {
                                    _setState(() {
                                      isMainContractor = value;

                                      /* If penalty has been disabled then empty the penalty amount */
                                      if (value == false) {
                                        hasPenalty = false;
                                        resetPenalty();
                                      }
                                    });
                                  },
                          ),
                        ),
                        SizedBox(
                          width: 5,
                        ),
                        NarFormLabelWidget(
                            label: 'Appaltatore',
                            fontSize: 14,
                            textColor: Colors.black)
                      ],
                    ),
                    isMainContractor
                        ? Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Expanded(
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    SizedBox(
                                      width: 20,
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.all(0),
                                      child: Switch(
                                        value: hasPenalty,
                                        activeColor:
                                            Theme.of(context).primaryColor,
                                        onChanged: (bool value) async {
                                          _setState(() {
                                            hasPenalty = value;

                                            /* If penalty has been disabled then empty the penalty amount */
                                            if (value == false) {
                                              resetPenalty();
                                            }
                                          });
                                        },
                                      ),
                                    ),
                                    SizedBox(
                                      width: 5,
                                    ),
                                    NarFormLabelWidget(
                                        label: 'Penale',
                                        fontSize: 14,
                                        textColor: Colors.black)
                                  ],
                                ),
                              ),
                              hasPenalty
                                  ? CustomTextFormField(
                                      label: "Penale concordata",
                                      controller: contPenalty,
                                      suffixIcon: Column(
                                        mainAxisSize: MainAxisSize.max,
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          NarFormLabelWidget(
                                            label: '€/giorno ',
                                            fontSize: 14,
                                            textColor: Color.fromRGBO(
                                                181, 181, 181, 1),
                                          ),
                                        ],
                                      ),
                                      validator: (value) {
                                        if (value == '') {
                                          return 'Required!';
                                        }
                                        if (isNumber(value) == false) {
                                          return 'Not a valid value';
                                        }

                                        return null;
                                      },
                                    )
                                  : SizedBox(
                                      height: 0,
                                    ),
                              SizedBox(width: 7),
                              hasPenalty
                                  ? CustomTextFormField(
                                      label: "Inizio lavori concordata",
                                      controller: contJobStart,
                                      suffixIcon: Container(
                                        height: 17,
                                        width: 17,
                                        // margin: EdgeInsets.only(left: 2),
                                        decoration: BoxDecoration(
                                          color: Colors.transparent,
                                          borderRadius:
                                              BorderRadius.circular(4),
                                        ),
                                        padding:
                                            EdgeInsets.symmetric(horizontal: 10),
                                        child: Image.asset(
                                          'assets/icons/calendar.png',
                                          color: Color(0xff7B7B7B),
                                        ),
                                      ),
                                      // validationMessage: 'Required!',
                                      validator: (value) {
                                        if (value == '') {
                                          return 'Required!';
                                        }

                                        return null;
                                      },
                                      onTap: () async {
                                        DateTime? pickedDate =
                                            await showDatePicker(
                                                context: context,
                                                initialDate: contJobStart
                                                            .text ==
                                                        ''
                                                    ? DateTime.now()
                                                    : DateTime.tryParse(
                                                        formatDateForParsing(
                                                            contJobStart
                                                                .text))!,
                                                firstDate: DateTime(1950),
                                                lastDate: DateTime(2300));

                                        if (pickedDate != null) {
                                          jobStart =
                                              pickedDate.millisecondsSinceEpoch;
                                          String formattedDate =
                                              DateFormat('dd/MM/yyyy')
                                                  .format(pickedDate);
                                          _setState(() {
                                            contJobStart.text =
                                                formattedDate; //set output date to TextField value.
                                          });
                                        } else {}
                                      },
                                    )
                                  : SizedBox(height: 0),
                              SizedBox(width: 7),
                              hasPenalty
                                  ? CustomTextFormField(
                                      label: "Fine lavori concordata",
                                      controller: contJobEnd,
                                      suffixIcon: Container(
                                        height: 17,
                                        width: 17,
                                        // margin: EdgeInsets.only(left: 2),
                                        decoration: BoxDecoration(
                                          color: Colors.transparent,
                                          borderRadius:
                                              BorderRadius.circular(4),
                                        ),
                                        padding: EdgeInsets.symmetric(
                                            horizontal: 10),
                                        child: Image.asset(
                                          'assets/icons/calendar.png',
                                          color: Color(0xff7B7B7B),
                                        ),
                                      ),
                                      // validationMessage: 'Required!',
                                      validator: (value) {
                                        if (value == '') {
                                          return 'Required!';
                                        }

                                        return null;
                                      },
                                      onTap: () async {
                                        DateTime? pickedDate =
                                            await showDatePicker(
                                                context: context,
                                                initialDate: contJobEnd.text ==
                                                        ''
                                                    ? DateTime.now()
                                                    : DateTime.tryParse(
                                                        formatDateForParsing(
                                                            contJobEnd.text))!,
                                                firstDate: DateTime(1950),
                                                lastDate: DateTime(2300));

                                        if (pickedDate != null) {
                                          jobEnd =
                                              pickedDate.millisecondsSinceEpoch;

                                          String formattedDate =
                                              DateFormat('dd/MM/yyyy')
                                                  .format(pickedDate);
                                          _setState(() {
                                            contJobEnd.text =
                                                formattedDate; //set output date to TextField value.
                                          });
                                        } else {}
                                      },
                                    )
                                  : SizedBox(height: 85),
                            ],
                          )
                        : SizedBox(height: 0),
                    /*Row(
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(0),
                          child: Switch(
                            // This bool value toggles the switch.
                            value: isSingle,
                            activeColor: Theme.of(context).primaryColor,
                            onChanged: disableIsUnique
                                ? null
                                : (bool value) async {
                                    _setState(() {
                                      isSingle = value;
                                    });
                                  },
                          ),
                        ),
                        SizedBox(
                          width: 5,
                        ),
                        NarFormLabelWidget(
                          label: 'Ditta unica',
                          fontSize: 15,
                          textColor: Colors.black,
                          fontWeight: 'bold',
                        )
                      ],
                    ),
                    SizedBox(
                      height: 20,
                    ),*/
                    NarFormLabelWidget(label: progressMessage)
                  ],
                ),
              ),
            ));
          });
        });
  }
}
