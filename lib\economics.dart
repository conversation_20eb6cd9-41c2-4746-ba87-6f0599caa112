import 'package:newarc_platform/classes/immaginaProject.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;

const gSFUpperLimit = 300;

class ImmaginaProjectEconomics {
  final ImmaginaProject project;
  static const double VATPerc = .22;
  static Map<String, dynamic> noWatermarkCost = {'price': 150, 'stripeId': appConfig.isProduction? "price_1Qtr6oBCCTTeQsGGB1F1fPLf" : 'price_1QRt3lBCCTTeQsGGtNv3V3f2'};
  static Map<String, Map<String, dynamic>> renderRequestFee = {
    'small': {'price': 399, 'stripeId': appConfig.isProduction? "price_1QtrDaBCCTTeQsGG7Rn7d7me" : 'price_1Qut1BBCCTTeQsGGiT5ckrO1'},
    'medium': {'price': 449, 'stripeId': appConfig.isProduction? "price_1ROExKBCCTTeQsGGf9twRFzt" : 'price_1Qut1kBCCTTeQsGGNBkhGOwq'},
    'large': {'price': 499, 'stripeId': appConfig.isProduction? "price_1ROEwiBCCTTeQsGGnirFWY4h" : 'price_1Qut3IBCCTTeQsGGX8wCk4CR'},
    'xlarge': {'price': 549, 'stripeId': appConfig.isProduction? "price_1ROEw7BCCTTeQsGGbe405Q9L" : 'price_1Qut3zBCCTTeQsGGdBnd0LXZ'},
    'xxlarge': {'price': 599, 'stripeId': appConfig.isProduction? "price_1QucNGBCCTTeQsGG3TqCAezj" : 'price_1Qut4fBCCTTeQsGGQ7pI5ZOu'},
    'xxxlarge': {'price': 649, 'stripeId': appConfig.isProduction? "price_1QucO9BCCTTeQsGGOaxSy1Nz" : 'price_1Qut5NBCCTTeQsGGGnpnG6PF'},
  };
  static Map<String, Map<String, dynamic>> newarcMediaFee = {
    'small': {'price': 79, 'stripeId': appConfig.isProduction? "price_1QtrDhBCCTTeQsGGeSDYYxWo" : 'price_1Qut6PBCCTTeQsGGDobGb3Fe'},
    'medium': {'price': 89, 'stripeId': appConfig.isProduction? "price_1QtrDjBCCTTeQsGGdrNHF9Dv" : 'price_1Qut7DBCCTTeQsGG8coWFDwT'},
    'large': {'price': 99, 'stripeId': appConfig.isProduction? "price_1QtrDlBCCTTeQsGGXOout35e" : 'price_1Qut7kBCCTTeQsGG600MfHCe'},
    'xlarge': {'price': 109, 'stripeId': appConfig.isProduction? "price_1QucD8BCCTTeQsGGGstz2Fkm" : 'price_1Qut8OBCCTTeQsGGoxk5wIWA'},
    'xxlarge': {'price': 119, 'stripeId': appConfig.isProduction? "price_1QucUpBCCTTeQsGGrOq6uLLJ" : 'price_1Qut9QBCCTTeQsGGZKo5r49E'},
    'xxxlarge': {'price': 129, 'stripeId': appConfig.isProduction? "price_1QucVTBCCTTeQsGGWGRQ7aXh" : 'price_1QutEkBCCTTeQsGGpbV7HyB8'},
  };
  static Map<String, Map<String, dynamic>> newarcRilieviFee = {
    'small': {'price': 149, 'stripeId': appConfig.isProduction? "price_1QucFvBCCTTeQsGGecJiXHjm" : 'price_1QutGOBCCTTeQsGGLIhR6nv0'},
    'medium': {'price': 199, 'stripeId': appConfig.isProduction? "price_1QucFxBCCTTeQsGGNfsFVSBU" : 'price_1QutHFBCCTTeQsGGCgfTrAil'},
    'large': {'price': 249, 'stripeId': appConfig.isProduction? "price_1QucFzBCCTTeQsGGfqQdDNUJ" : 'price_1QutHoBCCTTeQsGGXtJmQQb2'},
    'xlarge': {'price': 299, 'stripeId': appConfig.isProduction? "price_1QucG0BCCTTeQsGGs3IGIacO" : 'price_1QutJ4BCCTTeQsGGQpOVPxoB'},
    'xxlarge': {'price': 349, 'stripeId': appConfig.isProduction? "price_1QucYaBCCTTeQsGG9mvqHuTX" : 'price_1QutJvBCCTTeQsGG4W214aPv'},
    'xxxlarge': {'price': 399, 'stripeId': appConfig.isProduction? "price_1QucZcBCCTTeQsGGVfkws0ka" : 'price_1QutKnBCCTTeQsGG6LHsH1vb'},
  };


  static List<Map<String, dynamic>> generatePricingData() {
    return [
      {
        "title": "Request Fee",
        "prices": [
          {"label": "Immobili fino a 49mq", "price": "${renderRequestFee['small']?['price']}€ + iva"},
          {"label": "Immobili da 50mq a 99mq", "price": "${renderRequestFee['medium']?['price']}€ + iva"},
          {"label": "Immobili da 100mq a 149mq", "price": "${renderRequestFee['large']?['price']}€ + iva"},
        ],
        "extraPrices": [
          {"label": "Immobili da 150mq a 199mq", "price": "${renderRequestFee['xlarge']?['price']}€ + iva"},
          {"label": "Immobili da 200mq a 249mq", "price": "${renderRequestFee['xxlarge']?['price']}€ + iva"},
          {"label": "Immobili da 250mq a 299mq", "price": "${renderRequestFee['xxxlarge']?['price']}€ + iva"},
        ],
      },
      {
        "title": "Newarc Media",
        "prices": [
          {"label": "Immobili fino a 49mq", "price": "${newarcMediaFee['small']?['price']}€ + iva"},
          {"label": "Immobili da 50mq a 99mq", "price": "${newarcMediaFee['medium']?['price']}€ + iva"},
          {"label": "Immobili da 100mq a 149mq", "price": "${newarcMediaFee['large']?['price']}€ + iva"},
        ],
        "extraPrices": [
          {"label": "Immobili da 150mq a 199mq", "price": "${newarcMediaFee['xlarge']?['price']}€ + iva"},
          {"label": "Immobili da 200mq a 249mq", "price": "${newarcMediaFee['xxlarge']?['price']}€ + iva"},
          {"label": "Immobili da 250mq a 299mq", "price": "${newarcMediaFee['xxxlarge']?['price']}€ + iva"},
        ],
      },
      {
        "title": "Newarc Rilievi",
        "prices": [
          {"label": "Immobili fino a 49mq", "price": "${newarcRilieviFee['small']?['price']}€ + iva"},
          {"label": "Immobili da 50mq a 99mq", "price": "${newarcRilieviFee['medium']?['price']}€ + iva"},
          {"label": "Immobili da 100mq a 149mq", "price": "${newarcRilieviFee['large']?['price']}€ + iva"},
        ],
        "extraPrices": [
          {"label": "Immobili da 150mq a 199mq", "price": "${newarcRilieviFee['xlarge']?['price']}€ + iva"},
          {"label": "Immobili da 200mq a 249mq", "price": "${newarcRilieviFee['xxlarge']?['price']}€ + iva"},
          {"label": "Immobili da 250mq a 299mq", "price": "${newarcRilieviFee['xxxlarge']?['price']}€ + iva"},
        ],
      },
    ];
  }

  ImmaginaProjectEconomics({required this.project}) {
    if (project.grossSquareFootage == null) {
      throw ArgumentError('The "grossSquareFootage" attribute in ImmaginaProject must not be null.');
    }
  }

  List<String> getStripePriceIds(bool isSubscriptionOver) {
    List<String> priceIds = [];
    String? size;
    if (this.project.grossSquareFootage! < 50) {
      size = 'small';
    } else if (this.project.grossSquareFootage! < 100) {
      size = 'medium';
    } else if (this.project.grossSquareFootage! < 150) {
      size = 'large';
    } else if (this.project.grossSquareFootage! < 200) {
      size = 'xlarge';
    } else if (this.project.grossSquareFootage! < 250) {
      size = 'xxlarge';
    } else if (this.project.grossSquareFootage! < gSFUpperLimit) {
      size = 'xxxlarge';
    }

    if (isSubscriptionOver) {
      priceIds.add(renderRequestFee[size]!['stripeId']);
    }

    if (this.project.wantsNewarcPictures ?? false) {
      priceIds.add(newarcMediaFee[size]!['stripeId']);
    }
    if (this.project.wantsNewarcPlanimetry ?? false) {
      priceIds.add(newarcRilieviFee[size]!['stripeId']);
    }
    if (!this.project.wantsWatermark) {
      priceIds.add(noWatermarkCost['stripeId']);
    }
    return priceIds;
  }

  int? computeRenderRequestFee() {
    var tot;
    if (this.project.grossSquareFootage! < 50) {
      tot = renderRequestFee['small']!['price'];
    } else if (this.project.grossSquareFootage! < 100) {
      tot = renderRequestFee['medium']!['price'];
    } else if (this.project.grossSquareFootage! < 150) {
      tot = renderRequestFee['large']!['price'];
    } else if (this.project.grossSquareFootage! < 200) {
      tot = renderRequestFee['xlarge']!['price'];
    } else if (this.project.grossSquareFootage! < 250) {
      tot = renderRequestFee['xxlarge']!['price'];
    } else if (this.project.grossSquareFootage! < gSFUpperLimit) {
      tot = renderRequestFee['xxxlarge']!['price'];
    }

    if (!this.project.wantsWatermark) {
      tot += noWatermarkCost['price'];
    }
    return tot;
  }

  int? computeNewarcMediaFee() {
    int tot = 0;
    if (this.project.wantsNewarcPictures ?? false) {
      if (this.project.grossSquareFootage! < 50) {
        tot = newarcMediaFee['small']!['price'];
      } else if (this.project.grossSquareFootage! < 100) {
        tot = newarcMediaFee['medium']!['price'];
      } else if (this.project.grossSquareFootage! < 150) {
        tot = newarcMediaFee['large']!['price'];
      } else if (this.project.grossSquareFootage! < 200) {
        tot = newarcMediaFee['xlarge']!['price'];
      } else if (this.project.grossSquareFootage! < 250) {
        tot = newarcMediaFee['xxlarge']!['price'];
      } else if (this.project.grossSquareFootage! < gSFUpperLimit) {
        tot = newarcMediaFee['xxxlarge']!['price'];
      }
    }
    return tot;
  }

  int? computeNewarcRilieviFee() {
    int tot = 0;
    if (this.project.wantsNewarcPlanimetry ?? false) {
      if (this.project.grossSquareFootage! < 50) {
        tot = newarcRilieviFee['small']!['price'];
      } else if (this.project.grossSquareFootage! < 100) {
        tot = newarcRilieviFee['medium']!['price'];
      } else if (this.project.grossSquareFootage! < 150) {
        tot = newarcRilieviFee['large']!['price'];
      } else if (this.project.grossSquareFootage! < 200) {
        tot = newarcRilieviFee['xlarge']!['price'];
      } else if (this.project.grossSquareFootage! < 250) {
        tot = newarcRilieviFee['xxlarge']!['price'];
      } else if (this.project.grossSquareFootage! < gSFUpperLimit) {
        tot = newarcRilieviFee['xxxlarge']!['price'];
      }
    }
    return tot;
  }

  double? computeTotalVAT() {
    if (this.project.grossSquareFootage! < gSFUpperLimit) {
      return (computeRenderRequestFee()! + computeNewarcMediaFee()! + computeNewarcRilieviFee()!) * (VATPerc);
    }
  }

  double? computeTotalCost() {
    if (this.project.grossSquareFootage! < gSFUpperLimit) {
      return (computeRenderRequestFee()! + computeNewarcMediaFee()! + computeNewarcRilieviFee()!) * (1 + VATPerc);
    }
  }

  double? computeNewarcMediaRilieviFeeTotalVAT() {
    if (this.project.grossSquareFootage! < gSFUpperLimit) {
      return (computeNewarcMediaFee()! + computeNewarcRilieviFee()!) * (VATPerc);
    }
  }

  double? computeNewarcMediaRilieviFeeTotalCost() {
    if (this.project.grossSquareFootage! < gSFUpperLimit) {
      return (computeNewarcMediaFee()! + computeNewarcRilieviFee()!) * (1 + VATPerc);
    }
  }
}
