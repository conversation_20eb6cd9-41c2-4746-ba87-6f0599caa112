import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:newarc_platform/classes/immaginaProject.dart';
import 'package:newarc_platform/classes/projectJob.dart';
import 'package:newarc_platform/classes/user.dart';
import 'package:newarc_platform/pages/work/immagina_project_review/immagina_project_review_controller.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/utils/common_utils.dart';
import 'package:newarc_platform/utils/storage.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/button.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/widget/UI/tab/status_widget.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/multi-select-dropdown.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/widget/UI/link.dart';
import 'package:newarc_platform/widget/UI/select-box.dart';
import 'package:newarc_platform/widget/UI/tab/common_dropdown_widget.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:js' as js;
import '../../../classes/agencyUser.dart';
import '../../../functions/various.dart';
import '../../../widget/UI/base_newarc_button.dart';
import '../../../widget/UI/base_newarc_popup.dart';
import '../../../widget/UI/color-bg-dropdown.dart';
import '../../../widget/UI/image_dropdown.dart';

class ImmaginaProjectReview extends StatefulWidget {
  const ImmaginaProjectReview({
    super.key,
    required this.newarcUser,
    this.updateViewCallback,
    this.projectArguments = const {},
    this.projectFirebaseId,
    this.initialFetchProperties,
    this.isFromRequest = true,
    this.isFromProjectArchive = false,
  });

  final NewarcUser newarcUser;

  final Function? updateViewCallback;
  final Map? projectArguments;

  final String? projectFirebaseId;
  final bool? isFromRequest;
  final bool? isFromProjectArchive;


  final Function? initialFetchProperties;

  @override
  State<ImmaginaProjectReview> createState() => _ImmaginaProjectReviewState();
}

class _ImmaginaProjectReviewState extends State<ImmaginaProjectReview> {
  bool loading = true;
  ImmaginaProject immaginaProject = ImmaginaProject.empty();
  bool isExpanded = true;

  List<String> frazionamentiList = ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"];
  TextEditingController selectedFrazionamenti = TextEditingController(text: "1");
  List<String> status = [CommonUtils.completato.toCapitalized(), CommonUtils.inLavorazione.toCapitalized()];
  String selectedStatus = CommonUtils.inLavorazione.toCapitalized();

  List<NewarcUser> projectRenderistList = [];
  List<NewarcUser> allRenderistList = [];
  // TextEditingController renderistNotesController = TextEditingController();

  List actionDropdownOptions = [{
            'value': 'edit_team',
            'label': 'Modifica team',
            'image': 'assets/icons/account.svg',
            'iconColor': AppColor.greyColor
          },
          {
            'value': 'comment',
            'label': 'Commenti',
            'image': 'assets/icons/comment-bubble.svg',
            'iconColor': AppColor.greyColor
          },
          {
            'value': 'edit_priority',
            'label': 'Modifica priorità',
            'image': 'assets/icons/up_down.svg',
            'iconColor': AppColor.greyColor
          },
          {
            'value': 'fractionation',
            'label': 'Frazionamento',
            'image': 'assets/icons/frazionamento.svg',
            'iconColor': AppColor.greyColor
          },
        ];

  List<Map> priorityStatus = [
    {
      'value': 'normale',
      'label': 'Normale',
      'bgColor': Color(0xFFFEC600),
      'textColor': Colors.black
    },
    {
      'value': 'bassa',
      'label': 'Bassa',
      'bgColor': Color(0xff39C14F),
      'textColor': Colors.black
    },
    {
      'value': 'alta',
      'label': 'Alta',
      'bgColor': Color(0xffDD0000),
      'textColor': Colors.black
    },
  ];

  List<InsideProject> insideRequest = <InsideProject>[
    InsideProject(value: "Localizzazione", key: 'localizzazione'),
    InsideProject(value: "Info generali", key: 'info_generali'),
    InsideProject(value: "Descrizione", key: 'descrizione'),
    InsideProject(value: "Caratteristiche", key: 'caratteristiche'),
    InsideProject(value: "Planimetria", key: 'planimetria'),
    InsideProject(value: "Fotografie", key: 'fotografie'),
    InsideProject(value: "Info aggiuntive", key: 'indicazioni_speciali'),
  ];

  bool isLoadingProjectCopy = false;

  void openDialog(InsideProject request) {
    switch (request.key) {
      case "localizzazione":
        _locationViewDialog();
        break;
      case "info_generali":
        _motionless();
        break;
      case "descrizione":
        _description();
        break;

      case "caratteristiche":
        _characteristics();
        break;

      case "planimetria":
        _floorPlan();
        break;
      case "fotografie":
        _photographs();
        break;
      case "indicazioni_speciali":
        _problems();
        break;
      default:
        break;
    }
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timestamp)async{
      await initialFetch();
    });
  }

 Future<void> initialFetch() async {
    try {
      DocumentSnapshot<Map<String, dynamic>> collectionSnapshot;

      collectionSnapshot = await FirebaseFirestore.instance
          .collection(appConfig.COLLECT_IMMAGINA_PROJECTS)
          .doc(
            widget.projectFirebaseId,
          )
          .get();

      if (collectionSnapshot.data() != null) {
        immaginaProject.copy(
          ImmaginaProject.fromDocument(collectionSnapshot.data()!, widget.projectFirebaseId!),
        );
        selectedStatus = immaginaProject.requestStatus.toCapitalized();
        /// populate all renderists list
        final renderistUsers = await FirebaseFirestore.instance
          .collection(appConfig.COLLECT_USERS)
          .where('type', isEqualTo: 'newarc')
          .where('role', whereIn: ['master-renderist', 'renderist'])
          .where('isArchived', isEqualTo: false)
          .get();
        for (var userDoc in renderistUsers.docs) {
          final userData = userDoc.data();
          allRenderistList.add(NewarcUser.fromDocument(userData, userDoc.id));
        }
        /// populate projects' renderists team list
        projectRenderistList = allRenderistList.where((user) => immaginaProject.renderistTeamIdList.contains(user.id)).toList();
        // renderistNotesController.text = immaginaProject.renderistTeamNotes ?? "";
      }
    } catch (e) {
      print(e);
    }finally{
      if (mounted) {
        setState(() {
          loading = false;
        });
      }
    }
  }

  popupComments(
    // BuildContext context, 
    // StateSetter initialSetStateFn, 
    ImmaginaProject projectRow){

    TextEditingController _contComment = new TextEditingController();
    String progressMessage = '';
    return showDialog(
      context: context,
      builder: (BuildContext _bc1) {
        return StatefulBuilder(
          builder: (BuildContext _bc2, StateSetter acSetState) {
            return Center(
              child: BaseNewarcPopup(
                title: 'Aggiungi un commento',
                buttonText: 'Salva',
                onPressed: () async {

                  if( _contComment.text == '' ) {
                    acSetState(() {
                      progressMessage = 'Empty text!';
                    });
                    return false;
                  }

                  JobComments comment = JobComments({
                    'index': projectRow.renderistTeamNotes?.length ?? 0,
                    'date': DateTime.now().millisecondsSinceEpoch,
                    'commentorId': widget.newarcUser.id,
                    'message': _contComment.text
                  });
                  projectRow.renderistTeamNotes ??= [];
                  projectRow.renderistTeamNotes!.add(comment);

                  try {
                    await FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS)
                        .doc(projectRow.id)
                        .update({
                          'renderistTeamNotes': projectRow.renderistTeamNotes!.map((e) => e.toMap()).toList()
                        });
                    _contComment.text = '';
                    acSetState(() {
                      progressMessage = 'Saved!';
                    });
                    // setInitialValues();
                    // initialSetStateFn((){});

                    Future.delayed(Duration(seconds: 5), () {
                      acSetState(() {
                        progressMessage = '';
                      });
                    });
                    return false;
                  } catch (e) {
                    acSetState(() {
                      progressMessage = 'Error';
                    });

                    Future.delayed(Duration(seconds: 5), () {
                      acSetState(() {
                        progressMessage = '';
                      });
                    });

                    return false;
                  }
                },
                column: Container(
                  height: 400,
                  width: 550,
                  padding: EdgeInsets.only(left: 40, right: 40),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Container(
                        color: Colors.transparent,
                        child: Row(
                          children: [
                            CustomTextFormField(
                              label: "",
                              controller: _contComment,
                              minLines: 4,
                              controllerFontSize: 13,
                              validator: (value) {
                                return null;
                              },
                            ),
                          ],
                        ),
                      ),
                      SizedBox(
                        height: 5,
                      ),
                      NarFormLabelWidget(
                        label: progressMessage,
                        textAlign: TextAlign.center,
                        textColor: Color(0xff696969),
                        fontSize: 13,
                        fontWeight: '600',
                      ),
                      SizedBox(
                        height: 10,
                      ),
                      Container(
                        height: 200,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(
                            width: 1,
                            color: Color(0xffD7D7D7)
                          )
                        ),
                        padding: EdgeInsets.all(2),
                        child: ListView(
                          shrinkWrap: true,
                          children: (projectRow.renderistTeamNotes?.length ?? 0) > 0
                          ? projectRow.renderistTeamNotes!.map((e) {
                              return FutureBuilder(
                                  future: commentBox( context, projectRow, e, acSetState),
                                  initialData: noCommentBox( 'Loading...'),
                                  builder: (BuildContext _context, AsyncSnapshot snapshot) {
                                    
                                    if (snapshot.hasData) {
                                      return snapshot.data!;
                                    } else {
                                      return NarFormLabelWidget(
                                          label: "Error while loading comment!"
                                        );
                                    }
                                  });
                            }).toList()
                          : [
                              noCommentBox( 'Nessun commento.')
                            ]
                        ),
                      ),
                    ],
                  ),
                )
              )
            );
          }
        );
      }
    ).then((_) {
      setState(() {});
    });
  }

  Widget noCommentBox(String message) {
    return Container(
        decoration: BoxDecoration(
          color: Color(0xFFFFFFFF),
          borderRadius: BorderRadius.circular(7),
        ),
        padding: EdgeInsets.symmetric(vertical: 5, horizontal: 8),
        margin: EdgeInsets.only(bottom: 5),
        width: 350,
        height: 45,
        child: Center(
          child: Row(children: [
            NarFormLabelWidget(
              label: message,
              fontSize: 11,
              textColor: Color(0xffA8A8A8),
              textAlign: TextAlign.left,
            )
          ]),
        ));
  }

  Future<Widget> commentBox(BuildContext context, ImmaginaProject projectRow, JobComments comment, StateSetter _setState) async {
    NewarcUser? user;
    String profilePicture = '';
    bool hasImageError = false;

    if (comment.newarcUser == null) {
      DocumentSnapshot<Map<String, dynamic>> collectionSnapshot;
      collectionSnapshot = await FirebaseFirestore.instance
          .collection(appConfig.COLLECT_USERS)
          .doc(comment.commentorId)
          .get();

      if (collectionSnapshot.data() != null) {
        user = NewarcUser.fromDocument(collectionSnapshot.data()!, collectionSnapshot.id);
        try {
          profilePicture = await printUrl(
              'users/', comment.commentorId!, user.profilePicture!);
        } catch (e) {
          hasImageError = true;
        }
      }
    } else {
      user = comment.newarcUser;
      profilePicture = comment.profilePicture!;
    }
    String commentDate = '';

    if (comment.date! > 0) {
      DateTime commentedOn = DateTime.fromMillisecondsSinceEpoch(comment.date!);
      commentDate = (commentedOn.day > 9
              ? commentedOn.day.toString()
              : '0' + commentedOn.day.toString()) +
          '/' +
          (commentedOn.month > 9
              ? commentedOn.month.toString()
              : '0' + commentedOn.month.toString()) +
          '/' +
          commentedOn.year.toString();
    }

    return Container(
      decoration: BoxDecoration(
        color: Color(0xFFFFFFFF),
        border: Border(
          bottom: BorderSide( color: Color(0xffE2E2E2), width: 1)
        )
        // borderRadius: BorderRadius.circular(7),
      ),
      constraints: BoxConstraints(
        minHeight: 45,
        maxHeight: 60,
      ),
      padding: EdgeInsets.symmetric(vertical: 5),
      margin: EdgeInsets.only(bottom: 5),
      width: 350,
      child: Stack(
        children: [
          ListView(
            shrinkWrap: true,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        height: 30,
                        width: 30,
                        padding: EdgeInsets.all(1),
                        decoration: BoxDecoration(
                          color: Color(0xFFD9D9D9),
                          border: Border.all( width: 1, color: Color(0xffE2E2E2) ),
                          borderRadius: BorderRadius.circular(30),
                        ),
                        // clipBehavior: Clip.hardEdge,
                        child: hasImageError
                            ? Center(
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(30),
                                child: Image.asset('assets/icons/user-icon.png',
                                    height: 29, width: 29, fit: BoxFit.cover),
                              ),
                            )
                            : Center(
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(30),
                                child: Image.network(profilePicture,
                                    height: 29, width: 29, fit: BoxFit.cover,
                                    errorBuilder: (BuildContext context, exception,
                                        StackTrace? stackTrace) {
                                    return ClipRRect(
                                      borderRadius: BorderRadius.circular(30),
                                      child: Image.asset('assets/icons/user-icon.png',
                                          height: 29, width: 29, fit: BoxFit.cover),
                                    );
                                  }),
                              ),
                            ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(left: 5),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            NarFormLabelWidget(
                              label: user!.firstName! + ' ' + user!.lastName! + ' - ' + commentDate,
                              fontSize: 9,
                              textColor: Color(0xFFA0A0A0),
                              letterSpacing: 0.03,
                            ),
                            SizedBox(
                              height: 3,
                            ),
                            NarFormLabelWidget(
                              label: comment.message,
                              fontSize: 11,
                              textColor: Color(0xFF000000),
                              letterSpacing: 0.02,
                            )
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
          Positioned(
            right: 0,
            top: 0,
            child: MouseRegion(
              cursor: SystemMouseCursors.click,
              child: GestureDetector(
                onTap: (){
                  deleteCommentDialog(context, projectRow, comment, _setState);
                },
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(6),
                    color: Colors.white
                  ),
                  padding: EdgeInsets.all(5),
                  margin: EdgeInsets.only(right: 35),
                  child: Image.asset(
                    'assets/icons/trash-process.png',
                    color: Color(0xffAEAEAE),
                    height: 16
                  ),
                  height: 27,
                  width: 27,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  deleteCommentDialog( BuildContext context, ImmaginaProject projectRow, JobComments comment, StateSetter _setState) {
    return showDialog(
      context: context,
      builder: (BuildContext _bc1) {
        return StatefulBuilder(
          builder: (BuildContext _bc2, StateSetter __setState) {
            return Center(
              child: BaseNewarcPopup(
                title: 'Rimuovi commento',
                buttonText: 'Rimuovi',
                onPressed: () async {
                  try{
                    projectRow.renderistTeamNotes!.removeAt(comment.index!);
                    projectRow.renderistTeamNotes!.forEach((element) {
                      element.index = projectRow.renderistTeamNotes!.indexOf(element);
                    });
                    await FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS)
                    .doc(projectRow.id)
                    .update({
                      'renderistTeamNotes': projectRow.renderistTeamNotes!.map((e) => e.toMap()).toList()
                    });
                    _setState((){});
                  } catch (e) {
                    log("---------- ERROR While deleting comment ------> ${e.toString()}");
                  }
                },
                column: Container(
                    height: 99,
                    width: 465,
                    child: Center(
                      child: NarFormLabelWidget(
                          overflow: TextOverflow.visible,
                          label: 'Vuoi davvero eliminare questo commento?',
                          textAlign: TextAlign.center,
                          fontSize: 18,
                          fontWeight: '600',
                          height: 1.5,
                          textColor: Color(0xFF696969)),
                    )),
              )
            );
          }
        );
      });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: LayoutBuilder(
        builder: (context, constraints) {
          return Container(
            constraints: BoxConstraints(
              minHeight: constraints.maxHeight,
            ),
            child: SingleChildScrollView(
              child: loading ? SizedBox(
                height: MediaQuery.of(context).size.height * 0.5,
                child: Center(
                  child: CircularProgressIndicator(
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ) :
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [

                  Row(
                    children: [
                      Expanded(child: _header()),
                    ],
                  ),
                  SizedBox(
                    height: 10,
                  ),
                  _request(),
                  SizedBox(
                    height: 10,
                  ),
                  Row(
                    children: [
                      _announcement(),
                      SizedBox(
                        width: !((widget.isFromProjectArchive != null) && (widget.isFromProjectArchive ?? false))  ? 20 : 0,
                      ),

                      !((widget.isFromProjectArchive != null) && (widget.isFromProjectArchive ?? false)) ?
                      _sale() : SizedBox.shrink(),
                    ],
                  )
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildActionsDropdown() {
    return Container(
      width: 150,
      padding: EdgeInsets.symmetric(vertical: 5.0),
      child: NarImageDropdown(
        controller: TextEditingController(),
        customButton: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 10.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              NarFormLabelWidget(
                label: "Azioni",
                fontSize: 11,
                fontWeight: '600',
                textColor: AppColor.greyColor,
              ),
              Icon(
                Icons.keyboard_arrow_down,
                color: AppColor.iconGreyColor,
                size: 15,
              )
            ],
          ),
        ),
        options: widget.newarcUser.role != "renderist" ? actionDropdownOptions : actionDropdownOptions.where((option) => (option['value'] == 'fractionation' || option['value'] == 'comment')).toList(),
        iconSize: 13,
        hintText: "Azioni",
        onChanged: (value) {
          if(value["value"] == "fractionation"){
            showDialog(
              context: context,
              builder: (BuildContext context) => _onFrazionamentoTap(),
            );
          }else if(value["value"] == "edit_team"){
            showRenderistTeamDialog();
          }else if(value["value"] == "edit_priority"){
            showEditPriorityDialog();
          }else if(value["value"] == "comment"){
            popupComments(immaginaProject);
          }
        },
      ),
    );
  }

  Expanded _announcement() {
    return Expanded(
      child: Container(
        height: 330,
        padding: EdgeInsets.only(left: 22, right: 22, top: 22, bottom: 8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15),
          border: Border.all(
            width: 1.7,
            color: Color(
              0xffC3C9CF,
            ),
          ),
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                NarFormLabelWidget(
                  label: "Annuncio Newarc",
                  fontSize: 17,
                  fontWeight: '700',
                  textColor: Colors.black,
                ),
                !((widget.isFromProjectArchive != null) && (widget.isFromProjectArchive ?? false)) ?
                CustomDropdownButton(
                  width: 157,
                  selectedValue: selectedStatus,
                  items: status,
                  showCircle: true,
                  hintText: 'Select Status',
                  containerColor: Colors.green,
                  getColor: ( status) {
                    switch (status) {
                      case 'Da sbloccare':
                        return Color.fromRGBO(166, 166, 166, 1);
                      case 'Da contattare':
                        return Color.fromRGBO(245, 198, 32, 1);
                      case 'Contattato':
                        return Color.fromRGBO(86, 195, 229, 1);
                      case 'Completato':
                        return Color(0xff39C14F);
                      case 'In analisi':
                        return Color(0xffFEC600);
                      case 'Bloccata':
                        return Color(0xffDD0000);
                      case 'Confermata':
                        return Color(0xff39C14F);
                      case 'completato':
                        return Color(0xff39C14F);
                      case 'In lavorazione':
                        return Color(0xffFF7B00);
                      default:
                        return Colors.transparent;
                    }
                  },
                  onChanged: (value) async {
                    if (value != null) {
                      selectedStatus = value;
                      ///CHANGE STATUS
                      print(selectedStatus.toLowerCase());
                      var docRef = FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS).doc(widget.projectFirebaseId);
                      DocumentSnapshot<Map<String, dynamic>> immaginaproject = await docRef.get();
                      DocumentSnapshot<Map<String, dynamic>> agencyData = await FirebaseFirestore.instance.collection(appConfig.COLLECT_AGENCIES).doc(immaginaproject.data()?["agencyId"]).get();
                      await docRef.update({
                        'requestStatus': value.toLowerCase(),
                        // 'statusChangedDate': DateTime.now().millisecondsSinceEpoch,
                        'projectCompletedDate': selectedStatus.toLowerCase() == CommonUtils.completato ? DateTime.now().millisecondsSinceEpoch : null,
                      }).then((value) async {
                        if(selectedStatus.toLowerCase() == CommonUtils.completato){
                          // file upload send notification mail
                          sendEmail(templateId: CommonUtils.filesUploadedEmailTemplateId, agency: Agency(agencyData.data()!, immaginaproject.data()?["agencyId"] ?? ""), subject: CommonUtils.filesUploadedEmailSubject);
                        }
                        Fluttertoast.showToast(
                          msg: "Lo stato è cambiato confermato!",
                          toastLength: Toast.LENGTH_SHORT,
                          gravity: ToastGravity.BOTTOM,
                          timeInSecForIosWeb: 1,
                          backgroundColor: Colors.green,
                          textColor: Colors.white,
                          fontSize: 16.0,
                        );
                        await initialFetch();
                        widget.updateViewCallback!('progetti-attivi');
                      }).catchError((error) {
                        Fluttertoast.showToast(
                          msg: "Errore durante la modifica dello stato!",
                          toastLength: Toast.LENGTH_SHORT,
                          gravity: ToastGravity.BOTTOM,
                          timeInSecForIosWeb: 1,
                          backgroundColor: Colors.red,
                          textColor: Colors.white,
                          fontSize: 16.0,
                        );
                        print("Error updating document: $error");
                      });
                      setState(() {});
                    }
                  },
                ): SizedBox.shrink(),
              ],
            ),
            Spacer(),
            // NarButtonWidget(
            //   textHeight: 1,
            //   color: Theme.of(context).primaryColor,
            //   hoverColor: Theme.of(context).primaryColor,
            //   borderRadius: 7,
            //   fontWeight: '600',
            //   fontSize: 13,
            //   splashColor: Theme.of(context).primaryColor,
            //   textColor: Colors.white,
            //   borderSideColor: Colors.transparent,
            //   text: 'Crea Annuncio',
            //   buttonPadding: EdgeInsets.only(left: 18, right: 18, bottom: 0),
            //   onClick: () {},
            // ),
            Spacer(),
          ],
        ),
      ),
    );
  }

  // -------- isAgencyArchived ===> true and isHouseSold ===> true ----> Green
  // -------- isAgencyArchived ===> true and isHouseSold ===> false ----> Red
  // -------- isAgencyArchived ===> false white
  Flexible _sale() {
    return Flexible(
      child: Container(
        height: 330,
        padding: EdgeInsets.only(left: 22, right: 22, top: 22, bottom: 8),
        decoration: BoxDecoration(
          color: immaginaProject.isAgencyArchived ?? false ? (immaginaProject.isHouseSold ? Theme.of(context).primaryColor : Color(0xffE82525)) : AppColor.white,
          borderRadius: BorderRadius.circular(15),
          border: Border.all(
            width: 1.7,
            color: immaginaProject.isAgencyArchived ?? false ? (immaginaProject.isHouseSold ? Theme.of(context).primaryColor : Color(0xffE82525)) : Color(0xffC3C9CF),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                NarFormLabelWidget(
                  label: "Vendita",
                  fontSize: 17,
                  fontWeight: '700',
                  textColor: immaginaProject.isAgencyArchived ?? false ? AppColor.white : AppColor.black,
                ),
              ],
            ),
            // Spacer(),
            (immaginaProject.isAgencyArchived ?? false) ?
            Column(
              children: [
                immaginaProject.isHouseSold ?
                SvgPicture.asset(
                  "assets/icons/property.svg",
                  color: AppColor.white,
                ) :
                Stack(
                  alignment: Alignment.center,
                  children: [
                    // SVG image
                    SvgPicture.asset(
                      "assets/icons/hand_shake_blue.svg",
                      height: 52,
                      width: 59,
                      color: AppColor.white,
                    ),
                    // Red cross line
                    CustomPaint(
                      size: const Size(59, 52), // Match SVG size
                      painter: CrossLinePainter(),
                    ),
                  ],
                ),
                SizedBox(
                  height: 30,
                ),
                NarFormLabelWidget(
                  label: immaginaProject.isHouseSold ? "L’immobile è stato venduto!" :"Progetto archiviato senza vendita",
                  fontSize: 17,
                  fontWeight: '700',
                  textAlign: TextAlign.center,
                  textColor: AppColor.white,
                ),
                SizedBox(
                  height: 5,
                ),
                NarFormLabelWidget(
                  label: "Clicca il tasto qui sotto per archiviare il progetto.",
                  fontSize: 13,
                  fontWeight: '600',
                  textAlign: TextAlign.center,
                  textColor: AppColor.white,
                ),
                SizedBox(
                  height: 50,
                ),
                NarButtonWidget(
                  leadingIcon: SvgPicture.asset(
                    "assets/icons/sold_box.svg",
                    color: immaginaProject.isHouseSold ? Theme.of(context).primaryColor : Color(0xffE82525),
                  ),
                  textHeight: 1,
                  color: AppColor.white,
                  hoverColor: AppColor.white,
                  borderRadius: 7,
                  fontWeight: '600',
                  fontSize: 13,
                  splashColor: AppColor.white,
                  textColor: immaginaProject.isHouseSold ? Theme.of(context).primaryColor : Color(0xffE82525),
                  borderSideColor: Colors.transparent,
                  text: 'Archivia',
                  buttonPadding: EdgeInsets.only(left: 18, right: 18, bottom: 0),
                  onClick: () {
                    _archiveProjectConfirmDialog();
                  },
                ),
                SizedBox(
                  height: 10,
                ),
              ],
            )
                :
            Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  height: 50,
                ),
                SvgPicture.asset(
                  "assets/icons/property.svg",
                  // color: AppColor.white,
                ),
                SizedBox(
                  height: 30,
                ),
                NarFormLabelWidget(
                  label: "L’immobile non è stato ancora venduto.",
                  fontSize: 17,
                  fontWeight: '700',
                  textAlign: TextAlign.center,
                  textColor: AppColor.black,
                ),
              ],
            )


          ],
        ),
      ),
    );
  }

  _archiveProjectConfirmDialog(){
    return showDialog(
      context: context,
      builder: (context) {
        bool dialogLoading = false;
        return StatefulBuilder(
            builder: (context, setDialogState){
              return Center(
                child: BaseNewarcPopup(
                  noButton: true,
                  title: "Archivia progetto",
                  column: Container(
                    width: 400,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        SizedBox(height: 20),
                        immaginaProject.isHouseSold ?
                        CustomTextFormField(
                          textAlign: TextAlign.center,
                          isHaveBorder: true,
                          isCenterLabel: true,
                          flex: 0,
                          suffixIcon: null,
                          readOnly: true,
                          labelColor: AppColor.greyColor,
                          fillColor: Color(0xffF2F2F2),
                          label: "L’immobile è stato venduto a",
                          controller: TextEditingController(
                            text: "${CommonUtils().formatStringToDecimal(input: immaginaProject.sellingPrice.toString())}€",
                          ),
                        )
                            :
                        NarFormLabelWidget(
                          label: 'Il progetto è stato archiviato senza vendita da parte dell’agenzia.',
                          fontSize: 15,
                          textAlign: TextAlign.center,
                          fontWeight: '600',
                          textColor: AppColor.black,
                        ),
                        SizedBox(height: 50),
                        dialogLoading ?
                        Center(
                          child: CircularProgressIndicator(color: Theme.of(context).primaryColor,),
                        )
                            :
                        BaseNewarcButton(
                          //width: 142,
                          textColor: AppColor.white,
                          color:  Theme.of(context).primaryColor,
                          buttonText: "Conferma archiviazione",
                          onPressed: () async {
                            setDialogState(() {
                              dialogLoading = true;
                            });

                            String projectId = widget.projectFirebaseId ?? "";
                            try {
                              final collectionRef = FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS);

                              // Update the field
                              await collectionRef.doc(projectId).update({
                                'isWorkArchived': true
                              });

                              //------for navigating back to screen
                              widget.projectArguments!.clear();
                              widget.projectArguments!.addAll({
                                'isFromRequest': false,
                              });
                              widget.updateViewCallback!('progetti-attivi', projectArguments: widget.projectArguments);

                              Navigator.pop(context);


                              log("Document updated successfully!");
                            } catch (e) {
                              log("Error updating document: $e");
                            }finally{
                              setDialogState(() {
                                dialogLoading = true;
                              });
                            }

                          },
                        ),
                        SizedBox(height: 15),
                        NarFormLabelWidget(
                          label: 'Il progetto sarà spostato nella pagina “Archiviati”',
                          fontSize: 13,
                          textAlign: TextAlign.center,
                          fontWeight: '600',
                          textColor: AppColor.black,
                        ),
                      ],
                    ),
                  ),
                ),
              );
            });
      },
    );
  }

  _locationViewDialog() {
    return showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Spacer(),
              SizedBox(
                width: 30,
              ),
              NarFormLabelWidget(
                label: "Localizzazione",
                textColor: AppColor.black,
                fontWeight: '700',
                fontSize: 17,
              ),
              Spacer(),
              SizedBox(
                width: 30,
                child: InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Icon(
                    Icons.close,
                  ),
                ),
              ),
            ],
          ),
          titlePadding: EdgeInsets.symmetric(horizontal: 10, vertical: 18),
          contentPadding: EdgeInsets.symmetric(horizontal: 40, vertical: 18),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                height: 10,
              ),
              Row(
                children: [
                  Expanded(
                    flex: 3,
                    child: Column(
                      children: [
                        CustomTextFormField(
                          isHaveBorder: false,
                          flex: 0,
                          fillColor: Color(0xffF5F5F5),
                          readOnly: true,
                          label: "Indirizzo",
                          controller: TextEditingController(
                            text: "${immaginaProject.streetName ?? ''}",
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(
                    width: 16,
                  ),
                  Expanded(
                    flex: 1,
                    child: Column(
                      children: [
                        CustomTextFormField(
                          isHaveBorder: false,
                          flex: 0,
                          fillColor: Color(0xffF5F5F5),
                          readOnly: true,
                          label: "Numero civico",
                          controller: TextEditingController(
                            text: immaginaProject.streetNumber,
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(
                    width: 47,
                  ),
                  Expanded(
                    flex: 2,
                    child: Column(
                      children: [
                        CustomTextFormField(
                          isHaveBorder: false,
                          flex: 0,
                          fillColor: Color(0xffF5F5F5),
                          readOnly: true,
                          label: "Città",
                          controller: TextEditingController(
                            text: "${immaginaProject.city ?? ''}",
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              SizedBox(
                height: 16,
              ),
              Row(
                children: [
                  Expanded(
                    flex: 3,
                    child: Column(
                      children: [
                        CustomTextFormField(
                          isHaveBorder: false,
                          flex: 0,
                          fillColor: Color(0xffF5F5F5),
                          readOnly: true,
                          label: "Zona",
                          controller: TextEditingController(
                            text: immaginaProject.marketZone ?? "None",
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              SizedBox(
                height: 10,
              ),
            ],
          ),
          backgroundColor: AppColor.white,
        );
      },
    );
  }

  _motionless() {
    return showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Spacer(),
              SizedBox(
                width: 30,
              ),
              NarFormLabelWidget(
                label: "Immobile",
                textColor: AppColor.black,
                fontWeight: '700',
                fontSize: 17,
              ),
              Spacer(),
              SizedBox(
                width: 30,
                child: InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Icon(
                    Icons.close,
                  ),
                ),
              ),
            ],
          ),
          titlePadding: EdgeInsets.symmetric(horizontal: 10, vertical: 18),
          contentPadding: EdgeInsets.symmetric(horizontal: 40, vertical: 18),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                height: 10,
              ),
              Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: Column(
                      children: [
                        CustomTextFormField(
                          isHaveBorder: false,
                          flex: 0,
                          fillColor: Color(0xffF5F5F5),
                          readOnly: true,
                          label: "Tipologia",
                          controller: TextEditingController(
                            text: "${immaginaProject.propertyType??"None"}",
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(
                    width: 16,
                  ),
                  Expanded(
                    flex: 1,
                    child: Column(
                      children: [
                        CustomTextFormField(
                          isHaveBorder: false,
                          flex: 0,
                          fillColor: Color(0xffF5F5F5),
                          readOnly: true,
                          label: "Locali",
                          controller: TextEditingController(
                            text: "${immaginaProject.rooms}",
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(
                    width: 16,
                  ),
                  Expanded(
                    flex: 1,
                    child: Column(
                      children: [
                        CustomTextFormField(
                          isHaveBorder: false,
                          flex: 0,
                          fillColor: Color(0xffF5F5F5),
                          readOnly: true,
                          label: "Bagni",
                          controller: TextEditingController(
                            text: "${immaginaProject.numberOfBathrooms}",
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(
                    width: 16,
                  ),
                  Expanded(
                    flex: 1,
                    child: Column(
                      children: [
                        CustomTextFormField(
                          isHaveBorder: false,
                          flex: 0,
                          fillColor: Color(0xffF5F5F5),
                          readOnly: true,
                          label: "Piano",
                          controller: TextEditingController(
                            text: "${immaginaProject.unitFloor}",
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(
                    width: 16,
                  ),
                  Expanded(
                    flex: 1,
                    child: Column(
                      children: [
                        CustomTextFormField(
                          isHaveBorder: false,
                          flex: 0,
                          fillColor: Color(0xffF5F5F5),
                          readOnly: true,
                          label: "Mq",
                          controller: TextEditingController(
                            text: "${immaginaProject.grossSquareFootage}",
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(
                    width: 40,
                  ),
                  Expanded(
                    flex: 2,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomTextFormField(
                          isHaveBorder: false,
                          isMoney: true,
                          flex: 0,
                          fillColor: Color(0xffF5F5F5),
                          readOnly: true,
                          label: "Prezzo di vendita",
                          controller: TextEditingController(
                            text: "${immaginaProject.listingPrice}",
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              SizedBox(
                height: 10,
              ),
            ],
          ),
          backgroundColor: AppColor.white,
        );
      },
    );
  }

  _description() {
    return showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Spacer(),
              SizedBox(
                width: 40,
              ),
              NarFormLabelWidget(
                label: "Descrizione",
                textColor: AppColor.black,
                fontWeight: '700',
                fontSize: 17,
              ),
              SizedBox(
                width: 10,
              ),
              Spacer(),
              SizedBox(
                width: 30,
                child: InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Icon(
                    Icons.close,
                  ),
                ),
              ),
            ],
          ),
          titlePadding: EdgeInsets.symmetric(horizontal: 10, vertical: 20),
          contentPadding: EdgeInsets.only(
            left: 20,
            right: 20,
            bottom: 20,
          ),
          content: Container(
            width: MediaQuery.of(context).size.width/3,
            padding: EdgeInsets.all(24),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                width: 1.2,
                color: Color(0xffD1D1D1),
              ),
            ),
            child: SingleChildScrollView(
              scrollDirection: Axis.vertical,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  NarFormLabelWidget(
                    fontSize: 14,
                    overflow: TextOverflow.fade,
                    fontWeight: '600',
                    textColor: AppColor.black,
                    label: "${immaginaProject.description}",
                  ),
                ],
              ),
            ),
          ),
          backgroundColor: AppColor.white,
        );
      },
    );
  }

  _problems() {
    return showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Spacer(),
              SizedBox(
                width: 40,
              ),
              NarFormLabelWidget(
                label: "Altre indicazioni",
                textColor: AppColor.black,
                fontWeight: '700',
                fontSize: 17,
              ),
              SizedBox(
                width: 10,
              ),
              Spacer(),
              SizedBox(
                width: 30,
                child: InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Icon(
                    Icons.close,
                  ),
                ),
              ),
            ],
          ),
          titlePadding: EdgeInsets.symmetric(horizontal: 10, vertical: 20),
          contentPadding: EdgeInsets.only(
            left: 20,
            right: 20,
            bottom: 20,
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              NarFormLabelWidget(
                fontSize: 15,
                overflow: TextOverflow.fade,
                fontWeight: '700',
                textColor: AppColor.black,
                label: "Problematiche o difficoltà strutturali",
              ),
              SizedBox(height: 5,),
              Container(
                width: MediaQuery.of(context).size.width/3,
                padding: EdgeInsets.all(24),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(
                    width: 1.2,
                    color: Color(0xffD1D1D1),
                  ),
                ),
                child: SingleChildScrollView(
                  scrollDirection: Axis.vertical,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      NarFormLabelWidget(
                        fontSize: 14,
                        overflow: TextOverflow.fade,
                        fontWeight: '600',
                        textColor: AppColor.black,
                        label: immaginaProject.specialHints ?? "-",
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(height: 10,),
              NarFormLabelWidget(
                fontSize: 15,
                overflow: TextOverflow.fade,
                fontWeight: '700',
                textColor: AppColor.black,
                label: "Quando vorresti mettere in vendita l’immobile?",
              ),
              SizedBox(height: 5,),
              Container(
                width: MediaQuery.of(context).size.width/3,
                padding: EdgeInsets.all(24),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(
                    width: 1.2,
                    color: Color(0xffD1D1D1),
                  ),
                ),
                child: SingleChildScrollView(
                  scrollDirection: Axis.vertical,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      NarFormLabelWidget(
                        fontSize: 14,
                        overflow: TextOverflow.fade,
                        fontWeight: '600',
                        textColor: AppColor.black,
                        label: immaginaProject.propertyUpForSaleAnswer ?? "-",
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          backgroundColor: AppColor.white,
        );
      },
    );
  }

  _photographs() {
    bool isImagesDownload = false;
    return showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(builder: (context,_setState){
          return AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            title: Row(
              children: [
                Spacer(),
                SizedBox(
                  width: 30,
                ),
                NarFormLabelWidget(
                  label: "Fotografie",
                  textColor: AppColor.black,
                  fontWeight: '700',
                  fontSize: 17,
                ),
                Spacer(),
                SizedBox(
                  width: 30,
                  child: InkWell(
                    onTap: () {
                      Navigator.pop(context);
                    },
                    child: Icon(
                      Icons.close,
                    ),
                  ),
                ),
              ],
            ),
            titlePadding: EdgeInsets.symmetric(horizontal: 10, vertical: 18),
            contentPadding: EdgeInsets.symmetric(horizontal: 40, vertical: 18),
            content: SizedBox(
              width: MediaQuery.of(context).size.width / 1.5,
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(
                      height: 10,
                    ),
                    if (immaginaProject.pictures.isNotEmpty)
                       Row(
                         mainAxisAlignment: MainAxisAlignment.end,
                         children: [
                           BaseNewarcButton(
                            buttonText: isImagesDownload
                                ? "Downloading"
                                : "Scarica tutte",
                            fontSize: 13,
                            color: AppColor.buttonBorderColor,
                            textColor: AppColor.black,
                            horizontalPadding: 10,
                            fontWeight: "600",
                            width: 120,
                            height: 30,
                            onPressed: () async {
                              if (immaginaProject.pictures.isNotEmpty) {
                                List<String> images = [];
                                try {
                                  _setState(() {
                                    isImagesDownload = true;
                                  });

                                  List<Future<String>> urlFutures = immaginaProject.pictures.map((e) async {
                                    String fineName = e['file'].split('/').last;
                                    return await printUrl(
                                        "immaginaProjects/${widget.projectFirebaseId}/fotografie",
                                        '',
                                        fineName);
                                  }).toList();


                                  images = await Future.wait(urlFutures);
                                  if (images.isNotEmpty) {
                                    await js.context.callMethod(
                                        'zipAndDownloadFiles', [
                                      js.JsArray.from(images),
                                      "fotografie",
                                      "image"
                                    ]);
                                  }
                                } finally {
                                  _setState(() {
                                    isImagesDownload = false;
                                  });
                                }
                              }
                            },

                                                 ),
                         ],
                       ),
                    SizedBox(
                      height: 10,
                    ),
                    if (immaginaProject.pictures.isNotEmpty)
                      FutureBuilder<List<dynamic>>(
                        future: _fetchImagesFromFolder(immaginaProject.pictures),
                        builder: (context, snapshot) {
                          if (snapshot.connectionState == ConnectionState.waiting) {
                            return Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Center(child: CircularProgressIndicator()),
                            );
                          } else if (snapshot.hasError) {
                            return Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: NarFormLabelWidget(
                                label: "Error fetching images: ${snapshot.error}",
                                fontSize: 15,
                                fontWeight: '700',
                                textColor: Colors.black,
                              ),
                            );
                          } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
                            return Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: NarFormLabelWidget(
                                label: "No images found.",
                                fontSize: 15,
                                fontWeight: '700',
                                textColor: Colors.black,
                              ),
                            );
                          } else {
                            final urls = snapshot.data!;
                            return GridView.builder(
                              shrinkWrap: true,
                              physics: NeverScrollableScrollPhysics(),
                              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount: 3,
                                crossAxisSpacing: 45,
                                mainAxisSpacing: 10,
                                childAspectRatio: 1,
                                mainAxisExtent: 100,
                              ),
                              itemCount: urls.length,
                              itemBuilder: (context, index) {
                                final url = urls[index];
                                return Row(
                                  children: [
                                    InkWell(
                                      onTap: () async {
                                        await launchUrl(
                                          Uri.parse(url['file']),
                                        );
                                      },
                                      child: ClipRRect(
                                        borderRadius: BorderRadius.circular(8),
                                        child: Image.network(
                                          url['file'],
                                          width: 100,
                                          height: 100,
                                          fit: BoxFit.cover,
                                          errorBuilder: (context, error, stackTrace) {
                                            return Icon(Icons.broken_image, size: 50);
                                          },
                                        ),
                                      ),
                                    ),
                                    SizedBox(width: 10),
                                    Expanded(
                                      flex: 1,
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          NarFormLabelWidget(
                                            label: "Stanza",
                                            fontSize: 13,
                                            fontWeight: '600',
                                            textColor: Color(0xff696969),
                                          ),
                                          SizedBox(height: 5),
                                          CustomTextFormField(
                                            isHaveBorder: false,
                                            flex: 0,
                                            controllerFontSize: 12,
                                            fillColor: Color(0xffF5F5F5),
                                            readOnly: true,
                                            label: "",
                                            controller: TextEditingController(
                                              text: url['tag'],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    SizedBox(width: 10),
                                  ],
                                );
                              },
                            );
                          }
                        },
                      ),
                    SizedBox(
                      height: 10,
                    ),
                  ],
                ),
              ),
            ),
            backgroundColor: AppColor.white,
          );
        });
      },
    );
  }

  _floorPlan() {
    return showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Spacer(),
              SizedBox(
                width: 30,
              ),
              NarFormLabelWidget(
                label: "Planimetria",
                textColor: AppColor.black,
                fontWeight: '700',
                fontSize: 17,
              ),
              Spacer(),
              SizedBox(
                width: 30,
                child: InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Icon(
                    Icons.close,
                  ),
                ),
              ),
            ],
          ),
          titlePadding: EdgeInsets.symmetric(horizontal: 10, vertical: 18),
          contentPadding: EdgeInsets.symmetric(horizontal: 40, vertical: 18),
          content: SizedBox(
            width: 475,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  height: 10,
                ),
                if (immaginaProject.planimetry.isNotEmpty)
                  Row(
                    children: [
                      Expanded(
                        child: FutureBuilder<List<dynamic>>(
                          future: _fetchPlanmetriaFromFolder(immaginaProject.planimetry),
                          builder: (context, snapshot) {
                            if (snapshot.connectionState == ConnectionState.waiting) {
                              return Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Center(child: CircularProgressIndicator()),
                              );
                            } else if (snapshot.hasError) {
                              return Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: NarFormLabelWidget(
                                  label: "Error fetching images: ${snapshot.error}",
                                  fontSize: 15,
                                  fontWeight: '700',
                                  textColor: Colors.black,
                                ),
                              );
                            } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
                              return Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: NarFormLabelWidget(
                                  label: "No images found.",
                                  fontSize: 15,
                                  fontWeight: '700',
                                  textColor: Colors.black,
                                ),
                              );
                            } else {
                              final urls = snapshot.data!;
                              return Wrap(
                                spacing: 10,
                                runSpacing: 10,
                                children: urls.map((toElement) {
                                  return (toElement['file'].toString().contains('.pdf'))
                                      ? InkWell(
                                          onTap: () async {
                                            await launchUrl(
                                              Uri.parse(toElement['file']),
                                            );
                                          },
                                          child: Container(
                                            decoration: BoxDecoration(color: Color(0xffD9D9D9), borderRadius: BorderRadius.circular(7)),
                                            height: 100,
                                            width: 100,
                                            child: Icon(
                                              Icons.picture_as_pdf,
                                              size: 50,
                                            ),
                                          ),
                                        )
                                      : InkWell(
                                          onTap: () async {
                                            await launchUrl(
                                              Uri.parse(toElement['file']),
                                            );
                                          },
                                          child: ClipRRect(
                                            borderRadius: BorderRadius.circular(8),
                                            child: Image.network(
                                              toElement['file'],
                                              width: 100,
                                              height: 100,
                                              fit: BoxFit.cover,
                                              errorBuilder: (context, error, stackTrace) {
                                                return SizedBox(
                                                  height: 100,
                                                  width: 100,
                                                  child: Icon(
                                                    Icons.broken_image,
                                                    size: 50,
                                                  ),
                                                );
                                              },
                                            ),
                                          ),
                                        );
                                }).toList(),
                              );
                            }
                          },
                        ),
                      ),
                    ],
                  ),
                SizedBox(
                  height: 10,
                ),
              ],
            ),
          ),
          backgroundColor: AppColor.white,
        );
      },
    );
  }

  Future<List<dynamic>> _fetchPlanmetriaFromFolder(List<dynamic> images) async {
    try {
      List<dynamic> data = [];

      for (var item in images) {
        String downloadUrl = await getDownloadLink('immaginaProjects', widget.projectFirebaseId, item);
        data.add({
          "file": downloadUrl,
        });
      }

      return data;
    } catch (e) {
      print('Error fetching files from folder: $e');
      return [];
    }
  }

  Future<List<dynamic>> _fetchImagesFromFolder(List<dynamic> images) async {
    try {
      List<dynamic> data = [];

      for (var item in images) {
        String filePath = item['file'];
        String tag = item['tag'];
        String downloadUrl = await getDownloadLink('immaginaProjects', widget.projectFirebaseId, filePath);
        data.add({
          "file": downloadUrl,
          "tag": tag,
        });
      }

      return data;
    } catch (e) {
      print('Error fetching files from folder: $e');
      return [];
    }
  }

  _characteristics() {
    return showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Spacer(),
              SizedBox(
                width: 30,
              ),
              NarFormLabelWidget(
                label: "Dotazioni e particolarità",
                textColor: AppColor.black,
                fontWeight: '700',
                fontSize: 17,
              ),
              Spacer(),
              SizedBox(
                width: 30,
                child: InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Icon(
                    Icons.close,
                  ),
                ),
              ),
            ],
          ),
          titlePadding: EdgeInsets.symmetric(horizontal: 10, vertical: 18),
          contentPadding: EdgeInsets.symmetric(horizontal: 40, vertical: 18),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  height: 10,
                ),
                SizedBox(
                  width: 400,
                  child: Padding(
                    padding: const EdgeInsets.only(left: 8.0),
                    child: Wrap(
                      spacing: 20,
                      runSpacing: 20,
                      children: [
                        ...[
                          if (immaginaProject.elevator == true) "Ascensore",
                          if (immaginaProject.hasCantina == true) "Cantina",
                          if (immaginaProject.terrace == true) "Terrazzo",
                          if (immaginaProject.hasConcierge == true) "Portineria",
                          if (immaginaProject.highEfficiencyFrames == true) "Infissi ad alta efficienza",
                          if (immaginaProject.doubleEsposition == true) "Doppia esposizione",
                          if (immaginaProject.tripleEsposition == true) "Tripla esposizione",
                          if (immaginaProject.quadrupleEsposition == true) "Quadrupla esposizione",
                          if (immaginaProject.centralizedHeating == true) "Riscaldamento centralizzato",
                          if (immaginaProject.autonomousHeating == true) "Riscaldamento autonomo",
                          if (immaginaProject.privateGarden == true) "Giardino privato",
                          if (immaginaProject.sharedGarden == true) "Giardino condominiale",
                          if (immaginaProject.surveiledBuilding == true) "Stabile videosorvegliato",
                          if (immaginaProject.nobleBuilding == true) "Stabile signorile",
                          if (immaginaProject.fiber == true) "Fibra ottica",
                          if (immaginaProject.airConditioning == true) "Pred. condizionatore",
                          if (immaginaProject.securityDoor == true) "Porta blindata",
                          if (immaginaProject.tvStation == true) "Impianto TV",
                          if (immaginaProject.alarm == true) "Pred. antifurto",
                          if (immaginaProject.motorizedSunblind == true) "Tapparelle motorizzate",
                          if (immaginaProject.domotizedSunblind == true) "Tapparelle domotizzate",
                          if (immaginaProject.domotizedLights == true) "Luci domotizzate",
                          if (immaginaProject.highFloor == true) "Piano alto",
                          if (immaginaProject.metroVicinity == true) "Vicinanza metro",
                          if (immaginaProject.bigBalconies == true) "Ampi balconi",
                          if (immaginaProject.bigLiving == true) "Grande zona living",
                          if (immaginaProject.doubleBathroom == true) "Doppi servizi",
                          if (immaginaProject.hasGarage == true) "Box o garage",
                          if (immaginaProject.swimmingPool == true) "Piscina",
                        ].map(
                          (label) => Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              SvgPicture.asset(
                                "assets/icons/check_box.svg",
                                height: 14,
                                width: 14,
                              ),
                              SizedBox(width: 10),
                              NarFormLabelWidget(
                                label: label,
                                fontSize: 14,
                                fontWeight: '600',
                                textColor: Colors.black,
                              ),
                            ],
                          ),
                        )
                        .toList(),
                        ...[
                          Container(
                            width: 150,
                            height: 70,
                            child: Row(
                              children: [
                                CustomTextFormField(
                                  label: "Classe energetica",
                                  hintText: immaginaProject.energyClass.toString(),
                                  fillColor: Color(0xffF5F5F5),
                                  // flex: 1,
                                  isHaveBorder: false,
                                  readOnly: true,
                                ),
                              ],
                            ),
                          ),
                          SizedBox(width: 15,),
                          Container(
                            width: 150,
                            height: 70,
                            child: Row(
                              children: [
                                CustomTextFormField(
                                  label: "Anno di costruzione",
                                  hintText: immaginaProject.constructionYear.toString(),
                                  fillColor: Color(0xffF5F5F5),
                                  // flex: 1,
                                  isHaveBorder: false,
                                  readOnly: true,
                                ),
                              ],
                            ),
                          ),
                          SizedBox(width: 15,),
                          Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              NarFormLabelWidget(
                                label: 'Esposizione',
                                textColor: Color(0xff696969),
                                fontSize: 13,
                                fontWeight: '500',
                              ),
                              SizedBox(height: 15,),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: immaginaProject.externalEsposition
                                  .map((label)=>_externalEspositionIcon(label)).toList(),
                              )
                            ]
                          )
                        ]
                      ],
                    ),
                  ),
                ),
                SizedBox(
                  height: 10,
                ),
              ],
            ),
          ),
          backgroundColor: AppColor.white,
        );
      },
    );
  }

  Widget _externalEspositionIcon(String label){
    return Padding(
      padding: const EdgeInsets.only(right: 8.0),
      child: OutlinedButton(
        style: OutlinedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20.0),
          ),
          side: BorderSide(
            color: Colors.transparent,
          ),
          backgroundColor: Color(0xffF5F5F5),
        ),
        onPressed: null,
        child: Text(
          label,
          style: TextStyle(
            color: Theme.of(context).disabledColor,
            fontSize: 14,
            fontFamily: 'Raleway-700',
          ),
        ),
      ),
    );
  }

  AnimatedContainer _request() {
    return AnimatedContainer(
      duration: Duration(milliseconds: 800),
      padding: EdgeInsets.only(left: 22, right: 22, top: 22, bottom: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          width: 1.7,
          color: Color(
            0xffC3C9CF,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: NarFormLabelWidget(
                  label: "La richiesta",
                  fontSize: 16,
                  fontWeight: '700',
                  textColor: Colors.black,
                ),
              ),
              !((widget.isFromProjectArchive != null) && (widget.isFromProjectArchive ?? false)) && immaginaProject.requestStatus.toLowerCase() != CommonUtils.completato ?
              NarFormLabelWidget(
                label: immaginaProject.projectId,
                fontSize: 11,
                fontWeight: '600',
                textColor: Color(0xff737373),
              ) : SizedBox.shrink(),
            ],
          ),
          if (isExpanded) ...[
            Column(
              children: [
                SizedBox(
                  height: 24,
                ),
                Center(
                  child: Wrap(
                    alignment: WrapAlignment.start,
                    crossAxisAlignment: WrapCrossAlignment.center,
                    spacing: 15,
                    runSpacing: 15,
                    children: insideRequest.map((v) {
                      return Container(
                        width: 133,
                        height: 95,
                        decoration: BoxDecoration(
                          border: Border.all(
                            width: 1,
                            color: Color(0xffDADADA),
                          ),
                          borderRadius: BorderRadius.circular(10),
                          color: ((v.value == "Planimetria" && immaginaProject.planimetry.isEmpty) ||
                              (v.value =="Fotografie" && immaginaProject.pictures.isEmpty)) ? Color(0xffDADADA) : AppColor.white,
                        ),
                        padding: EdgeInsets.all(10),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            NarFormLabelWidget(
                              label: v.value,
                              fontSize: 12,
                              fontWeight: '700',
                              textColor: AppColor.black,
                            ),
                            SizedBox(
                              height: 10,
                            ),
                            ((v.value == "Planimetria" && immaginaProject.planimetry.isEmpty) ||
                                (v.value =="Fotografie" && immaginaProject.pictures.isEmpty)) ?
                            Padding(
                              padding: const EdgeInsets.only(bottom: 10,top: 10),
                              child: NarFormLabelWidget(
                                label: "Richiesto",
                                fontSize: 12,
                                fontWeight: '600',
                                textColor: AppColor.greyColor,
                              ),
                            )
                                :
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                NarLinkWidget(
                                  text: "Visualizza",
                                  textColor: Theme.of(context).primaryColor,
                                  fontWeight: '600',
                                  textAlign: TextAlign.center,
                                  fontSize: 12,
                                  onClick: () {
                                    openDialog(v);
                                  },
                                ),
                                SizedBox(
                                  width: 5,
                                ),
                                InkWell(
                                  onTap: () {
                                    openDialog(v);
                                  },
                                  child: SvgPicture.asset(
                                    "assets/icons/eye.svg",
                                    height: 8,
                                    color: Theme.of(context).primaryColor,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ],
            ),
          ],
          Column(
            children: [
              SizedBox(
                height: 10,
              ),
              Align(
                alignment: Alignment.centerRight,
                child: InkWell(
                  highlightColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  splashFactory: NoSplash.splashFactory,
                  hoverColor: Colors.transparent,
                  onTap: () {
                    setState(() {
                      isExpanded = !isExpanded;
                    });
                  },
                  child: Image.asset(
                    isExpanded ? 'assets/icons/arrow_up.png' : 'assets/icons/arrow_down.png',
                    height: 15,
                    color: Color(0xff6C6C6C),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  StatefulBuilder _onFrazionamentoTap() {
    return StatefulBuilder(builder: (context, setState) {
      return AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: Row(
          children: [
            Spacer(),
            SizedBox(
              width: 30,
            ),
            NarFormLabelWidget(
              label: "Trasforma in frazionamento",
              textColor: AppColor.black,
              fontWeight: '700',
              fontSize: 17,
            ),
            Spacer(),
            SizedBox(
              width: 30,
              child: InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: Icon(
                  Icons.close,
                ),
              ),
            ),
          ],
        ),
        titlePadding: EdgeInsets.symmetric(horizontal: 10, vertical: 18),
        contentPadding: EdgeInsets.symmetric(horizontal: 40, vertical: 18),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              height: 10,
            ),
            SizedBox(
              width: 300,
              child: NarSelectBoxWidget(
                label: 'Numero frazionamenti',
                options: frazionamentiList,
                onChanged: (value) {},
                controller: selectedFrazionamenti,
              ),
            ),
            SizedBox(
              height: 60,
            ),
            MouseRegion(
              cursor: SystemMouseCursors.click,
              child: GestureDetector(
                onTap: () {
                  _copyProjectByGivenNumber(copyNumber: selectedFrazionamenti.value.text.trim(), firebaseId: widget.projectFirebaseId, setState: setState);
                },
                child: Container(
                  height: 40,
                  width: 210,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor,
                    borderRadius: BorderRadius.circular(7),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20.0),
                    child: isLoadingProjectCopy
                        ? Center(
                            child: SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                color: AppColor.white,
                              ),
                            ),
                          )
                        : NarFormLabelWidget(
                            label: "Trasforma",
                            textAlign: TextAlign.center,
                            textColor: AppColor.white,
                            fontWeight: '600',
                            fontSize: 15,
                          ),
                  ),
                ),
              ),
            ),
            SizedBox(
              height: 30,
            ),
          ],
        ),
        backgroundColor: AppColor.white,
      );
    });
  }

  void showEditPriorityDialog() async {
    TextEditingController priorityController = TextEditingController();
    List<String> formErrorMessage = [];
    return showDialog(
        context: context,
        barrierDismissible: true,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (context,_setState){
            return Center(
              child: BaseNewarcPopup(
                  formErrorMessage: formErrorMessage,
                  buttonColor: Theme.of(context).primaryColor,
                  title: "Modifica priorità",
                  buttonText: "Trasforma",
                  onPressed: () async {
                    _setState((){
                      formErrorMessage.clear();
                      formErrorMessage.add("Salvataggio in corso...");
                    });
                    try{
                      await FirebaseFirestore.instance
                          .collection(appConfig.COLLECT_IMMAGINA_PROJECTS)
                          .doc(widget.projectFirebaseId)
                          .update({
                        "priority": priorityController.text.trim()
                      });
                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Salvato");
                      });
                    }catch(e){
                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Si è verificato un errore.");
                      });
                      log("---------- ERROR While editing priority ------> ${e.toString()}");
                    }
                  },
                  column: Container(
                    width: 400,
                    padding: EdgeInsets.symmetric(vertical: 25),
                    child: NarColorBgDropdown(
                      height: 51,
                      iconColor: Colors.white,
                      controller: priorityController,
                      downArrowSize: 15,
                      padding: EdgeInsets.symmetric(vertical: 5,horizontal: 10),
                      labelTextStyle: TextStyle(
                        color: AppColor.black,
                        fontFamily: 'Raleway-700',
                        fontSize: 14,
                      ),
                      options: priorityStatus,
                      onChanged: () async {},
                    ),
                  )),
            );
          });
        });
  }

  Future<void> showRenderistTeamDialog() {
    return showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(builder: (__context, _setState) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Spacer(),
              SizedBox(
                width: 30,
              ),
              NarFormLabelWidget(
                label: "Assegna il team",
                textColor: AppColor.black,
                fontWeight: '700',
                fontSize: 17,
              ),
              Spacer(),
              SizedBox(
                width: 30,
                child: InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Icon(
                    Icons.close,
                  ),
                ),
              ),
            ],
          ),
          titlePadding: EdgeInsets.symmetric(horizontal: 10, vertical: 18),
          contentPadding: EdgeInsets.symmetric(horizontal: 40, vertical: 18),
          content: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                height: 30,
              ),
              NarFormLabelWidget(
                label: "Seleziona Team",
                fontSize: 15,
                fontWeight: '500',
                textColor: AppColor.iconGreyColor,
              ),
              SizedBox(
                height: 5,
              ),
              MultiSelectDropdownWidget(
                dialogTitle: "Assegna Team Renderist",
                options: allRenderistList.map((user) => {'value': user.id, 'label': "${user.firstName ?? ""} ${user.lastName ?? ""}"}).toList(),
                initialValue: projectRenderistList.map((user) => {'value': user.id, 'label': "${user.firstName ?? ""} ${user.lastName ?? ""}"}).toList(),
                onChanged: (List<dynamic> selectedValues) {
                  print(selectedValues);
                  projectRenderistList = selectedValues.map((elem) => allRenderistList.firstWhere((user) => user.id == elem['value'])).toList();
                  _setState(() {});
                },
              ),
              // SizedBox(
              //   height: 15,
              // ),
              // SizedBox(
              //   width: 400,
              //   child: Row(
              //     children: [
              //       CustomTextFormField(
              //         flex: 1,
              //         label: '',
              //         hintText: "Aggiungi note",
              //         minLines: 4,
              //         controller: renderistNotesController,
              //       ),
              //     ],
              //   ),
              // ),
              SizedBox(
                height: 30,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  MouseRegion(
                    cursor: SystemMouseCursors.click,
                    child: GestureDetector(
                      onTap: () async {
                        // String notes = renderistNotesController.text;
                        List<String?> renderistTeamIdList = projectRenderistList.map((user) => user.id).toList();
                        var docRef = FirebaseFirestore.instance
                            .collection(appConfig.COLLECT_IMMAGINA_PROJECTS)
                            .doc(widget.projectFirebaseId);
                        await docRef.update({
                          'renderistTeamIdList': renderistTeamIdList,
                          // 'renderistTeamNotes': notes,
                        }).then((value) {
                          Fluttertoast.showToast(
                            msg: "Il team è stato modificato!",
                            toastLength: Toast.LENGTH_SHORT,
                            gravity: ToastGravity.BOTTOM,
                            timeInSecForIosWeb: 1,
                            backgroundColor: Colors.green,
                            textColor: Colors.white,
                            fontSize: 16.0,
                          );
                          Navigator.pop(context);
                        }).catchError((error) {
                          Fluttertoast.showToast(
                            msg: "Errore durante la modifica del team!",
                            toastLength: Toast.LENGTH_SHORT,
                            gravity: ToastGravity.BOTTOM,
                            timeInSecForIosWeb: 1,
                            backgroundColor: Colors.red,
                            textColor: Colors.white,
                            fontSize: 16.0,
                          );
                          print("Error updating document: $error");
                        });;
                      },
                      child: Container(
                        height: 40,
                        width: 210,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          color: Theme.of(context).primaryColor,
                          borderRadius: BorderRadius.circular(7),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 20.0),
                          child: NarFormLabelWidget(
                            label: "Salva Team",
                            textAlign: TextAlign.center,
                            textColor: AppColor.white,
                            fontWeight: '600',
                            fontSize: 15,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(
                height: 30,
              ),
            ],
          ),
          backgroundColor: AppColor.white,
        );
      });
      },
    );
  }

  _copyProjectByGivenNumber({required String copyNumber, firebaseId, required Function(void Function()) setState}) async {
    try {
      setState(() {
        isLoadingProjectCopy = true;
      });
      DocumentSnapshot<Map<String, dynamic>> originalDoc = await FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS).doc(firebaseId).get();

      if (originalDoc.exists && originalDoc.data() != null) {
        Map<String, dynamic> originalData = originalDoc.data()!;
        print(originalData);
        for (int i = 1; i <= int.parse(copyNumber); i++) {
          String newId = FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS).doc().id;
          print(newId);

          Map<String, dynamic> newData = {
            ...originalData,
            "id": newId,
            "housingUnit": "Unità abitativa $i",
            "projectId": "${originalData['projectId']}.$i",
            "createdAt": FieldValue.serverTimestamp(),
          };
          print("COPY_PROJECT_ID -------> $newId");
          await FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS).doc(newId).set(newData);
        }

        await FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS).doc(firebaseId).delete();

        Navigator.pop(context);
        setState(() {
          isLoadingProjectCopy = false;
        });
        widget.updateViewCallback!('progetti-attivi');

        print("Original document removed after copying.");
      } else {
        setState(() {
          isLoadingProjectCopy = false;
        });
        print("Original document does not exist or has no data.");
      }
    } catch (e) {
      setState(() {
        isLoadingProjectCopy = false;
      });
      print(e);
    }
  }

  Row _header() {
    return Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          MouseRegion(
            cursor: SystemMouseCursors.click,
            child: GestureDetector(
              onTap: () {
                widget.projectArguments!.clear();
                if((widget.isFromProjectArchive != null) && (widget.isFromProjectArchive ?? false)){
                  widget.updateViewCallback!('progetti-archiviati', projectArguments: widget.projectArguments);
                  widget.projectArguments!.addAll({
                    'isFromRequest': false,
                    'isFromProjectArchive' : true,
                  });
                }else{
                  widget.updateViewCallback!('progetti-attivi', projectArguments: widget.projectArguments);
                  widget.projectArguments!.addAll({
                    'isFromRequest': false,
                  });
                }
              },
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SvgPicture.asset(
                      'assets/icons/arrow_left.svg',
                      height: 15,
                      color: Colors.black),
                  SizedBox(width: 15,),
                  NarFormLabelWidget(
                    label: 'Tutti i progetti',
                    fontSize: 15,
                    fontWeight: '600',
                    textDecoration: TextDecoration.underline,
                    textColor: AppColor.black,
                  ),
                ],
              ),
            ),
          ),

          NarFormLabelWidget(
            label: "${immaginaProject.streetName ?? 'noStreetName'}, ${immaginaProject.streetNumber ?? 'noStreetNum'} ${immaginaProject.housingUnit != null ? '-' : ''} ${immaginaProject.housingUnit ?? ''}",
            fontSize: 20,
            fontWeight: '700',
            textColor: Colors.black,
          ),

          _buildActionsDropdown(),
        ]
    );
  }
}


// red cross line custom painter
class CrossLinePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Color(0xffFFFFFF) // Red cross color
      ..strokeWidth = 3.0 // Line thickness
      ..style = PaintingStyle.stroke;

    // Draw diagonal lines for the red cross
    canvas.drawLine(Offset(size.width, 0), Offset(0, size.height),
        paint); // Top-right to bottom-left
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
