import 'package:flutter/material.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:flutter_svg/flutter_svg.dart';

class NarImageSelectBoxWidget extends StatefulWidget {
  // final TextInputType? textInputType;
  // final String? hintText;
  // final Widget? prefixIcon;
  // final String? defaultText;
  // final FocusNode? focusNode;
  // final bool? obscureText;
  // final TextEditingController? controller;
  // final Function? functionValidate;
  // final String? parametersValidate;
  // final TextInputAction? actionKeyboard;
  // final Function? onSubmitField;

  final List<dynamic> options;
  final Function(dynamic)? onChanged;
  final Function? onFieldTap;
  final String? hint;
  final String? disabledHint;
  final bool? autoFocus;
  final TextEditingController? controller;
  final String? validationType;
  final String? parametersValidate;
  final String? label;
  final Color? labelColor;
  final int? flex;
  final double? iconSize;
  final EdgeInsetsGeometry? buttonPadding;
  final bool? enableMultipleSelect;
  final List? selectedItems;
  final double? valueLabelSize;

  NarImageSelectBoxWidget(
      {this.flex = 1,
      this.label = '',
      this.labelColor = const Color(0xff696969),
      required this.options,
      this.hint,
      this.disabledHint,
      this.controller,
      this.autoFocus,
      this.onChanged,
      this.onFieldTap,
      this.validationType,
      this.parametersValidate,
      this.iconSize = 24,
      this.buttonPadding = const EdgeInsets.only(top: 17, bottom: 17, left: 21.0, right: 8.0),
      this.enableMultipleSelect = false,
      this.selectedItems = const [],
      this.valueLabelSize = 14
      });

  @override
  _NarImageSelectBoxWidgetState createState() =>
      _NarImageSelectBoxWidgetState();
}

class _NarImageSelectBoxWidgetState extends State<NarImageSelectBoxWidget> {
  double bottomPaddingToError = 12;
  Map? dropdownValue;
  String? selectedValue;

  @override
  void initState() {
    super.initState();
    // print({ 'init', widget.options});
    if (widget.controller!.text == '') {
      if( widget.options.length > 0 ) dropdownValue = widget.options.first;
      else dropdownValue = null;
    } else {
      try {
        dropdownValue = widget.options
            .firstWhere((element) => element['value'] == widget.controller!.text, orElse: () =>  {'value': '', 'label': ''} );

        print({ 'dropdownValue', widget.controller!.text, dropdownValue  });
      } catch (e,s) {
        // dropdownValue = widget.options.first;
        print({e,s});
      }
    }
  }

  @protected
  void didUpdateWidget(NarImageSelectBoxWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    // print({ 'jpdate', widget.options});
    
    if (widget.controller!.text == '') {
      if( widget.options.length > 0 ) dropdownValue = widget.options.first;
      else dropdownValue = null;
    } else {
      try {
        dropdownValue = widget.options
            .firstWhere((element) => element['value'] == widget.controller!.text, orElse: () =>  {'value': '', 'label': ''} );
        
      } catch (e,s) {
        // dropdownValue = widget.options.first;
        print({e,s});
      }
    }

  }

  @override
  Widget build(BuildContext context) {
    return DropdownButtonFormField<Map>(
        isExpanded: true,
        value: dropdownValue,
        icon: Padding(
          padding: EdgeInsets.only(right: 8),
          child: SvgPicture.asset(
            'assets/icons/arrow_down.svg',
            width: 12,
            color: Color(0xff7e7e7e),
          ),
        ),

        elevation: 2,
        style: TextStyle(
          overflow: TextOverflow.ellipsis,
          color: Colors.black,
          fontSize: 12.0,
          fontWeight: FontWeight.bold,
          fontStyle: FontStyle.normal,
          //fontFamily: 'Visby800'
        ),
        borderRadius: BorderRadius.circular(8),
        iconSize: widget.iconSize!,
        decoration: InputDecoration(
          focusColor: Colors.transparent,
          hoverColor: Colors.transparent,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.all(
              Radius.circular(8),
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.all(
              Radius.circular(8),
            ),
            borderSide: BorderSide(
              color: Color.fromRGBO(227, 227, 227, 1),
              width: 1,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.all(
              Radius.circular(8),
            ),
            borderSide: BorderSide(
              color: Color.fromRGBO(227, 227, 227, 1),
              width: 1,
            ),
          ),
          errorBorder: OutlineInputBorder(
            borderSide: BorderSide(color: Color.fromARGB(255, 234, 28, 28)),
            borderRadius: BorderRadius.all(
              Radius.circular(8),
            ),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderSide: BorderSide(color: Color.fromRGBO(227, 227, 227, 1)),
            borderRadius: BorderRadius.all(
              Radius.circular(8),
            ),
          ),
          hintStyle: TextStyle(
            color: Colors.grey,
            fontSize: 15.0,
            fontWeight: FontWeight.w800,
            fontStyle: FontStyle.normal,
            letterSpacing: 0,
          ),
          
          contentPadding: widget.buttonPadding,
          isDense: false,
          fillColor: Colors.white,
        ),
        
        onChanged: (Map? value) {
          widget.controller!.text = value!['value'];

          if (widget.onChanged != null) {
            widget.onChanged!(value);
          }
        },
        validator: (value) {
          if (widget.validationType != null &&
              widget.parametersValidate != null) {
            if (widget.validationType == 'required' &&
                (value == null || widget.options.indexOf(value) == 0)) {
              return widget.parametersValidate;
            }
          }

          return null;
        },
        dropdownColor: Colors.white,
        selectedItemBuilder: (b) {
          if( widget.selectedItems!.length == 0 ) {
            // Map value = widget.options.firstWhere((e) => e['value'] == widget.controller!.text );
            return widget.options.map<Widget>((dynamic value) {
              return DropdownMenuItem<Map>(
                value: value,
                enabled: value['enabled'].runtimeType.toString() != 'Null' ? value['enabled'] : true,
                child: StatefulBuilder(
                  
                  builder: (context, _setState) {
                    return Opacity(
                      opacity: value['enabled'].runtimeType.toString() != 'Null' ? (value['enabled'] == true ? 1 : 0.8) : 1,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                      
                          widget.enableMultipleSelect == false
                          ? Container()
                          : (
                          value['value'] == '' 
                          ? Container()
                          : Theme(
                              data: Theme.of(context).copyWith(
                                primaryColor: Theme.of(context).primaryColor,
                                unselectedWidgetColor: Color.fromRGBO(219, 219, 219, 1),
                              ),
                              child: Checkbox(
                                value: widget.selectedItems!.indexOf(value) > -1 ? true : false , 
                                onChanged: ( val ) {
                                  try {
                                    if( val == true ) {
                                      widget.selectedItems!.add(value);
                                    } else {
                                      widget.selectedItems!.remove(value);
                                    }
                      
                                    if (widget.onChanged != null) {
                                      widget.onChanged!(val);
                                    }  
                                  } catch (e,s) {
                                    print({e,s});
                                  }
                                  
                      
                                  _setState(() {});
                                  // setState(() {});
                                }
                              ),
                            )
                          ),
                          value['image'] != null
                              ? Row(
                                  children: [
                                    Container(
                                      // height: widget.iconSize,
                                      // width: widget.iconSize,
                                      margin: EdgeInsets.only(left: 5),
                                      // decoration: BoxDecoration(
                                      //   borderRadius: BorderRadius.all(Radius.circular(20)),
                                      // ),
                                      child: getImageWidget(value['image'],iconColor: value['iconColor'].runtimeType.toString() != 'Null' ? value['iconColor'] : Colors.black,),
                                    ),
                                    SizedBox(
                                      width: 20,
                                    )
                                  ],
                                )
                              : SizedBox(
                                  height: 0,
                                ),
                          Expanded(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                NarFormLabelWidget(
                                  label: value['label'],
                                  textColor:  value['labelColor'].runtimeType.toString() != 'Null' ? value['labelColor'] : Colors.black,
                                  fontSize: widget.valueLabelSize,
                                  fontWeight: '600',
                                ),
                                
                                value['tag'].runtimeType.toString() != 'Null' ? value['tag'] : Container()
                              ],
                            ),
                          )
                        ],
                      ),
                    );
                  }
                ) );
              }).toList();
          } else {
            return [
              Container(
                  child: 
                
                    NarFormLabelWidget(
                      label: widget.selectedItems!.map((e) => e['value'] ).join(', '),
                      textColor: Colors.black,
                      fontSize: 14,
                      fontWeight: '600',
                      overflow: TextOverflow.ellipsis,
                    )
                  ,
                )
            ];
          }
        } ,
        items: widget.options.map<DropdownMenuItem<Map>>((dynamic value) {
          return DropdownMenuItem<Map>(

            value: value,
            enabled: value['enabled'].runtimeType.toString() != 'Null' ? value['enabled'] : true,
            child: StatefulBuilder(
              
              builder: (context, _setState) {
                return Opacity(
                  opacity: value['enabled'].runtimeType.toString() != 'Null' ? (value['enabled'] == true ? 1 : 0.7) : 1,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                  
                      widget.enableMultipleSelect == false
                      ? Container()
                      : (
                        value['value'] != '' 
                        ? 
                        Theme(
                          data: Theme.of(context).copyWith(
                            primaryColor: Theme.of(context).primaryColor,
                            unselectedWidgetColor: Color.fromRGBO(219, 219, 219, 1),
                          ),
                          child: Checkbox(
                            value: widget.selectedItems!.indexOf(value) > -1 ? true : false , 
                            onChanged: ( val ) {
                              try {
                                if( val == true ) {
                                  widget.selectedItems!.add(value);
                                } else {
                                  widget.selectedItems!.remove(value);
                                }
                  
                                if (widget.onChanged != null) {
                                  widget.onChanged!(val);
                                }  
                              } catch (e,s) {
                                print({e,s});
                              }
                              
                  
                              _setState(() {});
                              // setState(() {});
                            }
                          ),
                        )
                        
                       
                      : Container() ),
                      value['image'] != null
                          ? Row(
                              children: [
                                // Container(
                                //     height: widget.iconSize,
                                //     width: widget.iconSize,
                                //     margin: EdgeInsets.only(left: 5),
                                //     decoration: BoxDecoration(
                                //       image: DecorationImage(
                                //         fit: BoxFit.cover,
                                //         image: NetworkImage(value['image']),
                                //       ),
                                //       borderRadius: BorderRadius.all(
                                //         Radius.circular(20),
                                //       ),
                                //     ),
                                //     child: SizedBox(height: 0),
                                // ),
                                Container(
                                  height: widget.iconSize,
                                  width: widget.iconSize,
                                  margin: EdgeInsets.only(left: 5),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.all(Radius.circular(20)),
                                  ),
                                  child: getImageWidget(value['image'],iconColor: value['iconColor'].runtimeType.toString() != 'Null' ? value['iconColor'] : Colors.black,),
                                ),
                                SizedBox(
                                  width: 20,
                                )
                              ],
                            )
                          : SizedBox(
                              height: 0,
                            ),
                      Expanded(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            NarFormLabelWidget(
                              label: value['label'],
                              textColor:  value['labelColor'].runtimeType.toString() != 'Null' ? value['labelColor'] : Colors.black,
                              fontSize: widget.valueLabelSize,
                              fontWeight: '600',
                            ),
                            
                            value['tag'].runtimeType.toString() != 'Null' ? value['tag'] : Container()
                          ],
                        ),
                      )
                    ],
                  ),
                );
              }
            ),
          );
        }).toList());
  }

  Widget getImageWidget(String imagePath, {Color? iconColor}) {
    if (imagePath.endsWith('.svg')) {
      return SvgPicture.asset(
        imagePath,
        height: widget.iconSize,
        width: widget.iconSize,
        fit: BoxFit.cover,
        color: iconColor != null
            ? iconColor
            : null, // Apply color only if provided
      );
    } else if (imagePath.startsWith('http')) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: Image.network(
          imagePath,
          height: widget.iconSize,
          width: widget.iconSize,
          fit: BoxFit.cover,
          color: null,
        ),
      );
    } else {
      return ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: Image.asset(
          imagePath,
          height: widget.iconSize,
          width: widget.iconSize,
          fit: BoxFit.cover,
          color: null,
        ),
      );
    }
  }


}
