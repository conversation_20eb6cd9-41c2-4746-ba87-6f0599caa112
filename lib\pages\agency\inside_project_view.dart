import 'dart:developer';
import 'dart:html' as html;
import 'dart:async';
import 'package:newarc_platform/classes/property.dart';
import 'package:newarc_platform/functions/various.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/economics.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/utils/common_utils.dart';
import 'package:newarc_platform/utils/downloadBrochureQuotationPagePDF.dart';
import 'package:newarc_platform/widget/UI/button.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/link.dart';
import 'package:newarc_platform/widget/UI/tab/popup_menu_button.dart';
import 'package:newarc_platform/widget/UI/tab/status_widget.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../classes/immaginaProject.dart';
import '../../functions/various.dart';
import '../../utils/storage.dart';
import '../../widget/UI/base_newarc_button.dart';
import '../../widget/UI/base_newarc_popup.dart';
import '../../widget/UI/custom_textformfield.dart';
import '../../widget/custom_icon_button.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/app_const.dart' as appConst;

import 'package:flutter/services.dart';
import 'package:newarc_platform/utils/downloadAdBrochurePDF.dart';
import 'package:newarc_platform/utils/downloadAdImageBrochure.dart';
import 'dart:js' as js;

class InsideProject {
  final String? value, key;

  InsideProject({
    required this.value,
    required this.key,
  });
}

class InsideProjectNewArc {
  final String? value, image;
  final bool? isRequestCompleted;

  InsideProjectNewArc({
    required this.value,
    this.image,
    this.isRequestCompleted = false,
  });
}

class InsideProjectView extends StatefulWidget {
  InsideProjectView({
    super.key,
    this.isFromRequest = true,
    this.updateViewCallback,
    this.projectArguments = const {},
    this.isFromProjectArchive = false,
    this.projectFirebaseId,
    this.agencyUser,
  });

  // isFromRequest == true ====> Richiest
  // isFromRequest == false =====> Progetti
  final bool? isFromRequest;
  final bool? isFromProjectArchive;
  final Map? projectArguments;
  final String? projectFirebaseId;
  final Function? updateViewCallback;
  final AgencyUser? agencyUser;

  @override
  State<InsideProjectView> createState() => _InsideProjectViewState();
}

class _InsideProjectViewState extends State<InsideProjectView> {
  bool isLevelOneExpanded = true;
  bool isLevelTwoExpanded = true;
  bool isLoading = true;

  List<InsideProject> insideRequest = <InsideProject>[
    InsideProject(value: "Localizzazione", key: 'localizzazione'),
    InsideProject(value: "Info generali", key: 'info_generali'),
    InsideProject(value: "Descrizione", key: 'descrizione'),
    InsideProject(value: "Caratteristiche", key: 'caratteristiche'),
    InsideProject(value: "Planimetria", key: 'planimetria'),
    InsideProject(value: "Fotografie", key: 'fotografie'),
    InsideProject(value: "Info aggiuntive", key: 'indicazioni_speciali'),
  ];

  List<InsideProjectNewArc> newarcProject = <InsideProjectNewArc>[
    InsideProjectNewArc(
      image: "assets/icons/photographs.svg",
      value: "Fotografie",
    ),
    InsideProjectNewArc(
      image: "assets/icons/render.svg",
      value: "Render",
    ),
    InsideProjectNewArc(
      image: "assets/icons/video_render.svg",
      value: "Video Render",
    ),
    InsideProjectNewArc(
      image: "assets/icons/floor_plan.svg",
      value: "Planimetria",
    ),
    InsideProjectNewArc(
      image: "assets/icons/ad_images.svg",
      value: "Immagini annunci",
    ),
    InsideProjectNewArc(
      image: "assets/icons/pdf_brochures.svg",
      value: "Brochure PDF",
    ),
    InsideProjectNewArc(
      image: "assets/icons/virtual_tour.svg",
      value: "Virtual Tour",
    ),
  ];

  ImmaginaProject immaginaProject = ImmaginaProject.empty();

  int? selectedSaleReportDate;

  Property adData = Property.empty();

  bool downloadingPhotos = false;
  bool downloadingRender = false;
  bool downloadingVideo = false;
  bool downloadingPlan = false;
  bool downloadingImages = false;
  bool downloadingPDF = false;
  bool downloadingImmaginiPortali = false;
  bool downloadingBrochureQuotationPagePDF= false;
  NumberFormat localCurrencyFormat =
      NumberFormat.currency(locale: 'it_IT', symbol: '', decimalDigits: 0);

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timestamp) async {
      await initialFetch();
    });
  }

  Future<void> fetchAdData() async {
    if (immaginaProject.propertyId != null) {
      DocumentSnapshot<Map<String, dynamic>> adDataDocRef =
          await FirebaseFirestore.instance
              .collection(appConfig.COLLECT_NEWARC_HOME)
              .doc(immaginaProject.propertyId)
              .get();

      if (adDataDocRef.exists) {
        adData = Property.fromDocument(adDataDocRef);
      }
    }
  }

  Future<void> initialFetch() async {
    try {
      DocumentSnapshot<Map<String, dynamic>> collectionSnapshot;

      collectionSnapshot = await FirebaseFirestore.instance
          .collection(appConfig.COLLECT_IMMAGINA_PROJECTS)
          .doc(
            widget.projectFirebaseId,
          )
          .get();

      if (collectionSnapshot.data() != null) {
        immaginaProject.copy(
          ImmaginaProject.fromDocument(
              collectionSnapshot.data()!, widget.projectFirebaseId!),
        );
        await fetchAdData();
      }
    } catch (e) {
      print(e);
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: LayoutBuilder(builder: (context, constraints) {
        return SingleChildScrollView(
          child: isLoading
              ? SizedBox(
                  height: MediaQuery.of(context).size.height,
                  child: Center(
                    child: CircularProgressIndicator(
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                )
              : Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Row(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          MouseRegion(
                            cursor: SystemMouseCursors.click,
                            child: GestureDetector(
                              onTap: () {
                                widget.projectArguments!.clear();

                                if ((widget.isFromProjectArchive != null) &&
                                    (widget.isFromProjectArchive ?? false)) {
                                  widget.updateViewCallback!('progetti-archiviati',
                                      projectArguments: widget.projectArguments);
                                  widget.projectArguments!.addAll({
                                    'isFromRequest': false,
                                    'isFromProjectArchive': true,
                                  });
                                } else {
                                  widget.updateViewCallback!('progetti-attivi',
                                      projectArguments: widget.projectArguments);
                                  widget.projectArguments!.addAll({
                                    'isFromRequest': false,
                                  });
                                }
                              },
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  SvgPicture.asset(
                                      'assets/icons/arrow_left.svg',
                                      height: 15,
                                      color: Colors.black),
                                  SizedBox(width: 15,),
                                  NarFormLabelWidget(
                                    label: 'Tutti i progetti',
                                    fontSize: 15,
                                    fontWeight: '600',
                                    textDecoration: TextDecoration.underline,
                                    textColor: AppColor.black,
                                  ),
                                ],
                              ),
                            ),
                          ),

                          NarFormLabelWidget(
                            label: "${immaginaProject.streetName ?? 'noStreetName'}, ${immaginaProject.streetNumber ?? 'noStreetNum'} ${immaginaProject.housingUnit != null ? '-' : ''} ${immaginaProject.housingUnit ?? ''}",
                            fontSize: 20,
                            fontWeight: '700',
                            textColor: Colors.black,
                          ),

                          NarFormLabelWidget(
                            label: immaginaProject.projectId ?? "",
                            fontSize: 12,
                            fontWeight: '600',
                            textColor: Color(0xFF6C6C6C),
                          ),

                        ]
                    ),
                    SizedBox(
                      height: 10,
                    ),
                    _levelOne(),
                    SizedBox(
                      height: 18,
                    ),
                    _levelTwo(),
                    SizedBox(
                      height: 18,
                    ),
                    IntrinsicHeight(
                      child: Row(
                        children: [
                          Flexible(
                            flex: 1,
                            child: _announcement(),
                          ),
                          SizedBox(width: 20),
                          Flexible(
                            flex: 1,
                            child: _payments(),
                          ),
                          SizedBox(
                              width: !((widget.isFromProjectArchive != null) &&
                                      (widget.isFromProjectArchive ?? false))
                                  ? 20
                                  : 0),
                          //-------- for progetti-attivi ==> inside project view  hide _sales view
                          !((widget.isFromProjectArchive != null) &&
                                  (widget.isFromProjectArchive ?? false))
                              ? Flexible(
                                  flex: 1,
                                  child: _sale(),
                                )
                              : SizedBox.shrink(),
                        ],
                      ),
                    ),
                  ],
                ),
        );
      }),
    );
  }

  void _showDownloadingDialog(BuildContext context,{ValueNotifier<double>? progress}) {
    showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.black54,
      builder: (_) {
        return Scaffold(
          backgroundColor: Colors.transparent,
          body: Center(
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              decoration: BoxDecoration(
                //color: Colors.black87,
                borderRadius: BorderRadius.circular(30),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  CircularProgressIndicator(color: AppColor.white,),
                  SizedBox(height: 10,),
                  NarFormLabelWidget(
                    textAlign: TextAlign.center,
                    label: "Generazione in corso…",
                    fontSize: 18,
                    fontWeight: '700',
                    textColor: AppColor.white,
                  ),
                  SizedBox(height: 10,),
                  ValueListenableBuilder<double>(
                    valueListenable: progress!,
                    builder: (context, value, child) {
                      return Text(
                        '${value*100}%',
                        style: TextStyle(
                          color: AppColor.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Container _payments() {
    String paymentAmount = immaginaProject.grossSquareFootage! < gSFUpperLimit
        ? "€${ImmaginaProjectEconomics(project: immaginaProject).computeRenderRequestFee().toString()}"
        : "Preventivato";
    // num successFee = 0;
    // if(immaginaProject.sellingPrice != null){
    //   successFee = immaginaProject.sellingPrice! * 0.002; // 0.2% is 0.002
    // }
    num successFee = immaginaProject.appliedSuccessFee != null
        ? double.parse(immaginaProject.appliedSuccessFee!)
        : 0;
    num successFeeTotal = immaginaProject.sellingPrice != null
        ? (successFee * immaginaProject.sellingPrice!) / 100
        : 0;

    return Container(
      padding: EdgeInsets.only(left: 22, right: 22, top: 22, bottom: 22),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          width: 1.7,
          color: Color(
            0xffC3C9CF,
          ),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: NarFormLabelWidget(
                  label: "Pagamenti",
                  fontSize: 16,
                  fontWeight: '700',
                  textColor: Colors.black,
                ),
              ),
            ],
          ),
          SizedBox(
            height: 25,
          ),
          Container(
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                width: 1,
                color: Color(
                  0xffDADADA,
                ),
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: NarFormLabelWidget(
                        label: "Acquisto progetto",
                        fontSize: 12,
                        fontWeight: '600',
                        textColor: Colors.black,
                      ),
                    ),
                    NarFormLabelWidget(
                      label: (immaginaProject.isPaidWithCredits != null &&
                              (immaginaProject.isPaidWithCredits ?? false))
                          ? "1 credito"
                          : paymentAmount,
                      fontSize: 13,
                      fontWeight: '700',
                      textColor: Colors.black,
                    ),
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          NarFormLabelWidget(
                            label: "Locali: ",
                            fontSize: 12,
                            fontWeight: '500',
                            textColor: Color(0xff7B7B7B),
                          ),
                          NarFormLabelWidget(
                            label: immaginaProject.rooms.toString(),
                            fontSize: 12,
                            fontWeight: '700',
                            textColor: Color(0xff7B7B7B),
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 5,
                      ),
                      Row(
                        children: [
                          NarFormLabelWidget(
                            label: "Planimetria: ",
                            fontSize: 12,
                            fontWeight: '500',
                            textColor: Color(0xff7B7B7B),
                          ),
                          NarFormLabelWidget(
                            label: immaginaProject.wantsNewarcPlanimetry
                                ? 'acquistato'
                                : 'fornito',
                            fontSize: 12,
                            fontWeight: '700',
                            textColor: Color(0xff7B7B7B),
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 5,
                      ),
                      Row(
                        children: [
                          NarFormLabelWidget(
                            label: "Foto: ",
                            fontSize: 12,
                            fontWeight: '500',
                            textColor: Color(0xff7B7B7B),
                          ),
                          NarFormLabelWidget(
                            label: immaginaProject.wantsNewarcPictures
                                ? 'acquistato'
                                : 'fornito',
                            fontSize: 12,
                            fontWeight: '700',
                            textColor: Color(0xff7B7B7B),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 5.0),
                  child: Text(""),
                ),
              ],
            ),
          ),
          SizedBox(
            height: 8,
          ),
          Container(
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                width: 1,
                color: Color(
                  0xffDADADA,
                ),
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: NarFormLabelWidget(
                        label: "Success Fee",
                        fontSize: 12,
                        fontWeight: '600',
                        textColor: Colors.black,
                      ),
                    ),
                    NarFormLabelWidget(
                      label: immaginaProject.isAgencyArchived ?? false
                          ? "${CommonUtils().formatStringToDecimal(input: successFeeTotal.toString())}€"
                          : immaginaProject.appliedSuccessFee == "0"
                              ? "-"
                              : "In attesa",
                      fontSize: 13,
                      fontWeight: '700',
                      textColor: Colors.black,
                    ),
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          NarFormLabelWidget(
                            label: "Commissione: ",
                            fontSize: 12,
                            fontWeight: '500',
                            textColor: Color(0xff7B7B7B),
                          ),
                          NarFormLabelWidget(
                            label: immaginaProject.appliedSuccessFee != null
                                ? "${immaginaProject.appliedSuccessFee!.replaceAll('.', ',')}%"
                                : "null",
                            fontSize: 12,
                            fontWeight: '700',
                            textColor: Color(0xff7B7B7B),
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 5,
                      ),
                      Row(
                        children: [
                          NarFormLabelWidget(
                            label: "Vendita: ",
                            fontSize: 12,
                            fontWeight: '500',
                            textColor: Color(0xff7B7B7B),
                          ),
                          NarFormLabelWidget(
                            label: immaginaProject.isHouseSold
                                ? (immaginaProject.sellingPrice != null
                                    ? "${CommonUtils().formatStringToDecimal(input: immaginaProject.sellingPrice.toString())}€"
                                    : "")
                                : "in attesa",
                            fontSize: 12,
                            fontWeight: '700',
                            textColor: Color(0xff7B7B7B),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Container _announcement() {
    return Container(
      //height: 400,
      padding: EdgeInsets.only(left: 22, right: 22, top: 22, bottom: 22),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          width: 1.7,
          color: Color(
            0xffC3C9CF,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Row(
            children: [
              Expanded(
                child: Row(
                  children: [
                    Expanded(
                      child: NarFormLabelWidget(
                        label: "Annuncio",
                        fontSize: 16,
                        fontWeight: '700',
                        textColor: Colors.black,
                      ),
                    ),
                    StatusWidget(
                      status: immaginaProject.propertyId == null ||
                              adData.isActive == null ||
                              !adData.isActive!
                          ? "Non pubblicato"
                          : "Pubblicato",
                      statusColor: immaginaProject.propertyId == null ||
                              adData.isActive == null ||
                              !adData.isActive!
                          ? Color(0xffE82525)
                          : Color(0xff39C14F),
                    ),
                  ],
                ),
              ),
            ],
          ),
          // SizedBox(
          //   height: 25,
          // ),
          // Container(
          //   padding: EdgeInsets.all(12),
          //   decoration: BoxDecoration(
          //     borderRadius: BorderRadius.circular(10),
          //     border: Border.all(
          //       width: 1,
          //       color: Color(
          //         0xffDADADA,
          //       ),
          //     ),
          //   ),
          //   child: Column(
          //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
          //     crossAxisAlignment: CrossAxisAlignment.center,
          //     children: [
          //       Row(
          //         children: [
          //           Expanded(
          //             child: NarFormLabelWidget(
          //               label: "Visite all’annuncio",
          //               fontSize: 12,
          //               fontWeight: '600',
          //               textColor: Colors.black,
          //             ),
          //           ),
          //           SvgPicture.asset(
          //             "assets/icons/eye.svg",
          //             height: 12,
          //             color: Color(0xff7B7B7B),
          //           ),
          //         ],
          //       ),
          //       Padding(
          //         padding: const EdgeInsets.only(top: 8.0),
          //         child: NarFormLabelWidget(
          //           label: "15",
          //           fontSize: 28,
          //           fontWeight: '700',
          //           textColor: Colors.black,
          //         ),
          //       ),
          //       Padding(
          //         padding: const EdgeInsets.only(top: 5.0),
          //         child: Text(""),
          //       ),
          //     ],
          //   ),
          // ),
          // SizedBox(
          //   height: 8,
          // ),
          // Container(
          //   padding: EdgeInsets.all(12),
          //   decoration: BoxDecoration(
          //     borderRadius: BorderRadius.circular(10),
          //     border: Border.all(
          //       width: 1,
          //       color: Color(
          //         0xffDADADA,
          //       ),
          //     ),
          //   ),
          //   child: Column(
          //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
          //     crossAxisAlignment: CrossAxisAlignment.end,
          //     children: [
          //       Row(
          //         children: [
          //           Expanded(
          //             child: NarFormLabelWidget(
          //               label: "Richieste di contatto",
          //               fontSize: 12,
          //               fontWeight: '600',
          //               textColor: Colors.black,
          //             ),
          //           ),
          //           SvgPicture.asset(
          //             "assets/icons/mail.svg",
          //             height: 12,
          //             color: Color(0xff7B7B7B),
          //           ),
          //         ],
          //       ),
          //       Center(
          //         child: Padding(
          //           padding: const EdgeInsets.only(top: 8),
          //           child: NarFormLabelWidget(
          //             label: "2",
          //             fontSize: 28,
          //             fontWeight: '700',
          //             textColor: Colors.black,
          //           ),
          //         ),
          //       ),
          //       Row(
          //         mainAxisAlignment: MainAxisAlignment.end,
          //         children: [
          //           Padding(
          //             padding: const EdgeInsets.only(top: 5.0),
          //             child: NarLinkWidget(
          //               text: "Vai alle richieste",
          //               textColor: Color(0xff4483BC),
          //               fontWeight: '500',
          //               fontSize: 11,
          //               onClick: () {},
          //             ),
          //           ),
          //         ],
          //       ),
          //     ],
          //   ),
          // ),
          // SizedBox(
          //   height: 20,
          // ),
          Spacer(),
          immaginaProject.propertyId != null && adData.isActive!
              ? Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(top: 5.0),
                      child: NarLinkWidget(
                        text: "Vai all’annuncio",
                        textColor: Theme.of(context).primaryColor,
                        fontWeight: '500',
                        fontSize: 13,
                        onClick: () {
                          if (immaginaProject.propertyId != null) {
                            html.window.open(
                                appConst.newarcAdsBaseUrl +
                                    '${immaginaProject.propertyId}',
                                'Newarc Ad');
                          }
                        },
                      ),
                    ),
                  ],
                )
              : SizedBox.shrink(),
          SizedBox(
            height: 25,
          ),
        ],
      ),
    );
  }

  Container _sale() {
    return Container(
      //height: 400,
      padding: EdgeInsets.only(left: 22, right: 22, top: 22, bottom: 22),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor,
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          width: 1.7,
          color: Theme.of(context).primaryColor,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              NarFormLabelWidget(
                label: "Vendita",
                fontSize: 16,
                fontWeight: '700',
                textColor: Colors.white,
              )
            ],
          ),
          SizedBox(
            height: 25,
          ),
          SvgPicture.asset(
            'assets/icons/hand_shake_with_house.svg',
            height: 65,
            width: 65,
          ),
          SizedBox(
            height: 25,
          ),
          NarFormLabelWidget(
            label: "Hai venduto l’immobile?",
            fontSize: 17,
            fontWeight: '700',
            textColor: Colors.white,
          ),
          SizedBox(
            height: 10,
          ),
          NarFormLabelWidget(
            label: "Clicca il tasto qui sotto per chiudere il progetto.",
            fontSize: 13,
            fontWeight: '600',
            textColor: Colors.white,
            textAlign: TextAlign.center,
          ),
          SizedBox(
            height: 30,
          ),
          Container(
            width: 200,
            child: CustomIconButton(
              label: "Segnala vendita",
              textStyle: TextStyle(
                color: Theme.of(context).primaryColor,
                fontSize: 13,
                fontWeight: FontWeight.w600,
              ),
              width: 200,
              height: 50,
              leadingIcon: true,
              icon: Padding(
                padding: const EdgeInsets.only(right: 5.0),
                child: SvgPicture.asset(
                  'assets/icons/hand_shake_blue.svg',
                  color: Theme.of(context).primaryColor,
                ),
              ),
              color: Colors.white,
              function: () {
                _reportSaleFormDialog();
              },
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 5.0),
                child: NarLinkWidget(
                  text: "Chiudi senza vendita",
                  textColor: Color(0xffC0CFDD),
                  fontWeight: '500',
                  fontSize: 13,
                  onClick: () {
                    _showConfirmProjectClosureOrArchiveDialog();
                  },
                ),
              ),
            ],
          )
        ],
      ),
    );
  }

  AnimatedContainer _levelTwo() {
    return AnimatedContainer(
      duration: Duration(milliseconds: 800),
      padding: EdgeInsets.only(left: 0, right: 0, top: 22, bottom: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          width: 1.7,
          color: Color(
            0xffC3C9CF,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 22, vertical: 0),
            child: Row(
              children: [
                Expanded(
                  child: Row(
                    children: [
                      NarFormLabelWidget(
                        label: "Progetto Newarc",
                        fontSize: 16,
                        fontWeight: '700',
                        textColor: Colors.black,
                      ),
                      SizedBox(
                        width: 30,
                      ),
                    ],
                  ),
                ),
                Row(
                  children: [
                    StatusWidget(
                      status: immaginaProject.requestStatus.toLowerCase(),
                      statusColor: CommonUtils.getColor(
                          immaginaProject.requestStatus.toLowerCase()),
                    ),
                    SizedBox(
                      width: 10,
                    ),
                    if (immaginaProject.requestStatus.toLowerCase() ==
                        CommonUtils.bloccata)
                      PopupMenuOnHover(
                        note: immaginaProject.blockedNotes,
                        reason: immaginaProject.blockedSection ?? "",
                      ),
                  ],
                ),
              ],
            ),
          ),
          if (isLevelTwoExpanded) ...[
            Column(
              children: [
                SizedBox(
                  height: 24,
                ),
                Center(
                  child: immaginaProject.requestStatus.toLowerCase() ==
                          CommonUtils.completato
                      ? Wrap(
                          alignment: WrapAlignment.start,
                          crossAxisAlignment: WrapCrossAlignment.center,
                          spacing: 5,
                          runSpacing: 5,
                          children: [
                              Container(
                                // padding: EdgeInsets.all(10),
                                width: 150,
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    SvgPicture.asset(
                                      "assets/icons/im-photograh.svg",
                                      height: 45,
                                    ),
                                    SizedBox(
                                      height: 10,
                                    ),
                                    NarFormLabelWidget(
                                      label: 'Fotografie',
                                      fontSize: 13,
                                      fontWeight: '700',
                                      textColor: Colors.black,
                                    ),
                                    SizedBox(
                                      height: 5,
                                    ),
                                    NarFormLabelWidget(
                                      label: adData.firebaseId != ''
                                          ? adData.photoBeforeAfterPaths!.length
                                                  .toString() +
                                              '  immagini'
                                          : '0 immagina',
                                      fontSize: 10,
                                      fontWeight: '500',
                                      textColor: Color(0xff999999),
                                    ),
                                    SizedBox(
                                      height: 5,
                                    ),
                                    BaseNewarcButton(
                                      buttonText: downloadingRender
                                          ? "Downloading"
                                          : "Scarica",
                                      fontSize: 12,
                                      onPressed: () async {
                                        if (adData.firebaseId != '') {
                                          List<String> images = [];
                                          try {
                                            setState(() {
                                              downloadingRender = true;
                                            });

                                            List<Future<String>> urlFutures =
                                                adData.photoBeforeAfterPaths!
                                                    .map((e) async {
                                              return await printUrl(
                                                  e['after']['location'],
                                                  '',
                                                  e['after']['filename']);
                                            }).toList();

                                            String _filename = (adData
                                                        .addressInfo!
                                                        .streetName!)
                                                    .replaceAll(' ', '_') +
                                                '_' +
                                                (adData.addressInfo!
                                                        .streetNumber!)
                                                    .replaceAll(' ', '_') +
                                                '_' +
                                                adData.addressInfo!.streetName!
                                                    .replaceAll(' ', '_') +
                                                '_fotografie';

                                            images = await Future.wait(urlFutures);
                                            if (images.isNotEmpty) {
                                              await js.context.callMethod(
                                                  'zipAndDownloadFiles', [
                                                js.JsArray.from(images),
                                                "fotografie",
                                                _filename
                                              ]);
                                            }
                                          } finally {
                                            setState(() {
                                              downloadingRender = false;
                                            });
                                          }
                                        }
                                      },
                                      width: 50,
                                      height: 30,
                                    )
                                  ],
                                ),
                              ),
                              Container(
                                padding: EdgeInsets.all(10),
                                width: 150,
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    SvgPicture.asset(
                                      "assets/icons/im-render.svg",
                                      height: 45,
                                    ),
                                    SizedBox(
                                      height: 10,
                                    ),
                                    NarFormLabelWidget(
                                      label: 'Render',
                                      fontSize: 13,
                                      fontWeight: '700',
                                      textColor: Colors.black,
                                    ),
                                    SizedBox(
                                      height: 5,
                                    ),
                                    NarFormLabelWidget(
                                      label: adData.firebaseId != ''
                                          ? adData.photoDayTimePaths!.length
                                                  .toString() +
                                              '  immagini'
                                          : '0 immagina',
                                      fontSize: 10,
                                      fontWeight: '500',
                                      textColor: Color(0xff999999),
                                    ),
                                    SizedBox(
                                      height: 5,
                                    ),
                                    BaseNewarcButton(
                                      buttonText: downloadingPhotos
                                          ? "Downloading"
                                          : "Scarica",
                                      fontSize: 12,
                                      onPressed: () async {
                                        if (adData.firebaseId != '') {
                                          List<String> images = [];
                                          try {
                                            setState(() {
                                              downloadingPhotos = true;
                                            });

                                            String _filename = (adData
                                                        .addressInfo!
                                                        .streetName!)
                                                    .replaceAll(' ', '_') +
                                                '_' +
                                                (adData.addressInfo!
                                                        .streetNumber!)
                                                    .replaceAll(' ', '_') +
                                                '_' +
                                                adData.addressInfo!.streetName!
                                                    .replaceAll(' ', '_') +
                                                '_render';

                                            List<Future<String>> urlFutures =
                                                adData.photoDayTimePaths!
                                                    .map((e) async {
                                              return await printUrl(
                                                  e['location'],
                                                  '',
                                                  e['filename']);
                                            }).toList();

                                            images =
                                                await Future.wait(urlFutures);
                                            if (images.isNotEmpty) {
                                              await js.context.callMethod(
                                                  'zipAndDownloadFiles', [
                                                js.JsArray.from(images),
                                                "render",
                                                _filename
                                              ]);
                                            }
                                          } finally {
                                            setState(() {
                                              downloadingPhotos = false;
                                            });
                                          }
                                        }
                                      },
                                      width: 50,
                                      height: 30,
                                    )
                                  ],
                                ),
                              ),
                              if (adData.videoRenderPaths!.length > 0)
                                Container(
                                  padding: EdgeInsets.all(10),
                                  width: 150,
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      SvgPicture.asset(
                                        "assets/icons/im-video-render.svg",
                                        height: 45,
                                      ),
                                      SizedBox(
                                        height: 10,
                                      ),
                                      NarFormLabelWidget(
                                        label: 'Video Render',
                                        fontSize: 13,
                                        fontWeight: '700',
                                        textColor: Colors.black,
                                      ),
                                      SizedBox(
                                        height: 5,
                                      ),
                                      NarFormLabelWidget(
                                        label: '',
                                        fontSize: 10,
                                        fontWeight: '500',
                                        textColor: Color(0xff999999),
                                      ),
                                      SizedBox(
                                        height: 5,
                                      ),
                                      Opacity(
                                        opacity:
                                            adData.videoRenderPaths!.length > 0
                                                ? 1
                                                : 0.5,
                                        child: BaseNewarcButton(
                                          buttonText: downloadingVideo
                                              ? "Downloading"
                                              : "Scarica",
                                          fontSize: 12,
                                          onPressed: adData.videoRenderPaths!
                                                      .length ==
                                                  0
                                              ? () {}
                                              : () async {
                                                  if (adData.firebaseId != '') {
                                                    List<String> images = [];
                                                    try {
                                                      setState(() {
                                                        downloadingVideo = true;
                                                      });

                                                      List<Future<String>>
                                                          urlFutures = adData
                                                              .videoRenderPaths!
                                                              .map((e) async {
                                                        return await printUrl(
                                                            "newarcHomes/${adData.firebaseId}/video-render/",
                                                            '',
                                                            e);
                                                      }).toList();

                                                      String _filename = (adData
                                                                  .addressInfo!
                                                                  .streetName!)
                                                              .replaceAll(
                                                                  ' ', '_') +
                                                          '_' +
                                                          (adData.addressInfo!
                                                                  .streetNumber!)
                                                              .replaceAll(
                                                                  ' ', '_') +
                                                          '_' +
                                                          adData.addressInfo!
                                                              .streetName!
                                                              .replaceAll(
                                                                  ' ', '_') +
                                                          '_video';

                                                      images =
                                                          await Future.wait(
                                                              urlFutures);
                                                      if (images.isNotEmpty) {
                                                        await js.context.callMethod(
                                                            'zipAndDownloadFiles',
                                                            [
                                                              js.JsArray.from(
                                                                  images),
                                                              "video_render",
                                                              _filename
                                                            ]);
                                                      }
                                                    } finally {
                                                      setState(() {
                                                        downloadingVideo =
                                                            false;
                                                      });
                                                    }
                                                  }
                                                },
                                          width: 50,
                                          height: 30,
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                              Container(
                                padding: EdgeInsets.all(10),
                                width: 150,
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    SvgPicture.asset(
                                      "assets/icons/im-plan.svg",
                                      height: 45,
                                    ),
                                    SizedBox(
                                      height: 10,
                                    ),
                                    NarFormLabelWidget(
                                      label: 'Planimetria',
                                      fontSize: 13,
                                      fontWeight: '700',
                                      textColor: Colors.black,
                                    ),
                                    SizedBox(
                                      height: 5,
                                    ),
                                    NarFormLabelWidget(
                                      label: '',
                                      fontSize: 10,
                                      fontWeight: '500',
                                      textColor: Color(0xff999999),
                                    ),
                                    SizedBox(
                                      height: 5,
                                    ),
                                    BaseNewarcButton(
                                      buttonText: downloadingPlan
                                          ? "Downloading"
                                          : "Scarica",
                                      fontSize: 12,
                                      onPressed: () async {
                                        if (adData.firebaseId != '') {
                                          List<String> images = [];
                                          try {
                                            setState(() {
                                              downloadingPlan = true;
                                            });

                                            List<Future<String>> urlFutures =
                                                adData.picturePaths!
                                                    .map((e) async {
                                              return await printUrl(
                                                  "newarcHomes/${adData.firebaseId}/",
                                                  '',
                                                  e);
                                            }).toList();

                                            String _filename = (adData
                                                        .addressInfo!
                                                        .streetName!)
                                                    .replaceAll(' ', '_') +
                                                '_' +
                                                (adData.addressInfo!
                                                        .streetNumber!)
                                                    .replaceAll(' ', '_') +
                                                '_' +
                                                adData.addressInfo!.streetName!
                                                    .replaceAll(' ', '_') +
                                                '_planimetria';

                                            images =
                                                await Future.wait(urlFutures);
                                            if (images.isNotEmpty) {
                                              await js.context.callMethod(
                                                  'zipAndDownloadFiles', [
                                                js.JsArray.from(images),
                                                "planimetria",
                                                _filename
                                              ]);
                                            }
                                          } finally {
                                            setState(() {
                                              downloadingPlan = false;
                                            });
                                          }
                                        }
                                      },
                                      width: 50,
                                      height: 30,
                                    )
                                  ],
                                ),
                              ),
                              Container(
                                padding: EdgeInsets.all(10),
                                width: 150,
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    SvgPicture.asset(
                                      "assets/icons/im-portali.svg",
                                      height: 45,
                                    ),
                                    SizedBox(
                                      height: 10,
                                    ),
                                    NarFormLabelWidget(
                                      label: 'Immagini portali',
                                      fontSize: 13,
                                      fontWeight: '700',
                                      textColor: Colors.black,
                                    ),
                                    SizedBox(
                                      height: 5,
                                    ),
                                    NarFormLabelWidget(
                                      label: '',
                                      fontSize: 10,
                                      fontWeight: '500',
                                      textColor: Color(0xff999999),
                                    ),
                                    SizedBox(
                                      height: 5,
                                    ),
                                    BaseNewarcButton(
                                      buttonText: "Scarica",
                                      fontSize: 12,
                                      onPressed: () async {
                                        final ValueNotifier<double> progress = ValueNotifier(0);
                                        if (adData.firebaseId != null) {
                                          _showDownloadingDialog(context,progress: progress);
                                          try {
                                            setState(() {
                                              downloadingImmaginiPortali = true;
                                            });
                                            await imageAdsBrochure(adData, {
                                              'title': immaginaProject
                                                      .addressInfo!
                                                      .streetName! +
                                                  ' ' +
                                                  immaginaProject.addressInfo!
                                                      .streetNumber!,
                                              'agencyId':
                                                  immaginaProject.agencyId
                                            },progress: progress);
                                          } finally {
                                            Navigator.of(context,
                                                    rootNavigator: true)
                                                .pop();
                                            setState(() {
                                              downloadingImmaginiPortali = false;
                                            });
                                          }
                                        }
                                      },
                                      width: 50,
                                      height: 30,
                                    )
                                  ],
                                ),
                              ),
                              Container(
                                padding: EdgeInsets.all(10),
                                width: 150,
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    SvgPicture.asset(
                                      "assets/icons/im-pdf.svg",
                                      height: 45,
                                    ),
                                    SizedBox(
                                      height: 10,
                                    ),
                                    NarFormLabelWidget(
                                      label: 'Brochure PDF',
                                      fontSize: 13,
                                      fontWeight: '700',
                                      textColor: Colors.black,
                                    ),
                                    SizedBox(
                                      height: 5,
                                    ),
                                    NarFormLabelWidget(
                                      label: '',
                                      fontSize: 10,
                                      fontWeight: '500',
                                      textColor: Color(0xff999999),
                                    ),
                                    SizedBox(
                                      height: 5,
                                    ),
                                    BaseNewarcButton(
                                      buttonText:"Scarica",
                                      fontSize: 12,
                                      onPressed: () async {
                                        final ValueNotifier<double> progress = ValueNotifier(0.30);
                                        if (adData.firebaseId != null) {
                                          _showDownloadingDialog(context,progress: progress);

                                          try {
                                            setState(() {
                                              downloadingPDF = true;
                                            });
                                            if(adData.brochurePdfPath?.isNotEmpty ?? false){
                                              String url = await printUrl(adData.brochurePdfPath?["location"], '', adData.brochurePdfPath?["filename"]);
                                              List<String> split0 = url.split('/');
                                              List<String> split1 = split0[split0.length - 1].split('?');
                                              List<String> split2 = split1[0].split('%2F');
                                              String filename = split2[split2.length - 1];

                                              html.AnchorElement anchorElement = new html.AnchorElement(href: url);

                                              anchorElement.download = filename;
                                              anchorElement.click();
                                            }
                                            // await pdfAdsBrochure(adData, {
                                            //   'title': immaginaProject
                                            //           .addressInfo!
                                            //           .streetName! +
                                            //       ' ' +
                                            //       immaginaProject.addressInfo!
                                            //           .streetNumber!,
                                            //   'agencyId':
                                            //       immaginaProject.agencyId,
                                            //   'virtualTourUrl':
                                            //       adData.virtualTour
                                            // },progress: progress);
                                          } finally {
                                            progress.value = 1.0;
                                            Navigator.of(context,
                                                    rootNavigator: true)
                                                .pop();
                                            setState(() {
                                              downloadingPDF = false;
                                            });
                                          }
                                        }
                                      },
                                      width: 50,
                                      height: 30,
                                    )
                                  ],
                                ),
                              ),
                              Container(
                                padding: EdgeInsets.all(10),
                                width: 150,
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    SvgPicture.asset(
                                      "assets/icons/im-virtual-tour.svg",
                                      height: 45,
                                    ),
                                    SizedBox(
                                      height: 10,
                                    ),
                                    NarFormLabelWidget(
                                      label: 'Virtual Tour',
                                      fontSize: 13,
                                      fontWeight: '700',
                                      textColor: Colors.black,
                                    ),
                                    SizedBox(
                                      height: 5,
                                    ),
                                    NarFormLabelWidget(
                                      label: '',
                                      fontSize: 10,
                                      fontWeight: '500',
                                      textColor: Color(0xff999999),
                                    ),
                                    SizedBox(
                                      height: 5,
                                    ),
                                    BaseNewarcButton(
                                      buttonText: "Copia link",
                                      fontSize: 12,
                                      onPressed: () async {
                                        if (adData.firebaseId != null) {
                                          Clipboard.setData(ClipboardData(
                                              text: adData.virtualTour!));
                                          ScaffoldMessenger.of(context)
                                              .showSnackBar(SnackBar(
                                                  content: Text(
                                                      "Copied to clipboard!")));
                                        }
                                      },
                                      width: 50,
                                      height: 30,
                                    )
                                  ],
                                ),
                              )
                            ])
                      : SizedBox(),
                ),
                Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(7),
                      color: Color(0xffF1F1F1),
                    ),
                    margin: EdgeInsets.only(top: 20),
                    padding: EdgeInsets.all(10),
                    height: 67,
                    width: 358,
                    child: Center(
                        child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        NarFormLabelWidget(
                          label: 'Stima ristrutturazione',
                          fontSize: 13,
                          textColor: Color(0xff606060),
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            /* NarFormLabelWidget(
                              label: adData.FullRenoIns! != ""
                                  ? localCurrencyFormat.format(
                                          double.tryParse(adData.FullRenoIns!)) +
                                      "€"
                                  : "In attesa",
                              fontSize: 14,
                              textColor: Colors.black,
                            ), */
                            if (adData.FullRenoIns! != "") BaseNewarcButton(
                              buttonText: downloadingBrochureQuotationPagePDF
                                  ? "Generazione in corso…"
                                  : "Scarica",
                              fontSize: 12,
                              onPressed: () async {
                                final ValueNotifier<double> progress = ValueNotifier(0);
                                if (adData.firebaseId != null) {
                                  _showDownloadingDialog(context,progress: progress);

                                  try {
                                    setState(() {
                                      downloadingBrochureQuotationPagePDF = true;
                                    });
                                    await downloadBrochureQuotationPagePDF(adData, {
                                      'title': immaginaProject
                                          .addressInfo!
                                          .streetName! +
                                          ' ' +
                                          immaginaProject.addressInfo!
                                              .streetNumber!,
                                      'agencyId':
                                      immaginaProject.agencyId,
                                    },progress: progress);
                                  } finally {
                                    Navigator.of(context,
                                        rootNavigator: true)
                                        .pop();
                                    setState(() {
                                      downloadingBrochureQuotationPagePDF = false;
                                    });
                                  }
                                }
                              },
                              width: 50,
                              height: 30,
                            )
                          ],
                        )
                      ],
                    )))
              ],
            ),
          ],
          Column(
            children: [
              SizedBox(
                height: 10,
              ),
              Padding(
                padding: const EdgeInsets.only(right: 22),
                child: Align(
                  alignment: Alignment.centerRight,
                  child: InkWell(
                    highlightColor: Colors.transparent,
                    splashColor: Colors.transparent,
                    splashFactory: NoSplash.splashFactory,
                    hoverColor: Colors.transparent,
                    onTap: () {
                      setState(() {
                        isLevelTwoExpanded = !isLevelTwoExpanded;
                      });
                    },
                    child: Image.asset(
                      isLevelTwoExpanded
                          ? 'assets/icons/arrow_up.png'
                          : 'assets/icons/arrow_down.png',
                      height: 15,
                      color: Color(0xff6C6C6C),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
    /*return AnimatedContainer(
      duration: Duration(milliseconds: 800),
      padding: EdgeInsets.only(left: 22, right: 22, top: 22, bottom: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          width: 1.7,
          color: Color(
            0xffC3C9CF,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Row(
                  children: [
                    Expanded(
                      child: NarFormLabelWidget(
                        label: "Progetto Newarc",
                        fontSize: 16,
                        fontWeight: '700',
                        textColor: Colors.black,
                      ),
                    ),
                    StatusWidget(
                      status: immaginaProject.requestStatus.toLowerCase(),
                      statusColor: CommonUtils.getColor(immaginaProject.requestStatus.toLowerCase()),
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (isLevelTwoExpanded) ...[
            Column(
              children: [
                SizedBox(
                  height: 24,
                ),
                immaginaProject.requestStatus.toLowerCase() == CommonUtils.completato ?
                Center(
                  child: Wrap(
                    crossAxisAlignment: WrapCrossAlignment.center,
                    spacing: 55,
                    runSpacing: 20,
                    children: newarcProject.map((data) {
                      return Container(
                        width: 100,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SvgPicture.asset(
                              data.image ?? "",
                            ),
                            SizedBox(
                              height: 10,
                            ),
                            NarFormLabelWidget(
                              label: data.value,
                              fontSize: 13,
                              fontWeight: '600',
                              textColor: Colors.black,
                            ),
                            SizedBox(
                              height: 16,
                            ),
                            NarButtonWidget(
                              textHeight: 1,
                              color: Color(0xff4483BC),
                              hoverColor: Color(0xff4483BC),
                              borderRadius: 8,
                              fontWeight: '600',
                              fontSize: 12,
                              splashColor: Color(0xff4483BC),
                              textColor: Colors.white,
                              borderSideColor: Colors.transparent,
                              text: 'Scarica',
                              buttonPadding: EdgeInsets.only(
                                  left: 20, right: 20, bottom: 0),
                              onClick: () {},
                            ),
                          ],
                        ),
                      );
                    }).toList(),
                  ),
                )
                    :
                SizedBox.shrink(),
                SizedBox(
                  height: 34,
                ),
                Center(
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 100, vertical: 10),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(7),
                      color: Color(0xffF1F1F1),
                    ),
                    child: Column(
                      children: [
                        NarFormLabelWidget(
                          label: "Stima ristrutturazione:",
                          fontSize: 13,
                          fontWeight: '600',
                          textColor: Color(0xff606060),
                        ),
                        NarFormLabelWidget(
                          label: immaginaProject.requestStatus.toLowerCase() == CommonUtils.inLavorazione ? "In attesa" :"55.000 - 65.000",
                          fontSize: 14,
                          fontWeight: '700',
                          textColor: Colors.black,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
          Column(
            children: [
              SizedBox(
                height: 10,
              ),
              Align(
                alignment: Alignment.centerRight,
                child: InkWell(
                  highlightColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  splashFactory: NoSplash.splashFactory,
                  hoverColor: Colors.transparent,
                  onTap: () {
                    setState(() {
                      isLevelTwoExpanded = !isLevelTwoExpanded;
                    });
                  },
                  child: Image.asset(
                    isLevelTwoExpanded
                        ? 'assets/icons/arrow_up.png'
                        : 'assets/icons/arrow_down.png',
                    height: 15,
                    color: Color(0xff6C6C6C),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
    */
  }

  AnimatedContainer _levelOne() {
    return AnimatedContainer(
      duration: Duration(milliseconds: 800),
      padding: EdgeInsets.only(left: 22, right: 22, top: 22, bottom: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          width: 1.7,
          color: Color(
            0xffC3C9CF,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: NarFormLabelWidget(
                  label: "La richiesta",
                  fontSize: 16,
                  fontWeight: '700',
                  textColor: Colors.black,
                ),
              ),
            ],
          ),
          if (isLevelOneExpanded) ...[
            Column(
              children: [
                SizedBox(
                  height: 24,
                ),
                Center(
                  child: Wrap(
                    alignment: WrapAlignment.start,
                    crossAxisAlignment: WrapCrossAlignment.center,
                    spacing: 15,
                    runSpacing: 15,
                    children: insideRequest.map((v) {
                      return Container(
                        width: 133,
                        height: 95,
                        decoration: BoxDecoration(
                          border: Border.all(
                            width: 1,
                            color: Color(0xffDADADA),
                          ),
                          borderRadius: BorderRadius.circular(10),
                          color: ((v.value == "Planimetria" &&
                                      immaginaProject.wantsNewarcPlanimetry) ||
                                  (v.value == "Fotografie" &&
                                      immaginaProject.wantsNewarcPictures))
                              ? Color(0xffDADADA)
                              : AppColor.white,
                        ),
                        padding: EdgeInsets.all(10),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            NarFormLabelWidget(
                              label: v.value,
                              fontSize: 12,
                              fontWeight: '700',
                              textColor: AppColor.black,
                            ),
                            SizedBox(
                              height: 10,
                            ),
                            ((v.value == "Planimetria" &&
                                        immaginaProject
                                            .wantsNewarcPlanimetry) ||
                                    (v.value == "Fotografie" &&
                                        immaginaProject.wantsNewarcPictures))
                                ? Padding(
                                    padding: const EdgeInsets.only(
                                        bottom: 10, top: 10),
                                    child: NarFormLabelWidget(
                                      label: "Richiesto",
                                      fontSize: 12,
                                      fontWeight: '600',
                                      textColor: AppColor.greyColor,
                                    ),
                                  )
                                : Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      NarLinkWidget(
                                        text: "Visualizza",
                                        textColor:
                                            Theme.of(context).primaryColor,
                                        fontWeight: '600',
                                        textAlign: TextAlign.center,
                                        fontSize: 12,
                                        onClick: () {
                                          openDialog(v);
                                        },
                                      ),
                                      SizedBox(
                                        width: 5,
                                      ),
                                      InkWell(
                                        onTap: () {
                                          openDialog(v);
                                        },
                                        child: SvgPicture.asset(
                                          "assets/icons/eye.svg",
                                          height: 8,
                                          color: Theme.of(context).primaryColor,
                                        ),
                                      ),
                                    ],
                                  ),
                          ],
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ],
            ),
          ],
          Column(
            children: [
              SizedBox(
                height: 10,
              ),
              Align(
                alignment: Alignment.centerRight,
                child: InkWell(
                  highlightColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  splashFactory: NoSplash.splashFactory,
                  hoverColor: Colors.transparent,
                  onTap: () {
                    setState(() {
                      isLevelOneExpanded = !isLevelOneExpanded;
                    });
                  },
                  child: Image.asset(
                    isLevelOneExpanded
                        ? 'assets/icons/arrow_up.png'
                        : 'assets/icons/arrow_down.png',
                    height: 15,
                    color: Color(0xff6C6C6C),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void openDialog(InsideProject request) {
    switch (request.key) {
      case "localizzazione":
        _locationViewDialog();
        break;
      case "info_generali":
        _motionless();
        break;
      case "descrizione":
        _description();
        break;
      case "caratteristiche":
        _characteristics();
        break;
      case "planimetria":
        _floorPlan();
        break;
      case "fotografie":
        _photographs();
        break;
      case "indicazioni_speciali":
        _problems();
        break;
      default:
        break;
    }
  }

  _characteristics() {
    return showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Spacer(),
              SizedBox(
                width: 30,
              ),
              NarFormLabelWidget(
                label: "Dotazioni e particolarità",
                textColor: AppColor.black,
                fontWeight: '700',
                fontSize: 17,
              ),
              Spacer(),
              SizedBox(
                width: 30,
                child: InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Icon(
                    Icons.close,
                  ),
                ),
              ),
            ],
          ),
          titlePadding: EdgeInsets.symmetric(horizontal: 10, vertical: 18),
          contentPadding: EdgeInsets.symmetric(horizontal: 40, vertical: 18),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  height: 10,
                ),
                SizedBox(
                  width: 400,
                  child: Padding(
                    padding: const EdgeInsets.only(left: 8.0),
                    child: Wrap(
                      spacing: 20,
                      runSpacing: 20,
                      children: [
                        ...[
                          if (immaginaProject.elevator == true) "Ascensore",
                          if (immaginaProject.hasCantina == true) "Cantina",
                          if (immaginaProject.terrace == true) "Terrazzo",
                          if (immaginaProject.hasConcierge == true)
                            "Portineria",
                          if (immaginaProject.highEfficiencyFrames == true)
                            "Infissi ad alta efficienza",
                          if (immaginaProject.doubleEsposition == true)
                            "Doppia esposizione",
                          if (immaginaProject.tripleEsposition == true)
                            "Tripla esposizione",
                          if (immaginaProject.quadrupleEsposition == true)
                            "Quadrupla esposizione",
                          if (immaginaProject.centralizedHeating == true)
                            "Riscaldamento centralizzato",
                          if (immaginaProject.autonomousHeating == true)
                            "Riscaldamento autonomo",
                          if (immaginaProject.privateGarden == true)
                            "Giardino privato",
                          if (immaginaProject.sharedGarden == true)
                            "Giardino condominiale",
                          if (immaginaProject.surveiledBuilding == true)
                            "Stabile videosorvegliato",
                          if (immaginaProject.nobleBuilding == true)
                            "Stabile signorile",
                          if (immaginaProject.fiber == true) "Fibra ottica",
                          if (immaginaProject.airConditioning == true)
                            "Pred. condizionatore",
                          if (immaginaProject.securityDoor == true)
                            "Porta blindata",
                          if (immaginaProject.tvStation == true) "Impianto TV",
                          if (immaginaProject.alarm == true) "Pred. antifurto",
                          if (immaginaProject.motorizedSunblind == true)
                            "Tapparelle motorizzate",
                          if (immaginaProject.domotizedSunblind == true)
                            "Tapparelle domotizzate",
                          if (immaginaProject.domotizedLights == true)
                            "Luci domotizzate",
                          if (immaginaProject.highFloor == true) "Piano alto",
                          if (immaginaProject.metroVicinity == true)
                            "Vicinanza metro",
                          if (immaginaProject.bigBalconies == true)
                            "Ampi balconi",
                          if (immaginaProject.bigLiving == true)
                            "Grande zona living",
                          if (immaginaProject.doubleBathroom == true)
                            "Doppi servizi",
                          if (immaginaProject.hasGarage == true) "Box o garage",
                          if (immaginaProject.swimmingPool == true) "Piscina",
                        ]
                            .map(
                              (label) => Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.check_box_rounded,
                                    color: Theme.of(context).primaryColor,
                                    size: 18,
                                  ),
                                  SizedBox(width: 10),
                                  NarFormLabelWidget(
                                    label: label,
                                    fontSize: 14,
                                    fontWeight: '600',
                                    textColor: Colors.black,
                                  ),
                                ],
                              ),
                            )
                            .toList(),
                        ...[
                          Container(
                            width: 150,
                            height: 70,
                            child: Row(
                              children: [
                                CustomTextFormField(
                                  label: "Classe energetica",
                                  hintText:
                                      immaginaProject.energyClass.toString(),
                                  fillColor: Color(0xffF5F5F5),
                                  // flex: 1,
                                  isHaveBorder: false,
                                  readOnly: true,
                                ),
                              ],
                            ),
                          ),
                          SizedBox(
                            width: 15,
                          ),
                          Container(
                            width: 150,
                            height: 70,
                            child: Row(
                              children: [
                                CustomTextFormField(
                                  label: "Anno di costruzione",
                                  hintText: immaginaProject.constructionYear
                                      .toString(),
                                  fillColor: Color(0xffF5F5F5),
                                  // flex: 1,
                                  isHaveBorder: false,
                                  readOnly: true,
                                ),
                              ],
                            ),
                          ),
                          SizedBox(
                            width: 15,
                          ),
                          Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                NarFormLabelWidget(
                                  label: 'Esposizione',
                                  textColor: Color(0xff696969),
                                  fontSize: 13,
                                  fontWeight: '500',
                                ),
                                SizedBox(
                                  height: 15,
                                ),
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: immaginaProject.externalEsposition
                                      .map((label) =>
                                          _externalEspositionIcon(label))
                                      .toList(),
                                )
                              ])
                        ]
                      ],
                    ),
                  ),
                ),
                SizedBox(
                  height: 10,
                ),
              ],
            ),
          ),
          backgroundColor: AppColor.white,
        );
      },
    );
  }

  _locationViewDialog() {
    return showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Spacer(),
              SizedBox(
                width: 30,
              ),
              NarFormLabelWidget(
                label: "Localizzazione",
                textColor: AppColor.black,
                fontWeight: '700',
                fontSize: 17,
              ),
              Spacer(),
              SizedBox(
                width: 30,
                child: InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Icon(
                    Icons.close,
                  ),
                ),
              ),
            ],
          ),
          titlePadding: EdgeInsets.symmetric(horizontal: 10, vertical: 18),
          contentPadding: EdgeInsets.symmetric(horizontal: 40, vertical: 18),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                height: 10,
              ),
              Row(
                children: [
                  Expanded(
                    flex: 3,
                    child: Column(
                      children: [
                        CustomTextFormField(
                          isHaveBorder: false,
                          flex: 0,
                          fillColor: Color(0xffF5F5F5),
                          readOnly: true,
                          label: "Indirizzo",
                          controller: TextEditingController(
                            text: "${immaginaProject.streetName ?? ''}",
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(
                    width: 16,
                  ),
                  Expanded(
                    flex: 1,
                    child: Column(
                      children: [
                        CustomTextFormField(
                          isHaveBorder: false,
                          flex: 0,
                          fillColor: Color(0xffF5F5F5),
                          readOnly: true,
                          label: "Numero civico",
                          controller: TextEditingController(
                            text: immaginaProject.streetNumber,
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(
                    width: 47,
                  ),
                  Expanded(
                    flex: 2,
                    child: Column(
                      children: [
                        CustomTextFormField(
                          isHaveBorder: false,
                          flex: 0,
                          fillColor: Color(0xffF5F5F5),
                          readOnly: true,
                          label: "Città",
                          controller: TextEditingController(
                            text: "${immaginaProject.city ?? ''}",
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              SizedBox(
                height: 16,
              ),
              Row(
                children: [
                  Expanded(
                    flex: 3,
                    child: Column(
                      children: [
                        CustomTextFormField(
                          isHaveBorder: false,
                          flex: 0,
                          fillColor: Color(0xffF5F5F5),
                          readOnly: true,
                          label: "Zona",
                          controller: TextEditingController(
                            text: immaginaProject.marketZone ?? "None",
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              SizedBox(
                height: 10,
              ),
            ],
          ),
          backgroundColor: AppColor.white,
        );
      },
    );
  }

  _motionless() {
    return showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Spacer(),
              SizedBox(
                width: 30,
              ),
              NarFormLabelWidget(
                label: "Immobile",
                textColor: AppColor.black,
                fontWeight: '700',
                fontSize: 17,
              ),
              Spacer(),
              SizedBox(
                width: 30,
                child: InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Icon(
                    Icons.close,
                  ),
                ),
              ),
            ],
          ),
          titlePadding: EdgeInsets.symmetric(horizontal: 10, vertical: 18),
          contentPadding: EdgeInsets.symmetric(horizontal: 40, vertical: 18),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                height: 10,
              ),
              Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: Column(
                      children: [
                        CustomTextFormField(
                          isHaveBorder: false,
                          flex: 0,
                          fillColor: Color(0xffF5F5F5),
                          readOnly: true,
                          label: "Tipologia",
                          controller: TextEditingController(
                            text: "${immaginaProject.propertyType ?? "None"}",
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(
                    width: 16,
                  ),
                  Expanded(
                    flex: 1,
                    child: Column(
                      children: [
                        CustomTextFormField(
                          isHaveBorder: false,
                          flex: 0,
                          fillColor: Color(0xffF5F5F5),
                          readOnly: true,
                          label: "Locali",
                          controller: TextEditingController(
                            text: "${immaginaProject.rooms}",
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(
                    width: 16,
                  ),
                  Expanded(
                    flex: 1,
                    child: Column(
                      children: [
                        CustomTextFormField(
                          isHaveBorder: false,
                          flex: 0,
                          fillColor: Color(0xffF5F5F5),
                          readOnly: true,
                          label: "Bagni",
                          controller: TextEditingController(
                            text: "${immaginaProject.numberOfBathrooms}",
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(
                    width: 16,
                  ),
                  Expanded(
                    flex: 1,
                    child: Column(
                      children: [
                        CustomTextFormField(
                          isHaveBorder: false,
                          flex: 0,
                          fillColor: Color(0xffF5F5F5),
                          readOnly: true,
                          label: "Piano",
                          controller: TextEditingController(
                            text: "${immaginaProject.unitFloor}",
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(
                    width: 16,
                  ),
                  Expanded(
                    flex: 1,
                    child: Column(
                      children: [
                        CustomTextFormField(
                          isHaveBorder: false,
                          flex: 0,
                          fillColor: Color(0xffF5F5F5),
                          readOnly: true,
                          label: "Mq",
                          controller: TextEditingController(
                            text: "${immaginaProject.grossSquareFootage}",
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(
                    width: 40,
                  ),
                  Expanded(
                    flex: 2,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomTextFormField(
                          isHaveBorder: false,
                          isMoney: true,
                          flex: 0,
                          fillColor: Color(0xffF5F5F5),
                          readOnly: true,
                          label: "Prezzo di vendita",
                          controller: TextEditingController(
                            text: "${immaginaProject.listingPrice}",
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              SizedBox(
                height: 10,
              ),
            ],
          ),
          backgroundColor: AppColor.white,
        );
      },
    );
  }

  _description() {
    return showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Spacer(),
              SizedBox(
                width: 40,
              ),
              NarFormLabelWidget(
                label: "Descrizione",
                textColor: AppColor.black,
                fontWeight: '700',
                fontSize: 17,
              ),
              SizedBox(
                width: 10,
              ),
              Spacer(),
              SizedBox(
                width: 30,
                child: InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Icon(
                    Icons.close,
                  ),
                ),
              ),
            ],
          ),
          titlePadding: EdgeInsets.symmetric(horizontal: 10, vertical: 20),
          contentPadding: EdgeInsets.only(
            left: 20,
            right: 20,
            bottom: 20,
          ),
          content: Container(
            width: MediaQuery.of(context).size.width / 3,
            padding: EdgeInsets.all(24),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                width: 1.2,
                color: Color(0xffD1D1D1),
              ),
            ),
            child: SingleChildScrollView(
              scrollDirection: Axis.vertical,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  NarFormLabelWidget(
                    fontSize: 14,
                    overflow: TextOverflow.fade,
                    fontWeight: '600',
                    textColor: AppColor.black,
                    label: "${immaginaProject.description}",
                  ),
                ],
              ),
            ),
          ),
          backgroundColor: AppColor.white,
        );
      },
    );
  }

  _problems() {
    return showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Spacer(),
              SizedBox(
                width: 40,
              ),
              NarFormLabelWidget(
                label: "Altre indicazioni",
                textColor: AppColor.black,
                fontWeight: '700',
                fontSize: 17,
              ),
              SizedBox(
                width: 10,
              ),
              Spacer(),
              SizedBox(
                width: 30,
                child: InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Icon(
                    Icons.close,
                  ),
                ),
              ),
            ],
          ),
          titlePadding: EdgeInsets.symmetric(horizontal: 10, vertical: 20),
          contentPadding: EdgeInsets.only(
            left: 20,
            right: 20,
            bottom: 20,
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              NarFormLabelWidget(
                fontSize: 15,
                overflow: TextOverflow.fade,
                fontWeight: '700',
                textColor: AppColor.black,
                label: "Problematiche o difficoltà strutturali",
              ),
              SizedBox(
                height: 5,
              ),
              Container(
                width: MediaQuery.of(context).size.width / 3,
                padding: EdgeInsets.all(24),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(
                    width: 1.2,
                    color: Color(0xffD1D1D1),
                  ),
                ),
                child: SingleChildScrollView(
                  scrollDirection: Axis.vertical,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      NarFormLabelWidget(
                        fontSize: 14,
                        overflow: TextOverflow.fade,
                        fontWeight: '600',
                        textColor: AppColor.black,
                        label: immaginaProject.specialHints ?? "-",
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(
                height: 10,
              ),
              NarFormLabelWidget(
                fontSize: 15,
                overflow: TextOverflow.fade,
                fontWeight: '700',
                textColor: AppColor.black,
                label: "Quando vorresti mettere in vendita l’immobile?",
              ),
              SizedBox(
                height: 5,
              ),
              Container(
                width: MediaQuery.of(context).size.width / 3,
                padding: EdgeInsets.all(24),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(
                    width: 1.2,
                    color: Color(0xffD1D1D1),
                  ),
                ),
                child: SingleChildScrollView(
                  scrollDirection: Axis.vertical,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      NarFormLabelWidget(
                        fontSize: 14,
                        overflow: TextOverflow.fade,
                        fontWeight: '600',
                        textColor: AppColor.black,
                        label: immaginaProject.propertyUpForSaleAnswer ?? "-",
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          backgroundColor: AppColor.white,
        );
      },
    );
  }

  _photographs() {
    return showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Spacer(),
              SizedBox(
                width: 30,
              ),
              NarFormLabelWidget(
                label: "Fotografie",
                textColor: AppColor.black,
                fontWeight: '700',
                fontSize: 17,
              ),
              Spacer(),
              SizedBox(
                width: 30,
                child: InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Icon(
                    Icons.close,
                  ),
                ),
              ),
            ],
          ),
          titlePadding: EdgeInsets.symmetric(horizontal: 10, vertical: 18),
          contentPadding: EdgeInsets.symmetric(horizontal: 40, vertical: 18),
          content: SizedBox(
            width: MediaQuery.of(context).size.width / 1.5,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(
                    height: 10,
                  ),
                  if (immaginaProject.pictures.isNotEmpty)
                    FutureBuilder<List<dynamic>>(
                      future: _fetchImagesFromFolder(immaginaProject.pictures),
                      builder: (context, snapshot) {
                        if (snapshot.connectionState ==
                            ConnectionState.waiting) {
                          return Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Center(child: CircularProgressIndicator()),
                          );
                        } else if (snapshot.hasError) {
                          return Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: NarFormLabelWidget(
                              label: "Error fetching images: ${snapshot.error}",
                              fontSize: 15,
                              fontWeight: '700',
                              textColor: Colors.black,
                            ),
                          );
                        } else if (!snapshot.hasData ||
                            snapshot.data!.isEmpty) {
                          return Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: NarFormLabelWidget(
                              label: "No images found.",
                              fontSize: 15,
                              fontWeight: '700',
                              textColor: Colors.black,
                            ),
                          );
                        } else {
                          final urls = snapshot.data!;
                          return GridView.builder(
                            shrinkWrap: true,
                            physics: NeverScrollableScrollPhysics(),
                            gridDelegate:
                                SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: 3,
                              crossAxisSpacing: 45,
                              mainAxisSpacing: 10,
                              childAspectRatio: 1,
                              mainAxisExtent: 100,
                            ),
                            itemCount: urls.length,
                            itemBuilder: (context, index) {
                              final url = urls[index];
                              return Row(
                                children: [
                                  InkWell(
                                    onTap: () async {
                                      await launchUrl(
                                        Uri.parse(url['file']),
                                      );
                                    },
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(8),
                                      child: Image.network(
                                        url['file'],
                                        width: 100,
                                        height: 100,
                                        fit: BoxFit.cover,
                                        errorBuilder:
                                            (context, error, stackTrace) {
                                          return Icon(Icons.broken_image,
                                              size: 50);
                                        },
                                      ),
                                    ),
                                  ),
                                  SizedBox(width: 10),
                                  Expanded(
                                    flex: 1,
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        NarFormLabelWidget(
                                          label: "Stanza",
                                          fontSize: 13,
                                          fontWeight: '600',
                                          textColor: Color(0xff696969),
                                        ),
                                        SizedBox(height: 5),
                                        CustomTextFormField(
                                          isHaveBorder: false,
                                          flex: 1,
                                          controllerFontSize: 12,
                                          fillColor: Color(0xffF5F5F5),
                                          readOnly: true,
                                          label: "",
                                          controller: TextEditingController(
                                            text: url['tag'],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  SizedBox(width: 10),
                                ],
                              );
                            },
                          );
                        }
                      },
                    ),
                  SizedBox(
                    height: 10,
                  ),
                ],
              ),
            ),
          ),
          backgroundColor: AppColor.white,
        );
      },
    );
  }

  _floorPlan() {
    return showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Spacer(),
              SizedBox(
                width: 30,
              ),
              NarFormLabelWidget(
                label: "Planimetria",
                textColor: AppColor.black,
                fontWeight: '700',
                fontSize: 17,
              ),
              Spacer(),
              SizedBox(
                width: 30,
                child: InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Icon(
                    Icons.close,
                  ),
                ),
              ),
            ],
          ),
          titlePadding: EdgeInsets.symmetric(horizontal: 10, vertical: 18),
          contentPadding: EdgeInsets.symmetric(horizontal: 40, vertical: 18),
          content: SizedBox(
            width: 475,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  height: 10,
                ),
                if (immaginaProject.planimetry.isNotEmpty)
                  Row(
                    children: [
                      Expanded(
                        child: FutureBuilder<List<dynamic>>(
                          future: _fetchPlanmetriaFromFolder(
                              immaginaProject.planimetry),
                          builder: (context, snapshot) {
                            if (snapshot.connectionState ==
                                ConnectionState.waiting) {
                              return Padding(
                                padding: const EdgeInsets.all(8.0),
                                child:
                                    Center(child: CircularProgressIndicator()),
                              );
                            } else if (snapshot.hasError) {
                              return Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: NarFormLabelWidget(
                                  label:
                                      "Error fetching images: ${snapshot.error}",
                                  fontSize: 15,
                                  fontWeight: '700',
                                  textColor: Colors.black,
                                ),
                              );
                            } else if (!snapshot.hasData ||
                                snapshot.data!.isEmpty) {
                              return Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: NarFormLabelWidget(
                                  label: "No images found.",
                                  fontSize: 15,
                                  fontWeight: '700',
                                  textColor: Colors.black,
                                ),
                              );
                            } else {
                              final urls = snapshot.data!;
                              return Wrap(
                                spacing: 10,
                                runSpacing: 10,
                                children: urls.map((toElement) {
                                  return (toElement['file']
                                          .toString()
                                          .contains('.pdf'))
                                      ? InkWell(
                                          onTap: () async {
                                            await launchUrl(
                                              Uri.parse(toElement['file']),
                                            );
                                          },
                                          child: Container(
                                            decoration: BoxDecoration(
                                                color: Color(0xffD9D9D9),
                                                borderRadius:
                                                    BorderRadius.circular(7)),
                                            height: 100,
                                            width: 100,
                                            child: Icon(
                                              Icons.picture_as_pdf,
                                              size: 50,
                                            ),
                                          ),
                                        )
                                      : InkWell(
                                          onTap: () async {
                                            await launchUrl(
                                              Uri.parse(toElement['file']),
                                            );
                                          },
                                          child: ClipRRect(
                                            borderRadius:
                                                BorderRadius.circular(8),
                                            child: Image.network(
                                              toElement['file'],
                                              width: 100,
                                              height: 100,
                                              fit: BoxFit.cover,
                                              errorBuilder:
                                                  (context, error, stackTrace) {
                                                return SizedBox(
                                                  height: 100,
                                                  width: 100,
                                                  child: Icon(
                                                    Icons.broken_image,
                                                    size: 50,
                                                  ),
                                                );
                                              },
                                            ),
                                          ),
                                        );
                                }).toList(),
                              );
                            }
                          },
                        ),
                      ),
                    ],
                  ),
                SizedBox(
                  height: 10,
                ),
              ],
            ),
          ),
          backgroundColor: AppColor.white,
        );
      },
    );
  }

  Future<List<dynamic>> _fetchPlanmetriaFromFolder(List<dynamic> images) async {
    try {
      List<dynamic> data = [];

      for (var item in images) {
        String downloadUrl = await getDownloadLink(
            'immaginaProjects', widget.projectFirebaseId, item);
        data.add({
          "file": downloadUrl,
        });
      }
      return data;
    } catch (e) {
      print('Error fetching files from folder: $e');
      return [];
    }
  }

  Future<List<dynamic>> _fetchImagesFromFolder(List<dynamic> images) async {
    try {
      List<dynamic> data = [];

      for (var item in images) {
        String filePath = item['file'];
        String tag = item['tag'];
        String downloadUrl = await getDownloadLink(
            'immaginaProjects', widget.projectFirebaseId, filePath);
        data.add({
          "file": downloadUrl,
          "tag": tag,
        });
      }
      return data;
    } catch (e) {
      print('Error fetching files from folder: $e');
      return [];
    }
  }

  Widget _externalEspositionIcon(String label) {
    return Padding(
      padding: const EdgeInsets.only(right: 8.0),
      child: OutlinedButton(
        style: OutlinedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20.0),
          ),
          side: BorderSide(
            color: Colors.transparent,
          ),
          backgroundColor: Color(0xffF5F5F5),
        ),
        onPressed: null,
        child: Text(
          label,
          style: TextStyle(
            color: Theme.of(context).disabledColor,
            fontSize: 14,
            fontFamily: 'Raleway-700',
          ),
        ),
      ),
    );
  }

  //------- for Confirm Project Closure Archive dialog
  Future<void> _showConfirmProjectClosureOrArchiveDialog() {
    return showDialog(
      context: context,
      builder: (context) {
        bool dialogLoading = false;
        return StatefulBuilder(builder: (context, setDialogState) {
          return Center(
            child: BaseNewarcPopup(
              noButton: true,
              title: "Conferma chiusura progetto",
              column: Container(
                width: 400,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Stack(
                      alignment: Alignment.center,
                      children: [
                        // SVG image
                        SvgPicture.asset(
                          "assets/icons/hand_shake_blue.svg",
                          height: 52,
                          width: 59,
                          color: Colors.grey,
                        ),
                        // Red cross line
                        CustomPaint(
                          size: const Size(59, 52), // Match SVG size
                          painter: CrossLinePainter(),
                        ),
                      ],
                    ),
                    SizedBox(height: 20),
                    NarFormLabelWidget(
                      label:
                          'Vuoi chiudere il progetto senza aver venduto l’immobile?',
                      fontSize: 20,
                      textAlign: TextAlign.center,
                      fontWeight: '700',
                      textColor: AppColor.black,
                    ),
                    SizedBox(height: 10),
                    NarFormLabelWidget(
                      label:
                          'Il progetto sarà spostato nella sezione “Archiviati” e l’annuncio sarà rimosso dal portale.',
                      fontSize: 15,
                      textAlign: TextAlign.center,
                      fontWeight: '600',
                      textColor: AppColor.black,
                    ),
                    SizedBox(height: 15),
                    NarFormLabelWidget(
                      label:
                          'La fattura relativa alla success fee non sarà emessa',
                      fontSize: 15,
                      textAlign: TextAlign.center,
                      fontWeight: '600',
                      textColor: AppColor.black,
                    ),
                    SizedBox(height: 50),
                    dialogLoading
                        ? Center(
                            child: CircularProgressIndicator(
                              color: Theme.of(context).primaryColor,
                            ),
                          )
                        : BaseNewarcButton(
                            //width: 142,
                            textColor: AppColor.white,
                            color: Color(0xffE82525),
                            buttonText: "Conferma chiusura senza vendita",
                            onPressed: () async {
                              AgencyUser agencyUser =
                                  widget.projectArguments!["agencyUser"];
                              setDialogState(() {
                                dialogLoading = true;
                              });

                          String projectId = widget.projectArguments!["projectFirebaseId"];
                          try {
                            final collectionRef = FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS);
                            await collectionRef.doc(projectId).update({
                              'isAgencyArchived': true,
                              'isHouseSold': false
                            });
                            Map<String, dynamic> workEmailVariables = {
                              'agencyname': agencyUser.agency!.name,
                              'immaginaProjectId': immaginaProject.projectId
                            };
                            // project archive send notification mail
                            sendEmail(templateId: CommonUtils.agencyProjectArchiveEmailTemplateId, agency: agencyUser.agency!, subject: CommonUtils.agencyProjectArchiveEmailSubject);
                            final renderistUsers = await FirebaseFirestore.instance
                              .collection(appConfig.COLLECT_USERS)
                              .where('type', isEqualTo: 'newarc')
                              .where('role', isEqualTo: 'master-renderist')
                              .where('isActive', isEqualTo: true)
                              .get();
                            final masterUsers = await FirebaseFirestore.instance
                              .collection(appConfig.COLLECT_USERS)
                              .where('type', isEqualTo: 'newarc')
                              .where('role', isEqualTo: 'master')
                              .where('isActive', isEqualTo: true)
                              .get();
                            for (var userDoc in renderistUsers.docs + masterUsers.docs) {
                              final userData = userDoc.data();
                              if (userData['email'] != null) {
                                sendEmail(
                                  templateId: CommonUtils.agencyProjectArchiveForWorkSideEmailTemplateId, 
                                  agency: agencyUser.agency!, 
                                  subject: CommonUtils.agencyProjectArchiveForWorkSideEmailSubject, 
                                  variables: workEmailVariables, 
                                  recipientEmail: userData['email'], 
                                  recipientName: userData['firstName'] != null ? "${userData['firstName']} ${userData['lastName'] ?? ''}" : "Renderist",
                                );
                              }
                            }
                            widget.projectArguments!.clear();
                            widget.projectArguments!.addAll({
                              'isFromRequest': false,
                            });
                            widget.updateViewCallback!('progetti-attivi', projectArguments: widget.projectArguments);
                            Navigator.pop(context);

                          } catch (e) {
                            log("Error updating document: $e");
                          } finally{
                            setDialogState(() {
                              dialogLoading = true;
                            });
                          }
                        },
                      ),
                    ],
                  ),
                ),
              ),
            );
          });
      },
    );
  }

  Future<void> _showSuccessFeeDialog(
      {required bool isSuccessFee,
      String? successFee,
      String? sellingPrice,
      required Map<Object, Object?> archiveData,
      required String? projectId}) {
    return showDialog(
      context: context,
      barrierDismissible: isSuccessFee,
      builder: (context) {
        bool dialogLoading = false;
        bool _isFrozen = false;
        String paymentErrorMessage = "";
        return StatefulBuilder(builder: (context, setDialogState) {
          return Stack(
            children: [
              Center(
                child: BaseNewarcPopup(
                  noButton: true,
                  isShowCloseIcon: isSuccessFee,
                  title: "Conferma chiusura progetto",
                  column: Container(
                    width: 400,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        SvgPicture.asset(
                          "assets/icons/hand_shake_blue.svg",
                          height: 52,
                          width: 59,
                          color: Colors.grey,
                        ),
                        SizedBox(height: 20),
                        NarFormLabelWidget(
                          label: "Congratulazioni per la vendita!",
                          fontSize: 20,
                          textAlign: TextAlign.center,
                          fontWeight: '700',
                          textColor: AppColor.black,
                        ),
                        SizedBox(height: 10),
                        NarFormLabelWidget(
                          label:
                              "Il progetto sarà spostato nella sezione archiviati e l’annuncio sarà rimosso dal portale.",
                          fontSize: 15,
                          textAlign: TextAlign.center,
                          fontWeight: '600',
                          textColor: AppColor.black,
                        ),
                        SizedBox(height: 50),
                        isSuccessFee
                            ? Container(
                                padding: EdgeInsets.all(10.0),
                                decoration: BoxDecoration(
                                    color: Color(0xffEDF7FF),
                                    borderRadius: BorderRadius.circular(15),
                                    border: Border.all(
                                        color: Theme.of(context).primaryColor,
                                        width: 1.0)),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    NarFormLabelWidget(
                                      label: "Success Fee",
                                      fontSize: 17,
                                      textAlign: TextAlign.center,
                                      fontWeight: '700',
                                      textColor: Theme.of(context).primaryColor,
                                    ),
                                    SizedBox(
                                      height: 5,
                                    ),
                                    NarFormLabelWidget(
                                      label:
                                          "Prezzo di vendita: ${sellingPrice}€",
                                      fontSize: 13,
                                      textAlign: TextAlign.center,
                                      fontWeight: '600',
                                      textColor: Theme.of(context).primaryColor,
                                    ),
                                    SizedBox(
                                      height: 5,
                                    ),
                                    NarFormLabelWidget(
                                      label:
                                          "Success Fee: ${successFee}€ + IVA",
                                      fontSize: 13,
                                      textAlign: TextAlign.center,
                                      fontWeight: '600',
                                      textColor: Theme.of(context).primaryColor,
                                    ),
                                    SizedBox(
                                      height: 20,
                                    ),
                                    paymentErrorMessage.isNotEmpty
                                        ? Padding(
                                            padding: const EdgeInsets.only(
                                                bottom: 10.0),
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                Text(
                                                  paymentErrorMessage,
                                                  textAlign: TextAlign.center,
                                                  style: TextStyle(
                                                      fontFamily: 'Raleway-500',
                                                      fontSize: 14,
                                                      color: Colors.black),
                                                ),
                                              ],
                                            ),
                                          )
                                        : SizedBox(),
                                    dialogLoading
                                        ? Center(
                                            child: CircularProgressIndicator(
                                              color: Theme.of(context)
                                                  .primaryColor,
                                            ),
                                          )
                                        : Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              BaseNewarcButton(
                                                textColor: AppColor.white,
                                                fontSize: 13,
                                                color: Theme.of(context)
                                                    .primaryColor,
                                                buttonText: "Salda con carte",
                                                onPressed: () async {
                                                  AgencyUser agencyUser =
                                                      widget.projectArguments![
                                                          "agencyUser"];
                                                  try {
                                                    setDialogState(() {
                                                      dialogLoading = true;
                                                      _isFrozen = true;
                                                      paymentErrorMessage = "";
                                                    });
                                                    // open tab before async calls so to avoid iOS popup blocking
                                                    final newTab = html.window.open('', '_blank');
                                                    final docRef = FirebaseFirestore
                                                        .instance
                                                        .collection(appConfig
                                                            .COLLECT_IMMAGINA_PROJECTS)
                                                        .doc(projectId);
                                                    // check if payment has already been made
                                                    var element =
                                                        await docRef.get();
                                                    var data = element.data();
                                                    if (data != null &&
                                                        data.containsKey(
                                                            'receivedSuccessFeePaymentDate') &&
                                                        data['receivedSuccessFeePaymentDate'] !=
                                                            null) {
                                                      Navigator.of(context)
                                                          .pop();
                                                      setDialogState(() {
                                                        _isFrozen = false;
                                                        paymentErrorMessage =
                                                            "Il pagamento risulta già effettuato. Prova a ricaricare la pagina web.";
                                                        dialogLoading = false;
                                                      });
                                                      return;
                                                    }
                                                    // update project with success fee payment data user inputted without archiving
                                                    // so that stripe can access document info and compute dynamic payment
                                                    // and updating object with receivedSuccessFeePaymentDate = null
                                                    // so that docRef receivedSuccessFeePaymentDate exists even for old immaginaProjects
                                                    Map<String, dynamic>
                                                        archiveDataWithoutArchived =
                                                        {};
                                                    archiveData
                                                        .forEach((key, value) {
                                                      if (key !=
                                                          'isAgencyArchived') {
                                                        archiveDataWithoutArchived[
                                                                key.toString()] =
                                                            value;
                                                      }
                                                    });
                                                    await docRef.update(
                                                        archiveDataWithoutArchived);

                                                    bool wasPaymentDetected =
                                                        false;

                                        // Get payment link, redirect and set up freeze screen listener
                                        Map linkMap = await getStripeCheckoutLink([], projectId, agencyUser.agencyId!, origin: "success_fee");
                                        final paymentLink = linkMap['link'];
                                        if (paymentLink == null) {
                                          throw Exception("Payment link not found");
                                        }
                                        // Assign the Stripe payment link to the tab
                                        newTab.location.href = linkMap['link']!;
                                        // Check if the user closed the tab without completing payment
                                        Future.doWhile(() async {
                                          await Future.delayed(Duration(seconds: 1));
                                          return !wasPaymentDetected && newTab.closed == false;
                                        }).then((_) {
                                          if (!wasPaymentDetected) {
                                            setDialogState(() {
                                              dialogLoading = false;
                                              _isFrozen = false;
                                              paymentErrorMessage = "Qualcosa è andato storto con il pagamento, riprova.";
                                            });
                                          }
                                        });
                                        // Received payment listener
                                        StreamSubscription? subscription;
                                        subscription = docRef.snapshots().listen(
                                              (event) async {
                                            
                                            var temp = event.data();
                                            if (temp != null && temp['receivedSuccessFeePaymentDate'] != null && temp.containsKey('receivedSuccessFeePaymentDate')) {
                                              await subscription?.cancel();
                                              Map<String, dynamic> workEmailVariables = {
                                                'agencyname': agencyUser.agency!.name,
                                                'immaginaProjectId': immaginaProject.projectId
                                              };
                                              // project archive send notification mail
                                              sendEmail(templateId: CommonUtils.agencyProjectArchiveEmailTemplateId, agency: agencyUser.agency!, subject: CommonUtils.agencyProjectArchiveEmailSubject);
                                              final renderistUsers = await FirebaseFirestore.instance
                                                .collection(appConfig.COLLECT_USERS)
                                                .where('type', isEqualTo: 'newarc')
                                                .where('role', isEqualTo: 'master-renderist')
                                                .where('isActive', isEqualTo: true)
                                                .get();
                                              final masterUsers = await FirebaseFirestore.instance
                                                .collection(appConfig.COLLECT_USERS)
                                                .where('type', isEqualTo: 'newarc')
                                                .where('role', isEqualTo: 'master')
                                                .where('isActive', isEqualTo: true)
                                                .get();
                                              for (var userDoc in renderistUsers.docs + masterUsers.docs) {
                                                final userData = userDoc.data();
                                                if (userData['email'] != null) {
                                                  sendEmail(
                                                    templateId: CommonUtils.agencyProjectArchiveForWorkSideEmailTemplateId, 
                                                    agency: agencyUser.agency!, 
                                                    subject: CommonUtils.agencyProjectArchiveForWorkSideEmailSubject, 
                                                    variables: workEmailVariables, 
                                                    recipientEmail: userData['email'], 
                                                    recipientName: userData['firstName'] != null ? "${userData['firstName']} ${userData['lastName'] ?? ''}" : "Renderist",
                                                  );
                                                }
                                              }
                                              wasPaymentDetected = true;
                                              setDialogState(() {
                                                dialogLoading = false;
                                                _isFrozen = false;
                                                paymentErrorMessage = "";
                                              });
                                              Navigator.of(context).pop();
                                              widget.projectArguments!.clear();
                                              widget.projectArguments!.addAll({
                                                'isFromRequest': false,
                                              });
                                              widget.updateViewCallback!('progetti-attivi', projectArguments: widget.projectArguments);
                                            }
                                          },
                                          onError: (error) {
                                            print("Listen failed: $error");
                                            setDialogState(() {
                                              dialogLoading = false;
                                              _isFrozen = false;
                                              paymentErrorMessage = "Errore generico, ricarica la pagina web e riprova.";
                                            });
                                            subscription?.cancel();
                                          },
                                        );
                                      } catch (e) {
                                        print("Error in success fee stripe payment button $e");
                                        setDialogState(() {
                                          dialogLoading = false;
                                          _isFrozen = false;
                                          paymentErrorMessage = "Errore generico, ricarica la pagina web e riprova.";
                                        });
                                      }
                                    },
                                  ),
                                  // SizedBox(width: 20,),
                                  // BaseNewarcButton(
                                  //   textColor: Theme.of(context).primaryColor,
                                  //   fontSize: 13,
                                  //   borderColor: Theme.of(context).primaryColor,
                                  //   color:  AppColor.white,
                                  //   buttonText: "Salda con bonifico",
                                  //   onPressed: () async {
              
                                  //     final parentContext = context;
              
                                  //     final collectionRef = FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS);
              
                                  //     DocumentSnapshot<Map<String, dynamic>> data = await collectionRef.doc(projectId).get();
              
                                  //     Navigator.pop(context);
              
                                  //     String immaginaProjectID = await data.data()?["projectId"];

                                  //                 log("successFee $successFee");

                                  //                 _payByBankTransferDialog(
                                  //                     dialogContext:
                                  //                         parentContext,
                                  //                     successFee:
                                  //                         successFee ?? "",
                                  //                     immaginaProjectID:
                                  //                         immaginaProjectID,
                                  //                     archiveData: archiveData,
                                  //                     firebaseProjectID:
                                  //                         projectId ?? "");
                                  //               },
                                  //             ),
                                            ],
                                          )
                                  ],
                                ),
                              )
                            : dialogLoading
                                ? Center(
                                    child: CircularProgressIndicator(
                                      color: Theme.of(context).primaryColor,
                                    ),
                                  )
                                : BaseNewarcButton(
                                    //width: 142,
                                    textColor: AppColor.white,
                                    color: Theme.of(context).primaryColor,
                                    buttonText: "Chiudi",
                                    onPressed: () async {
                                      AgencyUser agencyUser = widget
                                          .projectArguments!["agencyUser"];
                                      try {
                                        setDialogState(() {
                                          dialogLoading = true;
                                        });

                              final collectionRef = FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS);
                              archiveData['receivedSuccessFeePaymentDate'] = null;
                              await collectionRef.doc(projectId ?? "").update(archiveData);
                              
                              Map<String, dynamic> workEmailVariables = {
                              'agencyname': agencyUser.agency!.name,
                              'immaginaProjectId': immaginaProject.projectId
                            };
                              // project archive send notification mail
                              sendEmail(templateId: CommonUtils.agencyProjectArchiveEmailTemplateId, agency: agencyUser.agency!, subject: CommonUtils.agencyProjectArchiveEmailSubject);
                              final renderistUsers = await FirebaseFirestore.instance
                                .collection(appConfig.COLLECT_USERS)
                                .where('type', isEqualTo: 'newarc')
                                .where('role', isEqualTo: 'master-renderist')
                                .where('isActive', isEqualTo: true)
                                .get();
                              final masterUsers = await FirebaseFirestore.instance
                                .collection(appConfig.COLLECT_USERS)
                                .where('type', isEqualTo: 'newarc')
                                .where('role', isEqualTo: 'master')
                                .where('isActive', isEqualTo: true)
                                .get();
                              for (var userDoc in renderistUsers.docs + masterUsers.docs) {
                                final userData = userDoc.data();
                                if (userData['email'] != null) {
                                  sendEmail(
                                    templateId: CommonUtils.agencyProjectArchiveForWorkSideEmailTemplateId, 
                                    agency: agencyUser.agency!, 
                                    subject: CommonUtils.agencyProjectArchiveForWorkSideEmailSubject, 
                                    variables: workEmailVariables, 
                                    recipientEmail: userData['email'], 
                                    recipientName: userData['firstName'] != null ? "${userData['firstName']} ${userData['lastName'] ?? ''}" : "Renderist",
                                  );
                                }
                              }
                              Navigator.pop(context);
                              widget.projectArguments!.clear();
                              widget.projectArguments!.addAll({
                                'isFromRequest': false,
                              });
                              widget.updateViewCallback!('progetti-attivi', projectArguments: widget.projectArguments);
              
                            } catch (e) {
                              log("Error Updating Immagina Project Document: $e");
                            } finally {
                              // Reset loading state
                              setDialogState(() {
                                dialogLoading = false;
                              });
                            }
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              if (_isFrozen)
                Positioned.fill(
                  child: Container(
                    color: Colors.black54, // Semi-transparent overlay
                  ),
                ),
            ],
          );
        });
      },
    );
  }

  Future<void> _reportSaleFormDialog() {
    return showDialog(
      context: context,
      builder: (context) {
        TextEditingController sellingPriceController = TextEditingController();
        TextEditingController dateOfSaleDeedController =
            TextEditingController();
        TextEditingController buyerNameController = TextEditingController();
        TextEditingController buyerPhoneNumberController =
            TextEditingController();
        TextEditingController buyerEmailController = TextEditingController();

        bool areAllFieldsFilled = false;

        return StatefulBuilder(
          builder: (context, setDialogState) {
            void checkFields() {
              final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
              bool allFilled = sellingPriceController.text.isNotEmpty &&
                  dateOfSaleDeedController.text.isNotEmpty &&
                  buyerNameController.text.isNotEmpty &&
                  buyerPhoneNumberController.text.isNotEmpty &&
                  buyerEmailController.text.isNotEmpty &&
                  emailRegex.hasMatch(buyerEmailController.text);

              if (allFilled != areAllFieldsFilled) {
                setDialogState(() {
                  areAllFieldsFilled = allFilled;
                });
              }
            }

            for (var controller in [
              sellingPriceController,
              dateOfSaleDeedController,
              buyerNameController,
              buyerPhoneNumberController,
              buyerEmailController,
            ]) {
              controller.addListener(checkFields);
            }

            return Center(
              child: BaseNewarcPopup(
                noButton: true,
                title: "Segnala la vendita",
                column: Container(
                  width: 300,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      CustomTextFormField(
                        textAlign: TextAlign.left,
                        isHaveBorder: true,
                        isCenterLabel: false,
                        flex: 0,
                        suffixIcon: Container(
                          width: 14,
                          padding: const EdgeInsets.only(right: 10),
                          child: Align(
                            alignment: Alignment.centerRight,
                            child: Text(
                              "€",
                              style: TextStyle(
                                color: Colors.grey,
                                fontSize: 14,
                                fontWeight: FontWeight.w700,
                                fontFamily: 'Raleway-700',
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                        readOnly: false,
                        label: "Prezzo di vendita",
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Campo obbligatorio';
                          }
                          // Remove any commas and euro symbols
                          String cleanValue = value
                              .replaceAll('.', '')
                              .replaceAll('€', '')
                              .trim();
                          // Try parsing as number
                          try {
                            double.parse(cleanValue);
                          } catch (e) {
                            return 'Inserire un numero valido';
                          }
                          return null;
                        },
                        controller: sellingPriceController,
                        isMoney: true,
                      ),
                      SizedBox(height: 10),
                      CustomTextFormField(
                        textAlign: TextAlign.left,
                        isHaveBorder: true,
                        isCenterLabel: false,
                        flex: 0,
                        suffixIcon: Container(
                          padding: const EdgeInsets.all(10),
                          height: 20,
                          width: 20,
                          child: Image.asset('assets/icons/calendar.png'),
                        ),
                        readOnly: true,
                        label: "Data atto di vendita",
                        controller: dateOfSaleDeedController,
                        onTap: () async {
                          DateTime? pickedDate = await showDatePicker(
                            context: context,
                            initialDate: DateTime.now(),
                            firstDate: DateTime(1950),
                            lastDate: DateTime(2300),
                          );

                          if (pickedDate != null) {
                            setState(() {
                              selectedSaleReportDate =
                                  pickedDate.millisecondsSinceEpoch;
                            });
                            String formattedDate =
                                DateFormat('dd/MM/yyyy').format(pickedDate);
                            dateOfSaleDeedController.text = formattedDate;
                          } else {
                            log("No Date Selected");
                          }
                        },
                      ),
                      SizedBox(height: 10),
                      CustomTextFormField(
                        textAlign: TextAlign.left,
                        isHaveBorder: true,
                        isCenterLabel: false,
                        flex: 0,
                        suffixIcon: null,
                        readOnly: false,
                        label: "Nome e cognome acquirente",
                        controller: buyerNameController,
                      ),
                      SizedBox(height: 10),
                      CustomTextFormField(
                        textAlign: TextAlign.left,
                        isHaveBorder: true,
                        isCenterLabel: false,
                        flex: 0,
                        suffixIcon: null,
                        readOnly: false,
                        label: "Telefono acquirente",
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Telefono è obbligatorio';
                          }
                          final phoneRegex = RegExp(r'^\+?\d+$');
                          if (!phoneRegex.hasMatch(value)) {
                            return 'Inserisci un numero di telefono valido';
                          }
                          return null;
                        },
                        controller: buyerPhoneNumberController,
                        isNumber: true,
                      ),
                      SizedBox(height: 10),
                      CustomTextFormField(
                        textAlign: TextAlign.left,
                        isHaveBorder: true,
                        isCenterLabel: false,
                        flex: 0,
                        suffixIcon: null,
                        readOnly: false,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Email è obbligatoria';
                          }
                          final emailRegex =
                              RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
                          if (!emailRegex.hasMatch(value)) {
                            return 'Inserisci un indirizzo email valido';
                          }
                          return null;
                        },
                        label: "E-mail acquirente",
                        controller: buyerEmailController,
                      ),
                      SizedBox(height: 48),
                      BaseNewarcButton(
                        width: 142,
                        textColor: areAllFieldsFilled
                            ? AppColor.white
                            : Color(0xffA6A6A6),
                        color: areAllFieldsFilled
                            ? Theme.of(context).primaryColor
                            : Color(0xffE6E6E6),
                        buttonText: "Avanti",
                        disableButton: !areAllFieldsFilled,
                        onPressed: areAllFieldsFilled
                            ? () async {
                                String projectId = widget
                                    .projectArguments?["projectFirebaseId"];
                                final immaginaProjectDoc =
                                    await FirebaseFirestore.instance
                                        .collection(
                                            appConfig.COLLECT_IMMAGINA_PROJECTS)
                                        .doc(projectId)
                                        .get();

                                if (!immaginaProjectDoc.exists) {
                                  throw Exception('Project document not found');
                                }

                                final successFee = immaginaProjectDoc
                                    .data()!['appliedSuccessFee'];
                                int sellingPrice = int.parse(
                                    sellingPriceController.text
                                        .replaceAll(r'.', ''));

                                Map<Object, Object?> archiveData = {
                                  'isAgencyArchived': true,
                                  'isHouseSold': true,
                                  'acquirerName': buyerNameController.text,
                                  'acquirerPhoneNumber':
                                      buyerPhoneNumberController.text,
                                  'acquirerEmailId': buyerEmailController.text,
                                  'sellingDate': selectedSaleReportDate,
                                  'sellingPrice': sellingPrice,
                                };
                                if (successFee == "0") {
                                  Navigator.pop(context);
                                  _showSuccessFeeDialog(
                                      isSuccessFee: false,
                                      archiveData: archiveData,
                                      projectId: projectId);
                                } else {
                                  Navigator.pop(context);
                                  String sellingPriceText =
                                      sellingPriceController.text;
                                  int successFeeNumber =
                                      (double.parse(successFee) *
                                              sellingPrice /
                                              100)
                                          .round();
                                  String successFeeText = CommonUtils()
                                      .formatStringToDecimal(
                                          input: successFeeNumber.toString());
                                  _showSuccessFeeDialog(
                                      isSuccessFee: true,
                                      sellingPrice: sellingPriceText,
                                      successFee: successFeeText,
                                      archiveData: archiveData,
                                      projectId: projectId);
                                }
                              }
                            : () {},
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  // _payByBankTransferDialog(
  //     {required BuildContext dialogContext,
  //     required String successFee,
  //     required String immaginaProjectID,
  //     required Map<Object, Object?>? archiveData,
  //     required String firebaseProjectID}) {
  //   return showDialog(
  //     context: dialogContext,
  //     barrierDismissible: false,
  //     builder: (context) {
  //       bool dialogLoading = false;

  //       return StatefulBuilder(builder: (context, setDialogState) {
  //         return Center(
  //           child: BaseNewarcPopup(
  //             noButton: true,
  //             isShowCloseIcon: false,
  //             title: "Salda la Success Fee",
  //             column: Container(
  //               // height: 305,
  //               width: 489,
  //               child: Column(
  //                 mainAxisAlignment: MainAxisAlignment.center,
  //                 mainAxisSize: MainAxisSize.min,
  //                 children: [
  //                   SizedBox(height: 26),
  //                   Container(
  //                     padding: EdgeInsets.all(18),
  //                     decoration: BoxDecoration(
  //                       borderRadius: BorderRadius.circular(10),
  //                       color: AppColor.white,
  //                       border: Border.all(
  //                         width: 1,
  //                         color: Color(0xffDBDBDB),
  //                       ),
  //                     ),
  //                     child: Column(
  //                       children: [
  //                         NarFormLabelWidget(
  //                           label: "Success Fee: €$successFee + IVA",
  //                           fontSize: 17,
  //                           fontWeight: '700',
  //                           textColor: Theme.of(context).primaryColor,
  //                         ),
  //                         SizedBox(
  //                           height: 2,
  //                         ),
  //                         NarFormLabelWidget(
  //                           label:
  //                               "Commissione dello 0,2% applicata al prezzo di vendita",
  //                           fontSize: 12,
  //                           fontWeight: '600',
  //                           textColor: Color(0xff999999),
  //                         ),
  //                         SizedBox(
  //                           height: 16,
  //                         ),
  //                         CustomTextFormField(
  //                           textAlign: TextAlign.center,
  //                           isHaveBorder: false,
  //                           isCenterLabel: true,
  //                           flex: 0,
  //                           suffixIcon: null,
  //                           fillColor: Color(0xffF5F5F5),
  //                           readOnly: true,
  //                           label: "Intestare bonifico a",
  //                           controller: TextEditingController(
  //                             text: "Newarc Srl",
  //                           ),
  //                         ),
  //                         SizedBox(
  //                           height: 10,
  //                         ),
  //                         CustomTextFormField(
  //                           textAlign: TextAlign.center,
  //                           suffixIcon: null,
  //                           isHaveBorder: false,
  //                           isCenterLabel: true,
  //                           flex: 0,
  //                           fillColor: Color(0xffF5F5F5),
  //                           readOnly: true,
  //                           label: "IBAN",
  //                           controller: TextEditingController(
  //                             text: "***************************",
  //                           ),
  //                         ),
  //                         SizedBox(
  //                           height: 10,
  //                         ),
  //                         CustomTextFormField(
  //                           textAlign: TextAlign.center,
  //                           suffixIcon: null,
  //                           isCenterLabel: true,
  //                           isHaveBorder: false,
  //                           flex: 0,
  //                           fillColor: Color(0xffF5F5F5),
  //                           readOnly: true,
  //                           label: "Somma da bonificare",
  //                           controller: TextEditingController(
  //                             text:
  //                                 "${((double.tryParse(successFee.replaceAll(".", "")) ?? 0 * 1.22) == 0) ? "" : ((double.tryParse(successFee.replaceAll(".", "")) ?? 0) * 1.22).toStringAsFixed(2)}€",
  //                           ),
  //                         ),
  //                         SizedBox(
  //                           height: 10,
  //                         ),
  //                         CustomTextFormField(
  //                           textAlign: TextAlign.center,
  //                           isCenterLabel: true,
  //                           isHaveBorder: false,
  //                           flex: 0,
  //                           suffixIcon: null,
  //                           fillColor: Color(0xffF5F5F5),
  //                           readOnly: true,
  //                           label: "Causale",
  //                           controller: TextEditingController(
  //                             text: "Success Fee progetto $immaginaProjectID",
  //                           ),
  //                         ),
  //                         SizedBox(
  //                           height: 20,
  //                         ),
  //                         NarFormLabelWidget(
  //                           label:
  //                               'Istituto bancario: Intesa San Paolo\nPiazza Massua 5, 10141, Torino',
  //                           fontSize: 12,
  //                           fontWeight: '600',
  //                           textAlign: TextAlign.center,
  //                           textColor: Color(0xff818181),
  //                         ),
  //                       ],
  //                     ),
  //                   ),
  //                   SizedBox(height: 48),
  //                   dialogLoading
  //                       ? Center(
  //                           child: CircularProgressIndicator(
  //                             color: Theme.of(context).primaryColor,
  //                           ),
  //                         )
  //                       : BaseNewarcButton(
  //                           width: 142,
  //                           textColor: AppColor.white,
  //                           color: Theme.of(context).primaryColor,
  //                           buttonText: "Chiudi",
  //                           onPressed: () async {
  //                             AgencyUser agencyUser =
  //                                 widget.projectArguments!["agencyUser"];

  //                             setDialogState(() {
  //                               dialogLoading = true;
  //                             });

  //                             try {
  //                               final collectionRef = FirebaseFirestore.instance
  //                                   .collection(
  //                                       appConfig.COLLECT_IMMAGINA_PROJECTS);
  //                               archiveData!['receivedSuccessFeePaymentDate'] =
  //                                   null;
  //                               await collectionRef
  //                                   .doc(firebaseProjectID)
  //                                   .update(archiveData);

  //                               Map<String, dynamic> emailVariable = {
  //                                 'successfeeamount':
  //                                     "${((double.tryParse(successFee.replaceAll(".", "")) ?? 0 * 1.22) == 0) ? "" : ((double.tryParse(successFee.replaceAll(".", "")) ?? 0) * 1.22).toStringAsFixed(2)}€",
  //                               };
  //                               Map<String, dynamic> workEmailVariables = {
  //                                 'agencyname': agencyUser.agency!.name,
  //                                 'immaginaProjectId': immaginaProject.projectId
  //                               };

  //                         // project archive send notification mail
  //                         sendEmail(templateId: CommonUtils.agencyProjectArchiveEmailTemplateId, agency: agencyUser.agency!, subject: CommonUtils.agencyProjectArchiveEmailSubject);
  //                         sendEmail(templateId: CommonUtils.agencyArchiveProjectAndHasSuccessFeeEmailTemplateId, agency: agencyUser.agency!, subject: CommonUtils.agencyArchiveProjectAndHasSuccessFeeEmailSubject,variables: emailVariable);
  //                         final renderistUsers = await FirebaseFirestore.instance
  //                           .collection(appConfig.COLLECT_USERS)
  //                           .where('type', isEqualTo: 'newarc')
  //                           .where('role', isEqualTo: 'master-renderist')
  //                           .where('isActive', isEqualTo: true)
  //                           .get();
  //                         final masterUsers = await FirebaseFirestore.instance
  //                             .collection(appConfig.COLLECT_USERS)
  //                             .where('type', isEqualTo: 'newarc')
  //                             .where('role', isEqualTo: 'master')
  //                             .where('isActive', isEqualTo: true)
  //                             .get();
  //                         for (var userDoc in renderistUsers.docs + masterUsers.docs) {
  //                           final userData = userDoc.data();
  //                           if (userData['email'] != null) {
  //                             sendEmail(
  //                               templateId: CommonUtils.agencyProjectArchiveForWorkSideEmailTemplateId, 
  //                               agency: agencyUser.agency!, 
  //                               subject: CommonUtils.agencyProjectArchiveForWorkSideEmailSubject, 
  //                               variables: workEmailVariables, 
  //                               recipientEmail: userData['email'], 
  //                               recipientName: userData['firstName'] != null ? "${userData['firstName']} ${userData['lastName'] ?? ''}" : "Renderist",
  //                             );
  //                           }
  //                         }

  //                               Navigator.pop(context);
  //                               widget.projectArguments!.clear();
  //                               widget.projectArguments!.addAll({
  //                                 'isFromRequest': false,
  //                               });
  //                               widget.updateViewCallback!('progetti-attivi',
  //                                   projectArguments: widget.projectArguments);
  //                             } catch (e) {
  //                               log("Error Getting Immagina Project Document: $e");
  //                             } finally {
  //                               // Reset loading state
  //                               setDialogState(() {
  //                                 dialogLoading = false;
  //                               });
  //                             }
  //                           },
  //                         ),
  //                 ],
  //               ),
  //             ),
  //           ),
  //         );
  //       });
  //     },
  //   );
  // }
}

formatDateForParsing(String dateString) {
  List splittedDate = dateString.split('/');
  return splittedDate[2] + '-' + splittedDate[1] + '-' + splittedDate[0];
}

// red cross line custom painter
class CrossLinePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Color(0xffE82525) // Red cross color
      ..strokeWidth = 3.0 // Line thickness
      ..style = PaintingStyle.stroke;

    // Draw diagonal lines for the red cross
    canvas.drawLine(Offset(size.width, 0), Offset(0, size.height),
        paint); // Top-right to bottom-left
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
