
import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/services.dart';
import 'package:newarc_platform/classes/renovationQuotation.dart';
import 'package:newarc_platform/classes/user.dart';
import 'package:intl/intl.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'dart:html' as html;
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/utils/various.dart';
import 'package:http/http.dart' as http;


NumberFormat localCurrencyFormatMain = NumberFormat.currency(locale: 'it_IT', symbol: '', decimalDigits: 2);
NumberFormat localCurrencyFormatMainInt = NumberFormat.currency(locale: 'it_IT', symbol: '', decimalDigits: 0);

Future<Uint8List> _loadImage(String path) async {
  final ByteData data = await rootBundle.load(path);
  return data.buffer.asUint8List();
}

fetchRenovator( renovatorId) async {
  
  try{
    DocumentSnapshot<Map<String, dynamic>> collectionSnapshot = await FirebaseFirestore.instance
        .collection(appConfig.COLLECT_USERS )
        .doc(renovatorId)
        .get();

    NewarcUser user;
    if (collectionSnapshot.data() != null ) {

      user = NewarcUser.fromDocument( collectionSnapshot.data()!, collectionSnapshot.id);
      return user;
    }

    return null;
  }catch(e,s){
    log("fetchRenovator ERROR ${e.toString()}");
    log("fetchRenovator StackTrace ${s.toString()}");
  }
}
Future<void> pdfDesign(
    RenovationQuotation  quotation,
    Map<String, Map<String, List<Map<dynamic, dynamic>>>> renovationData,
    Map categoryTotal, Map subCategoryTotal,
    Map categoryTotalForOnlyToShow,
    Map subCategoryTotalForOnlyToShow
    ) async {



    Map<String, Map<String, List<Map<dynamic, dynamic>>>> renoDataCopy = new Map.from(renovationData);
    if( renoDataCopy.containsKey('N - Newarc') ) {
      renoDataCopy = {
        'N - Newarc': renoDataCopy['N - Newarc']!,
        ...renoDataCopy..remove('N - Newarc'),
      };
    }
    
  try {

    double total = 0;

    final ByteData fontMediumData = await rootBundle.load('assets/fonts/Raleway-Medium.ttf');
    final ByteData fontLightItalicData = await rootBundle.load('assets/fonts/Raleway-LightItalic.ttf');
    final ralewayMedium = pw.Font.ttf(fontMediumData.buffer.asByteData());

    final ByteData fontBoldData =
        await rootBundle.load('assets/fonts/Raleway-Bold.ttf');
    final ralewayBold = pw.Font.ttf(fontBoldData.buffer.asByteData());
    final ralewayLightItalicBold = pw.Font.ttf(fontLightItalicData.buffer.asByteData());

    final Uint8List coverbgImageData = await _loadImage('assets/renovation-quotation-pdf-bg-new.jpg');
    final coverbg = pw.MemoryImage(coverbgImageData);

    final Uint8List partnerImageData = await _loadImage('assets/partners.png');
    final partnerImage = pw.MemoryImage(partnerImageData);

    final Uint8List coverLogoImageData =
        await _loadImage('assets/rq-pdf-cover-logo.png');
    final coverLogo = pw.MemoryImage(coverLogoImageData);

    final Uint8List cover2Image1LogoImageData =
        await _loadImage('assets/cover-2-1.jpg');
    final cover2Image1 = pw.MemoryImage(cover2Image1LogoImageData);

    final Uint8List cover2Image2LogoImageData =
        await _loadImage('assets/cover-2-3.jpg');
    final cover2Image2 = pw.MemoryImage(cover2Image2LogoImageData);

    final Uint8List cover2Image3LogoImageData =
        await _loadImage('assets/cover-2-2.jpg');
    final cover2Image3 = pw.MemoryImage(cover2Image3LogoImageData);

    final Uint8List cover2Image4LogoImageData =
        await _loadImage('assets/cover-2-4.jpg');
    final cover2Image4 = pw.MemoryImage(cover2Image4LogoImageData);


    final Uint8List valid = await _loadImage('assets/icons/valid.png');
    final validImage = pw.MemoryImage(valid);

    final Uint8List workDuration = await _loadImage('assets/icons/work-duration.png');
    final workDurationImage = pw.MemoryImage(workDuration);
    
    final Uint8List paymentOptions = await _loadImage('assets/icons/payment-options.png');
    final workPaymentOptions = pw.MemoryImage(paymentOptions);

    List<String> payments = [
      '#% Accettazione preventivo',
      '#% Inizio lavori',
      '#% Primo avanzamento lavori',
      '#% Secondo avanzamento lavori',
      '#% Fine lavori',
    ];
    if (quotation.paymentMode == 'Da definire') {
      payments.clear();
      payments.add('Da definire');
    } else if (quotation.paymentMode != null) {
      List<String> tmp = quotation.paymentMode.toString().split('-');
      if (tmp.length > 0) {
        for (var i = 0; i < tmp.length; i++) {
          payments[i] = payments[i].replaceFirst('#', tmp[i].toString());
        }
      }
    } else {
      payments.clear();
    }

    // Create PDF
    final pdf = pw.Document();

    NewarcUser renovatorData;
    renovatorData = await fetchRenovator(quotation.renovationContact!.assignedRenovatorId!);

    Uint8List? profileImageData;

    try {
      if (renovatorData.profilePicture != null &&
          renovatorData.profilePicture!.isNotEmpty) {
        final ref = FirebaseStorage.instance
            .ref()
            .child('users/${renovatorData.id}/' + renovatorData.profilePicture!);
        final response = await http.get(Uri.parse(await ref.getDownloadURL()));

        // Check if response is valid and an image
        if (response.statusCode == 200 &&
            (response.headers['content-type']?.contains('image') ?? false)) {
          profileImageData = response.bodyBytes;
        }
      }
    } catch (e) {
      print("Error loading profile image: $e");
    }

    List<pw.Widget> pdfDataWidgets = [];
    pdfDataWidgets.add(pw.Text('Quotazione',
        style: pw.TextStyle(
            font: ralewayBold,
            fontSize: 18,
            color: PdfColor.fromHex('000'))));

    pdfDataWidgets.add(
      pw.SizedBox(height: 20),
    );

    renoDataCopy.keys.map((category) {
      // print({ 'renovationRow', renovationRow});
      total += categoryTotal[category];
      log("subCategoryTotalForOnlyToShow =====> $subCategoryTotalForOnlyToShow");

      pdfDataWidgets.add(pw.Container(
        // margin: pw.EdgeInsets.only(top: 15),
        padding: pw.EdgeInsets.only(left: 15, right: 15, bottom: 10, top: 10),
        decoration: pw.BoxDecoration(
          color: PdfColor.fromHex('F2FAF7'),
          borderRadius: pw.BorderRadius.circular(7),
          // border: pw.Border(
          //   bottom: pw.BorderSide(width: 1.0, color: PdfColor.fromHex('E4E4E4')),

          // )
        ),
        child: pw.Row(
          mainAxisSize: pw.MainAxisSize.max,
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          crossAxisAlignment: pw.CrossAxisAlignment.center,
          children: [
            pw.Text(category,
                style: pw.TextStyle(
                    font: ralewayBold,
                    fontSize: 13,
                    color: PdfColor.fromHex('489B79'))),
            /*pw.Text(
                localCurrencyFormatMain.format(categoryTotal[category]) +
                    ' €',
                style: pw.TextStyle(
                    font: ralewayBold,
                    fontSize: 13,
                    color: PdfColor.fromHex('489B79')))*/
          ],
        ),
      ));
      // pdfDataWidgets.add(headerRow());
      pdfDataWidgets.add(pw.SizedBox(
        height: 10,
      ));

      renoDataCopy[category]!.keys.map((subcategory) {
        pdfDataWidgets.add(pw.Container(
          padding: pw.EdgeInsets.only(left: 15, right: 15, bottom: 5, top: 3),
          child: pw.Row(
            mainAxisSize: pw.MainAxisSize.max,
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            crossAxisAlignment: pw.CrossAxisAlignment.center,
            children: [
              pw.Text(subcategory,
              style: pw.TextStyle(
                  font: ralewayBold,
                  fontSize: 11,
                  color: PdfColor.fromHex('489B79'))),

              pw.Row(
                children: [

                  pw.Text(
                    localCurrencyFormatMain.format(
                      double.tryParse(subCategoryTotalForOnlyToShow[category]?[subcategory]!["subCategoryTotal"].toString() ?? "0.0") ?? 0.0,
                    ) + ' €',
                    style: pw.TextStyle(
                      font: ralewayBold,
                      fontSize: 11,
                      decoration: (subCategoryTotalForOnlyToShow[category]?[subcategory]?["isDiscounted"] == true)
                          ? pw.TextDecoration.lineThrough
                          : pw.TextDecoration.none,
                      color: PdfColor.fromHex('489B79'),
                    ),
                  ),

                  pw.SizedBox(width: subCategoryTotalForOnlyToShow[category]?[subcategory]?["isDiscounted"] ?? false ? 10 : 0),

                  subCategoryTotalForOnlyToShow[category]?[subcategory]?["isDiscounted"] ?? false ?
                  pw.Text(
                      RegExp(r'^[0.]*$').hasMatch(subCategoryTotal[category][subcategory].toString()) ? "Incluso" :
                      (localCurrencyFormatMain.format(double.tryParse(subCategoryTotal[category][subcategory].toString())) + ' €'),
                      style: pw.TextStyle(
                          font: ralewayBold,
                          fontSize: 11,
                          color: PdfColor.fromHex('489B79'))) :  pw.SizedBox.shrink(),
                ]
              ),
            ]
          )
          
          
        ));

        pdfDataWidgets.add(headRow(ralewayMedium, ralewayBold));
        
        renoDataCopy[category]![subcategory]!.map((entry) {
          pdfDataWidgets
              .add(dataRow(category, entry, ralewayMedium, ralewayBold));
        }).toList();
      }).toList();

      pdfDataWidgets.add(pw.SizedBox(
        height: 10,
      ));
    }).toList();

    if(quotation.discount != null && quotation.discount != '' && quotation.discount != '0' && quotation.discount != '0%' ) pdfDataWidgets.add(
      pw.Container(
          padding: pw.EdgeInsets.symmetric(horizontal: 15, vertical: 10),
          decoration: pw.BoxDecoration(
            // color: PdfColor.fromHex('489B79'),
            borderRadius: pw.BorderRadius.circular(7),
            border: pw.Border.all(
              width: 1.0, color: PdfColor.fromHex('489B79')
            )
          ),
          margin: pw.EdgeInsets.only(top: 10),
          child: pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text('Totale',
                  style: pw.TextStyle(
                      font: ralewayBold,
                      fontSize: 13,
                      color: PdfColor.fromHex('489B79'),
                    )
                  ),
              pw.Text(  localCurrencyFormatMain.format(total) + '€',
                  style: pw.TextStyle(
                      font: ralewayBold,
                      fontSize: 13,
                      color: PdfColor.fromHex('489B79'),
                      decoration: pw.TextDecoration.lineThrough
                    )
                  )
            ],
          )),
    );

    if( quotation.discount != null && quotation.discount != '' && quotation.discount != '0' && quotation.discount != '0%'  )  pdfDataWidgets.add(
      pw.Container(
          padding: pw.EdgeInsets.symmetric(horizontal: 15, vertical: 10),
          decoration: pw.BoxDecoration(
            // color: PdfColor.fromHex('489B79'),
            borderRadius: pw.BorderRadius.circular(7),
            border: pw.Border.all(
              width: 1.0, color: PdfColor.fromHex('489B79')
            )
          ),
          margin: pw.EdgeInsets.only(top: 10),
          child: pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text('Sconto',
                  style: pw.TextStyle(
                      font: ralewayBold,
                      fontSize: 13,
                      color: PdfColor.fromHex('489B79'))),
              pw.Text( quotation.discountType == 'Percentuale' ? quotation.discount! : localCurrencyFormatMain.format(double.tryParse(quotation.discount!) ?? "")+'€',
                  style: pw.TextStyle(
                      font: ralewayBold,
                      fontSize: 13,
                      color: PdfColor.fromHex('489B79')))
            ],
          )),
    );

    double totalAfterdiscount = 0;
    if( quotation.discount != null && quotation.discount != '' && quotation.discount != '0' && quotation.discount != '0%'  ) {
      String? discountStr = quotation.discount;

      double discount = 0.0;
      if (discountStr != null && discountStr.isNotEmpty) {
        // Check if discount contains '%'
        if (discountStr.contains('%')) {
          discount = double.tryParse(discountStr.replaceAll('%', '').trim()) ?? 0.0;
        } else {
          discount = double.tryParse(discountStr.trim()) ?? 0.0;
        }
      }
      if( discount > 0 ) {
        if( quotation.discountType == 'Percentuale' ) {
          totalAfterdiscount = total * (( 100 - discount ) / 100 );
        } else {
          totalAfterdiscount = total - discount;
        }
      } else {
        totalAfterdiscount = total;
      }
    } else {
      totalAfterdiscount = total;
    }

    pdfDataWidgets.add(
      pw.Container(
          padding: pw.EdgeInsets.symmetric(horizontal: 15, vertical: 10),
          decoration: pw.BoxDecoration(
            color: PdfColor.fromHex('489B79'),
            borderRadius: pw.BorderRadius.circular(7),
            
          ),
          margin: pw.EdgeInsets.only(top: 10),
          child: pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text(quotation.discount != null && quotation.discount != '' && quotation.discount != '0' && quotation.discount != '0%'  ? 'Totale scontato' : "Totale",
                  style: pw.TextStyle(
                      font: ralewayBold,
                      fontSize: 13,
                      color: PdfColor.fromHex('fff'))),
              pw.Text(  localCurrencyFormatMain.format(totalAfterdiscount) + '€ + iva',
                  style: pw.TextStyle(
                      font: ralewayBold,
                      fontSize: 13,
                      color: PdfColor.fromHex('fff')))
            ],
          )),
    );

    pdfDataWidgets.add(
      pw.SizedBox(height: 25),
    );

    const chartColors = [
      PdfColors.blue300,
      PdfColors.green300,
      PdfColors.amber300,
      PdfColors.pink300,
      PdfColors.cyan300,
      PdfColors.purple300,
      PdfColors.lime300,
    ];

    int colorIndex = -1;
    int _colorIndex = -1;

    List<pw.Widget> pdfDataWidgetGraph = [];

    pdfDataWidgetGraph.add(pw.Text('Riassunto',
        style: pw.TextStyle(
            font: ralewayBold,
            fontSize: 18,
            color: PdfColor.fromHex('000'))));

    pdfDataWidgetGraph.add(
      pw.SizedBox(height: 20),
    );

    pdfDataWidgetGraph.add(
      pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.start,
        children: [
          pw.Expanded(
            child: pw.Text('La tua ristrutturazione',
                style: pw.TextStyle(
                  font: ralewayBold,
                  fontSize: 12,
                  color: PdfColor.fromHex('000')
                )
            )
          ),
        ]
      ),
    );

    
    
    pdfDataWidgetGraph.add(
      pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.start,
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Expanded(
            child: pw.Column(
              children: [
                pw.SizedBox(height: 10),
                ...categoryTotalForOnlyToShow.entries.map((entry) {
                  return pw.Container(
                    decoration: pw.BoxDecoration(
                      borderRadius: pw.BorderRadius.circular(5),
                      color: PdfColor.fromHex('489B79')
                    ),
                    height: 25,
                    padding: pw.EdgeInsetsDirectional.symmetric(horizontal: 10),
                    margin:  pw.EdgeInsetsDirectional.only(bottom: 10),
                    child: pw.Center(
                      child: pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: pw.CrossAxisAlignment.center,
                        children: [
                          pw.Text(entry.key ?? "",
                            style: pw.TextStyle(
                              font: ralewayBold,
                              fontSize: 11,
                              color: PdfColor.fromHex('fff')
                            )
                          ),
                          pw.Row(
                            children: [
                              pw.SizedBox(
                                width: 70,
                                child: pw.Text(
                                    // check if "Incluso" must be outputted
                                    RegExp(r'^[0.]*$').hasMatch(entry.value["catTotal"].toString()) ? "" :
                                    // check if "catTotal" is equal to "catDiscountedAmount" (means no final percentage discount was applied)
                                    entry.value["catTotal"] == entry.value["catDiscountedAmount"] ? "" :
                                    // output "catTotal"
                                    localCurrencyFormatMain.format(double.tryParse(entry.value["catTotal"].toString()) ?? 0.0) + '€',
                                    textAlign: pw.TextAlign.left,
                                    style: pw.TextStyle(
                                        font: ralewayMedium,
                                        fontSize: 11,
                                        decoration: pw.TextDecoration.lineThrough,
                                        color: PdfColor.fromHex('fff')
                                    )
                                ),
                              ),

                              // pw.SizedBox(width: entry.value["isDiscounted"] ?? false ? 10 : 0),
                              pw.SizedBox(width: 10),

                              pw.SizedBox(
                                  width: 100,
                                child: pw.Text(
                                    RegExp(r'^[0.]*$').hasMatch(entry.value["catTotal"].toString()) ? "Incluso" :
                                    localCurrencyFormatMain.format(double.tryParse(entry.value["catDiscountedAmount"].toString()) ?? 0.0) + '€',
                                    textAlign: pw.TextAlign.left,
                                    style: pw.TextStyle(
                                        font: ralewayMedium,
                                        fontSize: 11,
                                        color: PdfColor.fromHex('fff')
                                    )
                                )
                              )

                            ]
                          )
                        ],
                      )
                    )
                  );
                }),

                if( quotation.discount != null && quotation.discount != '' && quotation.discount != '0' && quotation.discount != '0%'  ) pw.Container(
                  decoration: pw.BoxDecoration(
                    borderRadius: pw.BorderRadius.circular(5),
                    // color: PdfColor.fromHex('489B79'),
                    border: pw.Border.all( width: 1, color: PdfColor.fromHex('489B79'))
                  ),
                  height: 25,
                  padding: pw.EdgeInsetsDirectional.symmetric(horizontal: 10),
                  margin:  pw.EdgeInsetsDirectional.only(bottom: 10, top: 10),
                  child: pw.Center(
                    child: pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: pw.CrossAxisAlignment.center,
                      children: [
                        pw.Text('Totale',
                          style: pw.TextStyle(
                            font: ralewayBold,
                            fontSize: 11,
                            color: PdfColor.fromHex('489B79')
                          )
                        ),

                        pw.Row(
                          children: [
                            pw.SizedBox(
                              width: 70,
                              child: pw.Text(localCurrencyFormatMain.format(total) + '€',
                                  textAlign: pw.TextAlign.left,
                                  style: pw.TextStyle(
                                      font: ralewayMedium,
                                      decoration:  pw.TextDecoration.lineThrough,
                                      fontSize: 11,
                                      color: PdfColor.fromHex('489B79')
                                  )
                              )
                            ),
                            pw.SizedBox(width:10),
                            pw.SizedBox(
                              width: 100,
                              child: pw.Text(localCurrencyFormatMain.format(totalAfterdiscount) + '€' + " + iva",
                                  textAlign: pw.TextAlign.left,
                                  style: pw.TextStyle(
                                      font: ralewayMedium,
                                      fontSize: 11,
                                      color: PdfColor.fromHex('489B79')
                                  )
                              )
                            ),
                          ]
                        )

                      ],
                    )
                  )
                ),

                // pw.Container(
                //   decoration: pw.BoxDecoration(
                //     borderRadius: pw.BorderRadius.circular(5),
                //     color: PdfColor.fromHex('489B79')
                //   ),
                //   height: 25,
                //   padding: pw.EdgeInsetsDirectional.symmetric(horizontal: 10),
                //   child: pw.Center(
                //     child: pw.Row(
                //       mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                //       crossAxisAlignment: pw.CrossAxisAlignment.center,
                //       children: [
                //         pw.Text(quotation.discount != null && quotation.discount != '' && quotation.discount != '0' && quotation.discount != '0%'  ? 'Totale scontato' : "Totale",
                //           style: pw.TextStyle(
                //             font: ralewayBold,
                //             fontSize: 11,
                //             color: PdfColor.fromHex('fff')
                //           )
                //         ),
                //
                //         pw.Text(localCurrencyFormatMain.format(totalAfterdiscount) + '€' + " + iva",
                //           style: pw.TextStyle(
                //             font: ralewayMedium,
                //             fontSize: 11,
                //             color: PdfColor.fromHex('fff')
                //           )
                //         ),
                //
                //       ],
                //     )
                //   )
                // ),
              ],
            )
          ),
          
        ]
      ),
    );

    pdfDataWidgetGraph.add(
      pw.SizedBox(height: 10)
    );

    String laborCategoryKey = subCategoryTotal.keys.firstWhere((entry) => entry.contains("Lavori interni"), orElse: () => "" );

    if(laborCategoryKey != '') pdfDataWidgetGraph.add(
      pw.Container(
        decoration: pw.BoxDecoration(
          borderRadius: pw.BorderRadius.circular(12),
          border: pw.Border.all(width: 1, color: PdfColor.fromHex('D3D3D3'))
        ),
        padding: pw.EdgeInsets.all(15),
        margin:  pw.EdgeInsets.only(top: 20),
        child: pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Text('Focus lavori interni',
                style: pw.TextStyle(
                  font: ralewayBold,
                  fontSize: 12,
                  color: PdfColor.fromHex('000')
                )
            ),
            pw.SizedBox(height:10),
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.start,
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Expanded(
                  child: pw.Column(
                    children: [
                      ...subCategoryTotalForOnlyToShow.entries.map<pw.Widget>((cat) {
                        if( cat.key.contains('Lavori interni') ) {
                          
                          return pw.Column(
                            children: cat.value.entries.map<pw.Widget>((subCat) {
                              double subCateValue = subCat.value["subCategoryTotal"];
                              if((quotation.discount?.isNotEmpty ?? false) && quotation.discount!.contains("%")){
                                double wholeQuotationDiscountDouble = double.tryParse(quotation.discount!.replaceAll("%", "")) ?? 0.0;
                                double subCateValueWithDiscount = subCateValue * wholeQuotationDiscountDouble / 100;
                                subCateValue = subCateValue - subCateValueWithDiscount;
                              }
                              return pw.Container(
                                decoration: pw.BoxDecoration(
                                  borderRadius: pw.BorderRadius.circular(5),
                                  color: PdfColor.fromHex('F2FAF7'),
                                ),
                                height: 20,
                                padding: pw.EdgeInsetsDirectional.symmetric(horizontal: 10),
                                margin: pw.EdgeInsetsDirectional.only(bottom: 4),
                                child: pw.Center(
                                  child: pw.Row(
                                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                    crossAxisAlignment: pw.CrossAxisAlignment.center,
                                    children: [
                                      pw.Text(
                                        subCat.key,
                                        style: pw.TextStyle(
                                          font: ralewayBold,
                                          fontSize: 8,
                                          color: PdfColor.fromHex('489B79'),
                                        ),
                                      ),
                                      pw.Text(
                                        '${localCurrencyFormatMain.format(double.parse(subCateValue.toStringAsFixed(2)))}€',
                                        style: pw.TextStyle(
                                          font: ralewayMedium,
                                          fontSize: 8,
                                          color: PdfColor.fromHex('489B79'),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            }).toList(),
                          );
                        }
                        return pw.Container();

                      }).toList(),

                      
                    ],
                  )
                ),
                pw.SizedBox(width:20),
                pw.Expanded(
                  child: pw.Container(
                    width: 200,
                    height: 200,
                    child: pw.Column(
                      children: [
                        pw.Flexible(
                          child: pw.Chart(
                            title: pw.Text(
                              '',
                              style: const pw.TextStyle(
                                color: PdfColors.cyan,
                                fontSize: 1,
                              ),
                            ),
                            grid: pw.PieGrid(),
                            datasets: subCategoryTotal[laborCategoryKey].entries.map<pw.PieDataSet>((entry) {
                              final data = double.tryParse(((entry.value / categoryTotal[laborCategoryKey]) * 100).toString());
                              final value = (entry.value / categoryTotal[laborCategoryKey]) * 100;
                              colorIndex++;
                              if (colorIndex == chartColors.length) colorIndex = 0;

                              return pw.PieDataSet(
                                legend: data!.toStringAsFixed(1),
                                value: value,
                                color: chartColors[colorIndex],
                                legendStyle: const pw.TextStyle(fontSize: 8),
                              );
                            }).toList()
                            
                          ),
                        ),
                        pw.SizedBox(height: 5),
                        pw.Wrap(
                          crossAxisAlignment: pw.WrapCrossAlignment.start,
                          spacing: 1,
                          runSpacing: 2,
                          children: subCategoryTotal[laborCategoryKey].entries.map<pw.Widget>((entry){
                              final data = double.tryParse(((entry.value / total) * 100).toString());
                              final value = (entry.value / total) * 100;
                              _colorIndex++;
                              if( _colorIndex == chartColors.length ) _colorIndex = 0;
                              return pw.Row(
                                crossAxisAlignment: pw.CrossAxisAlignment.center,
                                mainAxisAlignment: pw.MainAxisAlignment.start,
                                mainAxisSize: pw.MainAxisSize.min,
                                children: [
                                  pw.Container(
                                    decoration: pw.BoxDecoration(
                                      color: chartColors[_colorIndex],
                                      borderRadius: pw.BorderRadius.circular(9),
                                      shape: pw.BoxShape.circle
                                    ),
                                    height: 7,
                                    width: 7,
                                    // margin: pw.EdgeInsets.only(right:5)
                                  ),
                                  pw.SizedBox(width: 2),
                                  pw.Text('${entry.key}',
                                    style: pw.TextStyle(
                                      font: ralewayMedium,
                                      fontSize: 6,
                                      color: PdfColor.fromHex('000')
                                    )
                                  ),
                                  pw.SizedBox(width: 5),
                                ]
                                
                              );
                            }).toList()
                        )
                      ],
                    ),
                  )
                ),
              ]
            ),
          ]
        )
        
      )
    );

    
    List<pw.Widget> pdfDataWidgets2 = [];
    
    pdfDataWidgets2.add(
      pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.start,
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Expanded(
            child: pw.Column(
              children: [
                pw.SizedBox(height: 10),
                pw.Container(
                  decoration: pw.BoxDecoration(
                    borderRadius: pw.BorderRadius.circular(10),
                    border: pw.Border.all( color: PdfColor.fromHex('D3D3D3')),
                  ),
                  height: 100,
                  padding: pw.EdgeInsets.only(
                    left: 15,
                    right: 10,
                    top: 7
                  ),
                  margin:  pw.EdgeInsetsDirectional.only(bottom: 8),
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: pw.CrossAxisAlignment.center,
                        children: [
                          pw.Text('Validità offerta',
                            style: pw.TextStyle(
                              fontSize: 11,
                              color: PdfColor.fromHex('000')
                            )
                          ),
                          pw.Image(
                            validImage,
                            height: 26,
                            width: 23,
                            fit: pw.BoxFit.cover
                            
                          ),
                        ],
                      ),
                      pw.SizedBox(height: 15),
                      pw.Text('3o giorni',
                        style: pw.TextStyle(
                          font: ralewayBold,
                          fontSize: 20,
                          color: PdfColor.fromHex('000')
                        )
                      ),
                    ]
                    
                  )
                ),
                  pw.Container(
                          decoration: pw.BoxDecoration(
                            borderRadius: pw.BorderRadius.circular(10),
                            border: pw.Border.all( color: PdfColor.fromHex('D3D3D3')),
                          ),
                          padding: pw.EdgeInsets.only(
                              left: 15,
                              right: 10,
                              top: 7,
                              bottom: 15
                          ),
                          margin:  pw.EdgeInsetsDirectional.only(top: 15),
                          child: pw.Column(
                              crossAxisAlignment: pw.CrossAxisAlignment.start,
                              children: [
                                pw.Row(
                                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: pw.CrossAxisAlignment.center,
                                  children: [
                                    pw.Text('Pagamento Lavori',
                                        style: pw.TextStyle(
                                            fontSize: 11,
                                            color: PdfColor.fromHex('000')
                                        )
                                    ),
                                    pw.Image(
                                        workPaymentOptions,
                                        height: 26,
                                        width: 26,
                                        fit: pw.BoxFit.cover

                                    ),
                                  ],
                                ),
                                pw.SizedBox(height: 15),
                                pw.Column(
                                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                                    children: payments.map((e) {
                                      return pw.Column(children: [
                                        pw.Text(e,
                                            style: pw.TextStyle(
                                                font: ralewayBold,
                                                fontSize: 11,
                                                color: PdfColor.fromHex('000')
                                            )
                                        ),
                                        pw.SizedBox(height: 5),

                                      ]);
                                    }).toList()
                                ),

                                pw.SizedBox(height: 5),
                                // pw.Text("Le modalità di pagamento sopracitate sono da intendersi per la sezione relativa ai lavori interni. Le altre sezioni saranno soggette a differenti modalità di pagamento.",
                                //     style: pw.TextStyle(
                                //         font: ralewayLightItalicBold,
                                //         fontSize: 8,
                                //         color: PdfColor.fromHex('000')
                                //     )
                                // ),

                              ]

                          )
                      ),


                  ]
                ),
          ),
          pw.SizedBox(width: 30),
          pw.Expanded(
            child: pw.Column(
              children: [
                pw.Container(
                    decoration: pw.BoxDecoration(
                      borderRadius: pw.BorderRadius.circular(10),
                      border: pw.Border.all( color: PdfColor.fromHex('D3D3D3')),
                    ),
                    height: 100,
                    padding: pw.EdgeInsets.only(
                        left: 15,
                        right: 10,
                        top: 7
                    ),
                    margin:  pw.EdgeInsetsDirectional.only(bottom: 8, top: 10),
                    child: pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          pw.Row(
                            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: pw.CrossAxisAlignment.center,
                            children: [
                              pw.Text('Durata presunta cantiere',
                                  style: pw.TextStyle(
                                      fontSize: 11,
                                      color: PdfColor.fromHex('000')
                                  )
                              ),
                              pw.Image(
                                  workDurationImage,
                                  height: 26,
                                  width: 26,
                                  fit: pw.BoxFit.cover

                              ),
                            ],
                          ),
                          pw.SizedBox(height: 15),
                          pw.Text(
                            // quotation.constructionDuration! == 'Da definire'
                            // ? 'Da definire'
                            // : quotation.constructionDuration!,
                              quotation.constructionDuration!,
                              style: pw.TextStyle(
                                  font: ralewayBold,
                                  fontSize: 20,
                                  color: PdfColor.fromHex('000')
                              )
                          ),
                          pw.SizedBox(height:5),
                          pw.Text(
                              'Da inizio lavori',
                              style: pw.TextStyle(
                                  font: ralewayBold,
                                  fontSize: 8,
                                  color: PdfColor.fromHex('757575'),
                                  letterSpacing: 0.3
                              )
                          ),
                        ]
                    )
                ),
                pw.Container(
                    decoration: pw.BoxDecoration(
                      borderRadius: pw.BorderRadius.circular(10),
                      border: pw.Border.all( color: PdfColor.fromHex('D3D3D3')),
                    ),
                    padding: pw.EdgeInsets.only(
                        left: 15,
                        right: 10,
                        top: 7,
                        bottom: 15
                    ),
                    margin:  pw.EdgeInsetsDirectional.only(top: 15),
                    child: pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          pw.Row(
                            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: pw.CrossAxisAlignment.center,
                            children: [
                              pw.Text('Pagamento Forniture e Pratiche',
                                  style: pw.TextStyle(
                                      fontSize: 11,
                                      color: PdfColor.fromHex('000')
                                  )
                              ),
                              pw.Image(
                                  workPaymentOptions,
                                  height: 26,
                                  width: 26,
                                  fit: pw.BoxFit.cover

                              ),
                            ],
                          ),
                          pw.SizedBox(height: 15),

                          pw.Text("In base alle disponibilità dei fornitori dei materiali selezionati e secondo le modalità esplicate in fase contrattuale.",
                              style: pw.TextStyle(
                                  font: ralewayBold,
                                  fontSize: 11,
                                  color: PdfColor.fromHex('000')
                              )
                          ),

                        ]

                    )
                ),
              ]
            )
          ),

        ]
      ),
    );
    
    pw.TextStyle tsHeading = pw.TextStyle(
              font: ralewayBold,
              fontSize: 6,
              color: PdfColor.fromHex('000'));

    pw.TextStyle tsText = pw.TextStyle(
              font: ralewayMedium,
              fontSize: 6,
              color: PdfColor.fromHex('000'));
    
    pdfDataWidgets2.add(
      pw.SizedBox(height: 40),
    );

    pdfDataWidgets2.add(
      pw.Text('Variazioni ed Extra-Variazioni', style: tsHeading),
    );
    pdfDataWidgets2.add(
      pw.Text('Eventuali variazioni o extra-variazioni rispetto all\'elenco di lavorazioni indicate nel presente preventivo dovranno essere preventivamente concordate con la committenza e formalizzate mediante apposito atto scritto.', style: tsText),
    );
    pdfDataWidgets2.add(
      pw.SizedBox(height: 15),
    );

    pdfDataWidgets2.add(
      pw.Text('Pratiche e Forniture', style: tsHeading),
    );
    pdfDataWidgets2.add(
      pw.Text('Le pratiche e le forniture saranno soggette a preventivi separati con modalità di pagamento differenziate rispetto alla sezione dedicata ai lavori interni', style: tsText),
    );
    pdfDataWidgets2.add(
      pw.SizedBox(height: 15),
    );

    pdfDataWidgets2.add(
      pw.Text('Esclusioni dal Capitolo di Spesa', style: tsHeading),
    );
    pdfDataWidgets2.add(
      pw.Text('Si precisa che sono esclusi dal presente capitolo di spesa tutti gli interventi, forniture e materiali non espressamente specificati nella presente offerta.', style: tsText),
    );
    pdfDataWidgets2.add(
      pw.SizedBox(height: 15),
    );
    
    pdfDataWidgets2.add(
      pw.Text('Autorizzazione all\'Esecuzione dei Lavori', style: tsHeading),
    );
    pdfDataWidgets2.add(
      pw.Text('L\'approvazione del preventivo comporta l\'autorizzazione integrale all\'impresa proponente per l\'esecuzione dei lavori, secondo quanto descritto nell\'offerta.', style: tsText),
    );
    pdfDataWidgets2.add(
      pw.SizedBox(height: 15),
    );

    pdfDataWidgets2.add(
      pw.Text('Impatto sui Tempi di Consegna', style: tsHeading),
    );
    pdfDataWidgets2.add(
      pw.Text("Si precisa che le presenti lavorazioni, non incluse nel capitolato originario, potrebbero influire sui tempi di consegna dell'immobile rispetto a quanto eventualmente indicato in precedenti accordi o contratti, come ad esempio nel compromesso di vendita.", style: tsText),
    );
    pdfDataWidgets2.add(
      pw.SizedBox(height: 15),
    );

    pdfDataWidgets2.add(
      pw.Text("Modifiche ai Disegni Costruttivi", style: tsHeading),
    );
    pdfDataWidgets2.add(
      pw.Text("Nel caso in cui dovessero emergere ulteriori variazioni o modifiche ai disegni costruttivi già definiti e consolidati, saranno addebitate a parte alla parte acquirente tutte le spese collegate. Tali spese potranno variare in base:\n"        
      "• Alla natura delle modifiche richieste;\n"
      "• Allo stato di avanzamento dei lavori;\n"
      "• Alla necessità di ottenere nuove autorizzazioni o varianti comunali.", style: tsText),
    );
    pdfDataWidgets2.add(
      pw.SizedBox(height: 15),
    );

    pdfDataWidgets2.add(
      pw.Text("Onere per Servizi e Allacciamenti", style: tsHeading),
    );
    pdfDataWidgets2.add(
      pw.Text("Rimangono a carico del cliente tutti i costi relativi a servizi accessori, quali:\n"        
      "• Fornitura e posa di punti luce o apparecchiature elettriche non specificate;\n"
      "• Installazione e allacciamenti per gas, acqua, elettricità, telefonia e altre utenze;\n"
      "• Eventuali oneri per pratiche comunali e catastali non incluse nel presente preventivo.", style: tsText),
    );
    pdfDataWidgets2.add(
      pw.SizedBox(height: 15),
    );

    pdfDataWidgets2.add(
      pw.Text("Autocertificazione IVA Agevolata 10%", style: tsHeading),
    );
    pdfDataWidgets2.add(
      pw.Text("Qualora si desideri applicare l'aliquota IVA agevolata al 10%, è obbligatorio fornire una autocertificazione attestante il diritto all'agevolazione. In assenza della suddetta autocertificazione, si applicherà l'aliquota IVA ordinaria.", style: tsText),
    );
    pdfDataWidgets2.add(
      pw.SizedBox(height: 15),
    );

    // pdfDataWidgets2.add(
    //   pw.Text('DURATA PRESUNTA CANTIERE:',
    //       style: pw.TextStyle(
    //           font: ralewayBold,
    //           fontSize: 9,
    //           color: PdfColor.fromHex('000'))),
    // );
    // pdfDataWidgets2.add(
    //   pw.Text(
    //       quotation.constructionDuration! == 'Da definire'
    //           ? 'Da definire'
    //           : quotation.constructionDuration! +
    //               ' dalla data di inizio lavori.',
    //       style: pw.TextStyle(
    //           font: ralewayMedium,
    //           fontSize: 9,
    //           color: PdfColor.fromHex('000'))),
    // );
    // pdfDataWidgets2.add(
    //   pw.SizedBox(height: 25),
    // );
    // pdfDataWidgets2.add(
    //   pw.Text('ESCLUSIONI:',
    //       style: pw.TextStyle(
    //           font: ralewayBold,
    //           fontSize: 9,
    //           color: PdfColor.fromHex('000'))),
    // );
    // pdfDataWidgets2.add(
    //   pw.Text(
    //       '• Ogni tipo di lavorazione effettuata sotto richiesta dalla proprietà e non compresa in questo computo verrà conteggiata a fine lavori.',
    //       style: pw.TextStyle(
    //           font: ralewayMedium,
    //           fontSize: 9,
    //           color: PdfColor.fromHex('000'))),
    // );
    // pdfDataWidgets2.add(
    //   pw.SizedBox(height: 5),
    // );

    // pdfDataWidgets2.add(
    //   pw.Text('• Fornitura e consumi di acqua ed energia elettrica.',
    //       style: pw.TextStyle(
    //           font: ralewayMedium,
    //           fontSize: 9,
    //           color: PdfColor.fromHex('000'))),
    // );

    // pdfDataWidgets2.add(
    //   pw.SizedBox(height: 25),
    // );
    // pdfDataWidgets2.add(
    //   pw.Text('VALIDITÀ OFFERTA:',
    //       style: pw.TextStyle(
    //           font: ralewayBold,
    //           fontSize: 9,
    //           color: PdfColor.fromHex('000'))),
    // );
    // pdfDataWidgets2.add(
    //   pw.Text('30 giorni.',
    //       style: pw.TextStyle(
    //           font: ralewayMedium,
    //           fontSize: 9,
    //           color: PdfColor.fromHex('000'))),
    // );

    // pdfDataWidgets2.add(
    //   pw.Text("Modifiche ai Disegni Costruttivi", style: tsHeading),
    // );
    // pdfDataWidgets2.add(
    //   pw.Text("Nel caso in cui dovessero emergere ulteriori variazioni o modifiche ai disegni costruttivi già definiti e consolidati, saranno addebitate a parte alla parte acquirente tutte le spese collegate. Tali spese potranno variare in base:\n"
    //   "• Alla natura delle modifiche richieste;\n"
    //   "• Allo stato di avanzamento dei lavori;\n"
    //   "• Alla necessità di ottenere nuove autorizzazioni o varianti comunali.", style: tsText),
    // );
    // pdfDataWidgets2.add(
    //   pw.SizedBox(height: 15),
    // );

    // pdfDataWidgets2.add(
    //   pw.Text("Onere per Servizi e Allacciamenti", style: tsHeading),
    // );
    // pdfDataWidgets2.add(
    //   pw.Text("Rimangono a carico del cliente tutti i costi relativi a servizi accessori, quali:\n"
    //   "• Fornitura e posa di punti luce o apparecchiature elettriche non specificate;\n"
    //   "• Installazione e allacciamenti per gas, acqua, elettricità, telefonia e altre utenze;\n"
    //   "• Eventuali oneri per pratiche comunali e catastali non incluse nel presente preventivo.", style: tsText),
    // );
    // pdfDataWidgets2.add(
    //   pw.SizedBox(height: 15),
    // );

    // pdfDataWidgets2.add(
    //   pw.Text("Autocertificazione IVA Agevolata 10%", style: tsHeading),
    // );
    // pdfDataWidgets2.add(
    //   pw.Text("Qualora si desideri applicare l'aliquota IVA agevolata al 10%, è obbligatorio fornire una autocertificazione attestante il diritto all'agevolazione. In assenza della suddetta autocertificazione, si applicherà l'aliquota IVA ordinaria.", style: tsText),
    // );
    
    // pdfDataWidgets2.add(
    //   pw.Text('DURATA PRESUNTA CANTIERE:',
    //       style: pw.TextStyle(
    //           font: ralewayBold,
    //           fontSize: 9,
    //           color: PdfColor.fromHex('000'))),
    // );
    // pdfDataWidgets2.add(
    //   pw.Text(
    //       quotation.constructionDuration! == 'Da definire'
    //           ? 'Da definire'
    //           : quotation.constructionDuration! +
    //               ' dalla data di inizio lavori.',
    //       style: pw.TextStyle(
    //           font: ralewayMedium,
    //           fontSize: 9,
    //           color: PdfColor.fromHex('000'))),
    // );
    // pdfDataWidgets2.add(
    //   pw.SizedBox(height: 25),
    // );
    // pdfDataWidgets2.add(
    //   pw.Text('ESCLUSIONI:',
    //       style: pw.TextStyle(
    //           font: ralewayBold,
    //           fontSize: 9,
    //           color: PdfColor.fromHex('000'))),
    // );
    // pdfDataWidgets2.add(
    //   pw.Text(
    //       '• Ogni tipo di lavorazione effettuata sotto richiesta dalla proprietà e non compresa in questo computo verrà conteggiata a fine lavori.',
    //       style: pw.TextStyle(
    //           font: ralewayMedium,
    //           fontSize: 9,
    //           color: PdfColor.fromHex('000'))),
    // );
    // pdfDataWidgets2.add(
    //   pw.SizedBox(height: 5),
    // );

    // pdfDataWidgets2.add(
    //   pw.Text('• Fornitura e consumi di acqua ed energia elettrica.',
    //       style: pw.TextStyle(
    //           font: ralewayMedium,
    //           fontSize: 9,
    //           color: PdfColor.fromHex('000'))),
    // );

    // pdfDataWidgets2.add(
    //   pw.SizedBox(height: 25),
    // );
    // pdfDataWidgets2.add(
    //   pw.Text('VALIDITÀ OFFERTA:',
    //       style: pw.TextStyle(
    //           font: ralewayBold,
    //           fontSize: 9,
    //           color: PdfColor.fromHex('000'))),
    // );
    // pdfDataWidgets2.add(
    //   pw.Text('30 giorni.',
    //       style: pw.TextStyle(
    //           font: ralewayMedium,
    //           fontSize: 9,
    //           color: PdfColor.fromHex('000'))),
    // );

    /**
      * Cover page
      */
    pdf.addPage(
      pw.Page(
        theme:
            pw.ThemeData(defaultTextStyle: pw.TextStyle(font: ralewayMedium)),
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(0),
        build: (pw.Context context) => pw.Center(
            child: pw.Stack(children: [
          pw.Positioned(
              child: pw.Image(
                coverbg,
                fit: pw.BoxFit.cover,
              ),
              left: 0,
              top: 0,
              right: 0,
              bottom: 0),



          pw.Container(
              width: double.infinity,
              height: double.infinity,
              padding: pw.EdgeInsets.only(
                  top: 50, left: 50, right: 35, bottom: 30),
              child: pw.Column(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Container(
                      child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          children: [
                            pw.Row(
                                crossAxisAlignment: pw.CrossAxisAlignment.start,
                                mainAxisAlignment: pw.MainAxisAlignment.start,
                                children: [
                                  pw.Expanded(child: pw.Image(coverLogo, height: 56)),
                                  pw.SizedBox(width: 65),
                                  pw.Expanded(
                                      child: pw.Column(
                                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                                          children: [
                                            pw.Text('SPETT.LE ',
                                                style: pw.TextStyle(
                                                    fontSize: 8,
                                                    letterSpacing: 1,
                                                    color: PdfColor.fromHex('000'))),
                                            pw.SizedBox(height: 5),
                                            pw.Text(
                                                quotation.renovationContact!.name! +
                                                    ' ' +
                                                    quotation.renovationContact!.surname!,
                                                style: pw.TextStyle(
                                                    font: ralewayBold,
                                                    fontSize: 12,
                                                    color: PdfColor.fromHex('000'))),
                                            pw.SizedBox(height: 10),
                                            pw.Row(children: [
                                              pw.Expanded(
                                                  child: pw.Column(
                                                      mainAxisAlignment:
                                                      pw.MainAxisAlignment.start,
                                                      crossAxisAlignment:
                                                      pw.CrossAxisAlignment.start,
                                                      children: [
                                                        pw.Text('DATA',
                                                            style: pw.TextStyle(
                                                                fontSize: 8,
                                                                letterSpacing: 1,
                                                                color:
                                                                PdfColor.fromHex('000'))),
                                                        pw.SizedBox(height: 5),
                                                        //--------> in-attesa or da-modificare =========> quotation creation date
                                                        //--------> accettato or rifiutato  =========> modification date if modification date is null then show pdf created date
                                                        pw.Text(
                                                            (quotation.status == "in-attesa" || quotation.status == "da-modificare" ) ?
                                                            "${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year}" :
                                                            quotation.status == "accettato" || quotation.status == "rifiutato" ? quotation.modificationDate != null ? timestampToUtcDate(quotation.modificationDate!) : "${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year}" :
                                                            "${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year}",
                                                            style: pw.TextStyle(
                                                                font: ralewayBold,
                                                                fontSize: 10,
                                                                color:
                                                                PdfColor.fromHex('000'))),
                                                      ])),
                                              pw.SizedBox(width: 5),
                                              pw.Expanded(
                                                  child: pw.Column(
                                                      mainAxisAlignment:
                                                      pw.MainAxisAlignment.start,
                                                      crossAxisAlignment:
                                                      pw.CrossAxisAlignment.start,
                                                      children: [
                                                        pw.Text('CODICE',
                                                            style: pw.TextStyle(
                                                                fontSize: 8,
                                                                letterSpacing: 1,
                                                                color:
                                                                PdfColor.fromHex('000'))),
                                                        pw.SizedBox(height: 5),
                                                        pw.Text("${quotation.code!}_V${quotation.version!}_R${quotation.revision!}",
                                                            style: pw.TextStyle(
                                                                font: ralewayBold,
                                                                fontSize: 10,
                                                                color:
                                                                PdfColor.fromHex('000'))),
                                                      ]))
                                            ]),
                                          ]))
                                ]),
                        pw.SizedBox(height: 100),
                        pw.Text('Ristrutturazione',
                            style: pw.TextStyle(
                                fontSize: 12,
                                font: ralewayMedium,
                                color: PdfColor.fromHex('499B79'))),
                        pw.SizedBox(height: 10),
                        pw.Text("${quotation.renovationContact?.addressInfo?.streetName!} ${quotation.renovationContact?.addressInfo?.streetNumber!}",
                            style: pw.TextStyle(
                                font: ralewayBold,
                                fontSize: 40,
                                color: PdfColor.fromHex('000000'))),
                        pw.SizedBox(height: 40),
                        pw.Text('Il tuo architetto',
                            style: pw.TextStyle(
                                fontSize: 12,
                                font: ralewayMedium,
                                color: PdfColor.fromHex('499B79'))),
                        pw.SizedBox(height: 10),
                            pw.Row(
                              crossAxisAlignment: pw.CrossAxisAlignment.center,
                              children: [
                                // Profile Picture
                                profileImageData != null ?
                                pw.Container(
                                  width: 37,
                                  height: 37,
                                  decoration: pw.BoxDecoration(
                                    shape: pw.BoxShape.circle,
                                    image: pw.DecorationImage(
                                      image: pw.MemoryImage(profileImageData),
                                      fit: pw.BoxFit.cover,
                                    ),
                                  ),
                                ) : pw.SizedBox.shrink(),

                                pw.SizedBox(width: 10), // Space between image and text

                                // Name
                                pw.Text(
                                  '${renovatorData.firstName ?? ""} ${renovatorData.lastName ?? ""}',
                                  style: pw.TextStyle(
                                    font: ralewayMedium,
                                    fontSize: 13,
                                    color: PdfColor.fromHex('000000'),
                                  ),
                                ),
                              ],
                            )
                      ])),
                  pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        pw.Text('Con il supporto e la collaborazione di',
                            style: pw.TextStyle(
                                fontSize: 11,
                                color: PdfColor.fromHex('ffffff'))),
                        pw.SizedBox(height: 10),
                        pw.Image(partnerImage,height: 65,width: 330),
                      ]
                  ),
                ],
              )),
        ])),
      ),
    );

    List<pw.Widget> cover2 = [];

    cover2.add(pw.Text('La differenza Newarc',
        style: pw.TextStyle(
            font: ralewayBold,
            fontSize: 18,
            color: PdfColor.fromHex('000'))));

    cover2.add(
      pw.SizedBox(height: 20),
    );

    cover2.add(pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.start,
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Expanded(
              child: pw.Container(
                  width: 221,
                  height: 132,
                  decoration: pw.BoxDecoration(
                    borderRadius: pw.BorderRadius.circular(10),
                    image: pw.DecorationImage(
                      image: cover2Image1,
                      fit: pw.BoxFit.cover,
                    ),
                  ))),
          pw.SizedBox(
            width: 40,
          ),
          pw.Expanded(
              child: pw.Container(
                  child: pw.Column(
                      mainAxisAlignment: pw.MainAxisAlignment.start,
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                pw.Text('Materioteca Newarc',
                    style: pw.TextStyle(
                        font: ralewayBold,
                        fontSize: 13,
                        color: PdfColor.fromHex('000'))),
                pw.SizedBox(height: 5),
                pw.Text(
                    'Seleziona i tuoi materiali direttamente nella nostra materioteca e scopri la loro resa di applicazione grazie all’esperienza digitale Newarc.',
                    style: pw.TextStyle(
                        font: ralewayMedium,
                        fontSize: 10,
                        color: PdfColor.fromHex('000'),
                        lineSpacing: 5))
              ]))),
        ]));

    cover2.add(pw.SizedBox(
      height: 40,
    ));

    cover2.add(pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.start,
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Expanded(
              child: pw.Container(
                  width: 221,
                  height: 132,
                  decoration: pw.BoxDecoration(
                    borderRadius: pw.BorderRadius.circular(10),
                    image: pw.DecorationImage(
                      image: cover2Image2,
                      fit: pw.BoxFit.cover,
                    ),
                  ))),
          pw.SizedBox(
            width: 40,
          ),
          pw.Expanded(
              child: pw.Container(
                  child: pw.Column(
                      mainAxisAlignment: pw.MainAxisAlignment.start,
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                pw.Text('App del cantiere',
                    style: pw.TextStyle(
                        font: ralewayBold,
                        fontSize: 13,
                        color: PdfColor.fromHex('000'))),
                pw.SizedBox(height: 5),
                pw.Text(
                    'L’applicazione Newarc Ristrutturazioni ti permetterà di rimanere costantemente aggiornato sullo stato dei lavori, comodamente da casa tua.',
                    style: pw.TextStyle(
                        font: ralewayMedium,
                        fontSize: 10,
                        color: PdfColor.fromHex('000'),
                        lineSpacing: 5))
              ]))),
        ]));

    cover2.add(pw.SizedBox(
      height: 40,
    ));

    cover2.add(pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.start,
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Expanded(
              child: pw.Container(
                  width: 221,
                  height: 132,
                  decoration: pw.BoxDecoration(
                    borderRadius: pw.BorderRadius.circular(10),
                    image: pw.DecorationImage(
                      image: cover2Image3,
                      fit: pw.BoxFit.cover,
                    ),
                  ))),
          pw.SizedBox(
            width: 40,
          ),
          pw.Expanded(
              child: pw.Container(
                  child: pw.Column(
                      mainAxisAlignment: pw.MainAxisAlignment.start,
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                pw.Text('Architetto dedicato',
                    style: pw.TextStyle(
                        font: ralewayBold,
                        fontSize: 13,
                        color: PdfColor.fromHex('000'))),
                pw.SizedBox(height: 5),
                pw.Text(
                    'Uno dei nostri architetti sarà al tuo fianco per progettare insieme a te la tua futura casa e per seguire il cantiere durante tutte le fasi.',
                    style: pw.TextStyle(
                        font: ralewayMedium,
                        fontSize: 10,
                        color: PdfColor.fromHex('000'),
                        lineSpacing: 5))
              ]))),
        ]));

    cover2.add(pw.SizedBox(
      height: 40,
    ));

    cover2.add(pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.start,
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Expanded(
              child: pw.Container(
                  width: 221,
                  height: 132,
                  decoration: pw.BoxDecoration(
                    borderRadius: pw.BorderRadius.circular(10),
                    image: pw.DecorationImage(
                      image: cover2Image4,
                      fit: pw.BoxFit.cover,
                    ),
                  ))),
          pw.SizedBox(
            width: 50,
          ),
          pw.Expanded(
              child: pw.Container(
                  child: pw.Column(
                      mainAxisAlignment: pw.MainAxisAlignment.start,
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                pw.Text('Ditte selezionate',
                    style: pw.TextStyle(
                        font: ralewayBold,
                        fontSize: 13,
                        color: PdfColor.fromHex('000'))),
                pw.SizedBox(height: 5),
                pw.Text(
                    'Ci avvaliamo solo di professionisti con cui instauriamo lunghe collaborazioni. Un rapporto di fiducia basato sulla qualità.',
                    style: pw.TextStyle(
                        font: ralewayMedium,
                        fontSize: 10,
                        color: PdfColor.fromHex('000'),
                        lineSpacing: 5))
              ]))),
        ]));
    
    /**
      * Cover 2
      */
    pdf.addPage(pw.MultiPage(
      theme:
          pw.ThemeData(defaultTextStyle: pw.TextStyle(font: ralewayMedium)),
      pageFormat: PdfPageFormat.a4,
      footer: (context) => pw.Container(
          // color: PdfColors.amber,
          height: 35,
          child: pw.Column(
            mainAxisAlignment: pw.MainAxisAlignment.start,
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.SizedBox(height: 10),
              pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: pw.CrossAxisAlignment.center,
                  children: [
                    pw.Expanded(
                      flex: 1,
                      child: pw.Image(coverLogo, height: 25),
                    ),
                    pw.Expanded(
                      flex: 5,
                      child: pw.Padding(
                          padding: pw.EdgeInsets.symmetric(horizontal: 20),
                          child: pw.Text(
                              "NEWARC SRL - Sede Legale Via Vittorio Emanuele II 29, Chieri - Sede Operativa Corso Ferrucci 36, Torino - P.Iva 12533550013 - <EMAIL> - 011.02.63.850",
                              overflow: pw.TextOverflow.visible,
                              style: pw.TextStyle(
                                  font: ralewayMedium,
                                  fontSize: 7,
                                  color: PdfColor.fromHex('6E6E6E')))),
                    ),
                    pw.Expanded(
                        flex: 1,
                        child: pw.Text('${context.pageNumber}',
                            textAlign: pw.TextAlign.right,
                            style: pw.TextStyle(
                                font: ralewayBold,
                                fontSize: 13,
                                color: PdfColor.fromHex('000'))))
                  ]),
            ],
          )),
      margin: const pw.EdgeInsets.all(25),
      build: (pw.Context context) => cover2,
    ));

    /**
      * Content
      */
    pdf.addPage(pw.MultiPage(
      theme:
          pw.ThemeData(defaultTextStyle: pw.TextStyle(font: ralewayMedium)),
      pageFormat: PdfPageFormat.a4,
      footer: (context) => pw.Container(
          // color: PdfColors.amber,
          height: 110,
          child: pw.Column(
            mainAxisAlignment: pw.MainAxisAlignment.start,
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.SizedBox(height: 10),
              pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  mainAxisAlignment: pw.MainAxisAlignment.start,
                  children: [
                    pw.Text("Firma per accettazione",
                        overflow: pw.TextOverflow.visible,
                        style: pw.TextStyle(
                            font: ralewayBold,
                            fontSize: 11,
                            color: PdfColors.black)),
                    pw.Container(
                        height: 1,
                        width: 250,
                        margin: pw.EdgeInsets.only(top: 30),
                        decoration: pw.BoxDecoration(
                          color: PdfColors.black,
                        ))
                  ]),
              pw.SizedBox(height: 30),
              pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: pw.CrossAxisAlignment.center,
                  children: [
                    pw.Expanded(
                      flex: 1,
                      child: pw.Image(coverLogo, height: 25),
                    ),
                    pw.Expanded(
                      flex: 5,
                      child: pw.Padding(
                          padding: pw.EdgeInsets.symmetric(horizontal: 20),
                          child: pw.Text(
                              "NEWARC SRL - Sede Legale Via Vittorio Emanuele II 29, Chieri - Sede Operativa Corso Ferrucci 36, Torino - P.Iva 12533550013 - <EMAIL> - 011.02.63.850",
                              overflow: pw.TextOverflow.visible,
                              style: pw.TextStyle(
                                  font: ralewayMedium,
                                  fontSize: 7,
                                  color: PdfColor.fromHex('6E6E6E')))),
                    ),
                    pw.Expanded(
                        flex: 1,
                        child: pw.Text('${context.pageNumber}',
                            textAlign: pw.TextAlign.right,
                            style: pw.TextStyle(
                                font: ralewayBold,
                                fontSize: 13,
                                color: PdfColor.fromHex('000'))))
                  ]),
            ],
          )),
      margin: const pw.EdgeInsets.all(25),
      build: (pw.Context context) => pdfDataWidgets,
    ));


    /**
        Graph page
      */

    pdf.addPage(pw.MultiPage(
      theme:
          pw.ThemeData(defaultTextStyle: pw.TextStyle(font: ralewayMedium)),
      pageFormat: PdfPageFormat.a4,
      footer: (context) => pw.Container(
          // color: PdfColors.amber,
          height: 110,
          child: pw.Column(
            mainAxisAlignment: pw.MainAxisAlignment.start,
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.SizedBox(height: 10),
              pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  mainAxisAlignment: pw.MainAxisAlignment.start,
                  children: [
                    pw.Text("Firma per accettazione",
                        overflow: pw.TextOverflow.visible,
                        style: pw.TextStyle(
                            font: ralewayBold,
                            fontSize: 11,
                            color: PdfColors.black)),
                    pw.Container(
                        height: 1,
                        width: 250,
                        margin: pw.EdgeInsets.only(top: 30),
                        decoration: pw.BoxDecoration(
                          color: PdfColors.black,
                        ))
                  ]),
              pw.SizedBox(height: 30),
              pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: pw.CrossAxisAlignment.center,
                  children: [
                    pw.Expanded(
                      flex: 1,
                      child: pw.Image(coverLogo, height: 25),
                    ),
                    pw.Expanded(
                      flex: 5,
                      child: pw.Padding(
                          padding: pw.EdgeInsets.symmetric(horizontal: 20),
                          child: pw.Text(
                              "NEWARC SRL - Sede Legale Via Vittorio Emanuele II 29, Chieri - Sede Operativa Corso Ferrucci 36, Torino - P.Iva 12533550013 - <EMAIL> - 011.02.63.850",
                              overflow: pw.TextOverflow.visible,
                              style: pw.TextStyle(
                                  font: ralewayMedium,
                                  fontSize: 7,
                                  color: PdfColor.fromHex('6E6E6E')))),
                    ),
                    pw.Expanded(
                        flex: 1,
                        child: pw.Text('${context.pageNumber}',
                            textAlign: pw.TextAlign.right,
                            style: pw.TextStyle(
                                font: ralewayBold,
                                fontSize: 13,
                                color: PdfColor.fromHex('000'))))
                  ]),
            ],
          )),
      margin: const pw.EdgeInsets.all(25),
      build: (pw.Context context) => pdfDataWidgetGraph,
    ));

    /**
        Last page
      */

    pdf.addPage(pw.MultiPage(
      theme:
          pw.ThemeData(defaultTextStyle: pw.TextStyle(font: ralewayMedium)),
      pageFormat: PdfPageFormat.a4,
      footer: (context) => pw.Container(
          // color: PdfColors.amber,
          height: 110,
          child: pw.Column(
            mainAxisAlignment: pw.MainAxisAlignment.start,
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.SizedBox(height: 10),
              pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  mainAxisAlignment: pw.MainAxisAlignment.start,
                  children: [
                    pw.Text("Firma per accettazione",
                        overflow: pw.TextOverflow.visible,
                        style: pw.TextStyle(
                            font: ralewayBold,
                            fontSize: 11,
                            color: PdfColors.black)),
                    pw.Container(
                        height: 1,
                        width: 250,
                        margin: pw.EdgeInsets.only(top: 30),
                        decoration: pw.BoxDecoration(
                          color: PdfColors.black,
                        ))
                  ]),
              pw.SizedBox(height: 30),
              pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: pw.CrossAxisAlignment.center,
                  children: [
                    pw.Expanded(
                      flex: 1,
                      child: pw.Image(coverLogo, height: 25),
                    ),
                    pw.Expanded(
                      flex: 5,
                      child: pw.Padding(
                          padding: pw.EdgeInsets.symmetric(horizontal: 20),
                          child: pw.Text(
                              "NEWARC SRL - Sede Legale Via Vittorio Emanuele II 29, Chieri - Sede Operativa Corso Ferrucci 36, Torino - P.Iva 12533550013 - <EMAIL> - 011.02.63.850",
                              overflow: pw.TextOverflow.visible,
                              style: pw.TextStyle(
                                  font: ralewayMedium,
                                  fontSize: 7,
                                  color: PdfColor.fromHex('6E6E6E')))),
                    ),
                    pw.Expanded(
                        flex: 1,
                        child: pw.Text('${context.pageNumber}',
                            textAlign: pw.TextAlign.right,
                            style: pw.TextStyle(
                                font: ralewayBold,
                                fontSize: 13,
                                color: PdfColor.fromHex('000'))))
                  ]),
            ],
          )),
      margin: const pw.EdgeInsets.all(25),
      build: (pw.Context context) => pdfDataWidgets2,
    ));

    // Save PDF to bytes
    final pdfBytes = await pdf.save();
    // Create a Blob from PDF bytes
    final blob = html.Blob([pdfBytes], 'application/pdf');
    // Create a link element
    final url = html.Url.createObjectUrlFromBlob(blob);
    final anchor = html.AnchorElement(href: url)
      ..setAttribute('download', "${quotation.code!}_V${quotation.version!}_R${quotation.revision!}" + '.pdf')
      ..click();

    // Clean up
    html.Url.revokeObjectUrl(url);
  } catch (e, s) {
    print({e, s});
  }
}

pw.Widget headRow(ralewayMedium, ralewayBold) {
  return pw.Container(
    margin: pw.EdgeInsets.only(bottom: 5),
    padding: const pw.EdgeInsets.only(left: 15, right: 15, bottom: 0, top: 5),
    child: pw.Row(
      mainAxisSize: pw.MainAxisSize.max,
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Expanded(
          flex: 6,
          child: pw.Container(
),
        ),
        pw.SizedBox(
          width: 5,
        ),
        pw.Expanded(
          flex: 1,
          child: pw.Text('Unita',
              textAlign: pw.TextAlign.right,
              
              style: pw.TextStyle(fontSize: 7, color: PdfColor.fromHex('6E6E6E'), letterSpacing: 0.3 )),
        ),
        pw.SizedBox(
          width: 5,
        ),
        pw.Expanded(
            flex: 1,
            child: pw.Text('Quantità',
                textAlign: pw.TextAlign.right,
                style: pw.TextStyle(
                    fontSize: 7, color: PdfColor.fromHex('6E6E6E'), letterSpacing: 0.3))),
        pw.SizedBox(
          width: 5,
        ),
        pw.Expanded(
            flex: 1,
            child: pw.Text(
                'Unitario',
                textAlign: pw.TextAlign.right,
                style: pw.TextStyle(
                    fontSize: 7, color: PdfColor.fromHex('6E6E6E'), letterSpacing: 0.3))),
        pw.SizedBox(
          width: 5,
        ),
        pw.Expanded(
            flex: 1,
            child: pw.Text(
                'Costo',
                textAlign: pw.TextAlign.right,
                style: pw.TextStyle(
                    fontSize: 7,
                    color: PdfColor.fromHex('6E6E6E'), letterSpacing: 0.3))),
      ],
    ),
  );
}

pw.Widget dataRow(String category, Map rowData, ralewayMedium, ralewayBold) {
  return pw.Container(
    margin: pw.EdgeInsets.only(bottom: 5),
    padding: const pw.EdgeInsets.only(left: 15, right: 15, bottom: 0, top: 0),
    child: pw.Row(
      mainAxisSize: pw.MainAxisSize.max,
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Expanded(
          flex: 6,
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Container(
                constraints: pw.BoxConstraints(maxWidth: 300),
                child: pw.Text(
                    rowData['code'] +
                        (rowData['code'] != '' ? ' - ' : '') +
                        rowData['title'],
                    style: pw.TextStyle(
                        fontSize: 8, color: PdfColor.fromHex('000'))),
              ),
              pw.SizedBox(
                height: 5,
              ),
              pw.Padding(
                padding: const pw.EdgeInsets.only(right: 15),
                child: pw.Text(rowData['comment'],
                    style: pw.TextStyle(
                        fontSize: 8,
                        color: PdfColor.fromHex('6e6e6e'),
                        letterSpacing: 0.3)),
              ),
            ],
          ),
        ),
        pw.SizedBox(
          width: 5,
        ),
        pw.Expanded(
          flex: 1,
          child: pw.Text(rowData['measurementUnit'],
              textAlign: pw.TextAlign.right,
              style:
                  pw.TextStyle(fontSize: 9, color: PdfColor.fromHex('000'))),
        ),
        pw.SizedBox(
          width: 5,
        ),
        pw.Expanded(
            flex: 1,
            child: pw.Text(rowData['quantity'].toString(),
                textAlign: pw.TextAlign.right,
                style: pw.TextStyle(
                    fontSize: 9, color: PdfColor.fromHex('000')))),
        pw.SizedBox(
          width: 5,
        ),
        pw.Expanded(
            flex: 1,
            child: pw.Text(
                localCurrencyFormatMain.format(double.tryParse(rowData['unitPrice'].toString().replaceAll(".", "").replaceAll(",", ".")) ?? 0.0) + '€',
                textAlign: pw.TextAlign.right,
                style: pw.TextStyle(
                    fontSize: 9, color: PdfColor.fromHex('000')))),
        pw.SizedBox(
          width: 5,
        ),
        pw.Expanded(
            flex: 1,
            child: pw.Text(
                localCurrencyFormatMain.format(double.tryParse(rowData['total'].toString()) ?? 0) + '€',
                textAlign: pw.TextAlign.right,
                style: pw.TextStyle(
                  decoration: rowData['isDiscounted'] ?? false ? pw.TextDecoration.lineThrough : pw.TextDecoration.none ,
                    fontSize: 9,
                    color: PdfColor.fromHex('000')))
                  ),
      ],
    ),
  );
}