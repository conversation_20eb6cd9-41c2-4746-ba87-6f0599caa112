import 'package:firebase_auth/firebase_auth.dart';
import 'package:newarc_platform/classes/baseAddressInfo.dart';
import 'package:newarc_platform/classes/basePersonInfo.dart';

class Agency extends BaseAddressInfo {
  String? id;
    @Deprecated('Use fullAddress instead or address components of the agency object ie: Agency extends BaseAddressInfo  ') String? address;
  String? name;
  String? legalEntity;
  String? sdi;
  String? iban;
  @Deprecated('Use sedeLegaleFull instead') String? sedeLegale;
  BaseAddressInfo? sedeLegaleFull;
  String? email;
  int? level;
  int? points;
  String? vat;
  String? phone;

  ///subscription
  int? subscriptionPaymentDate;
  int? subscriptionStartDate;
  int? subscriptionServiceCount;
  int? subscriptionServiceCountLeft;
  int? subscriptionEndDate;
  String? subscriptionId;
  String? stripeCustomerId;

  String? renovationFee;

  List<Map>? documentPath;
  List? documents;



  bool? humanVerification;
  bool? notificationsRead;
  bool? isVisibile;
  int? registrationDate;
  bool? isActive;
  bool consentedToPrivacyPolicy = false;
  bool acceptedTermsConditions = false;

  late BasePersonInfo referencePerson;

  String? formationType;
  String? fiscalCode;

  Agency(Map<String, dynamic> agencyMap, String agencyId) {
    this.subscriptionPaymentDate = agencyMap['subscriptionPaymentDate'];
    this.subscriptionStartDate = agencyMap['subscriptionStartDate'];
    this.subscriptionServiceCount =
        agencyMap['subscriptionServiceCount'];
    this.subscriptionServiceCountLeft =
        agencyMap['subscriptionServiceCountLeft'];
    this.subscriptionId = agencyMap['subscriptionId'];
    this.stripeCustomerId = agencyMap['stripeCustomerId'];
    this.subscriptionEndDate = agencyMap['subscriptionEndDate'];

    this.id = agencyId;

    this.name = agencyMap['name'] ?? 'no name';
    this.legalEntity = agencyMap['legalEntity'] ?? 'no LE';
    this.sedeLegaleFull = BaseAddressInfo.fromMap(agencyMap['sedeLegaleFull']??{});
    this.sedeLegale = agencyMap['sedeLegale']?? this.sedeLegaleFull?.toFullAddress()??'no SL';
    this.sdi = agencyMap['sdi'] ?? 'no sdi';
    this.iban = agencyMap['iban'] ?? 'no iban';

    this.email = agencyMap['email'] ?? "no email";
    this.level = agencyMap['level'] ?? 'no level';
    this.points = agencyMap['points'] ?? 'no points';
    this.vat = agencyMap['vat'] ?? 'no vat';
    this.phone = agencyMap['phone'] ?? 'no phone';
    this.humanVerification = agencyMap['humanVerification'] ?? false;
    this.notificationsRead = agencyMap['notificationsRead'] ?? true;
    this.registrationDate =
        agencyMap['registrationDate'] ?? DateTime.now().millisecondsSinceEpoch;
    this.isActive =
        agencyMap['isActive'] != null ? agencyMap['isActive'] : true;
    this.consentedToPrivacyPolicy =
        agencyMap['consentedToPrivacyPolicy'] ?? false;
    this.acceptedTermsConditions =
        agencyMap['acceptedTermsConditions'] ?? false;

    this.address = agencyMap['address'] ?? 'no address';
    this.streetNumber = agencyMap['streetNumber'] ?? 'x';
    this.city = agencyMap['city'] ?? "no city";
    this.streetName = agencyMap['streetName'] ?? "no street name";
    this.postalCode = agencyMap['postalCode'] ?? "no postal code";
    this.province = agencyMap['province'] ?? "no province";
    this.region = agencyMap['region'] ?? "no region";
    this.country = agencyMap['country'] ?? "no country";
    this.latitude = agencyMap['latitude'] ?? "no latitude";
    this.longitude = agencyMap['longitude'] ?? "no longitude";
    this.fullAddress = agencyMap['fullAddress'] ?? this.toFullAddress();

    this.referencePerson = BasePersonInfo(agencyMap['referencePerson']??{});
    this.formationType = agencyMap['formationType'];
    this.fiscalCode = agencyMap['fiscalCode'];
    this.renovationFee = agencyMap['renovationFee'];

    final docData = agencyMap['documentPath'];
    if (docData != null) {
      if (docData is List) {
        this.documentPath = List<Map>.from(docData);
      }else {
        this.documentPath = [];
      }
    } else {
      this.documentPath = [];
    }
    documents = this.documentPath?.isNotEmpty ?? false
        ? this.documentPath?.map((doc) => doc['filename'] ?? '').where((f) => f.isNotEmpty).toList()
        : [];

  }

  Agency.empty() {
    this.subscriptionPaymentDate = null;
    this.subscriptionStartDate = null;
    this.subscriptionServiceCount = null;
    this.subscriptionId = null;
    this.subscriptionEndDate = null;
    this.subscriptionServiceCountLeft = null;
    this.stripeCustomerId = null;

    this.id = "";
    this.renovationFee = "";
    this.address = "";
    this.streetNumber = "";
    this.legalEntity = "";
    this.sdi = "";
    this.iban = "";
    this.sedeLegale = "";
    this.name = "";
    this.email = "";
    this.city = "";
    this.level = 1;
    this.points = 0;
    this.vat = "";
    this.phone = "";
    this.humanVerification = false;
    this.notificationsRead = true;
    this.registrationDate = DateTime.now().millisecondsSinceEpoch;
    this.isActive = true;
    this.consentedToPrivacyPolicy = false;
    this.acceptedTermsConditions = false;
    this.referencePerson = BasePersonInfo({});
    this.formationType = null;
    this.fiscalCode = null;
    this.renovationFee = "";
    this.documentPath = [];
  }

  Agency.fromDocument(Map<String, dynamic> data, String id) {
    this.id = id;
    this.subscriptionPaymentDate = data['subscriptionPaymentDate'];
    this.renovationFee = data['renovationFee'] ?? "";
    this.subscriptionStartDate = data['subscriptionStartDate'];
    this.subscriptionServiceCount = data['subscriptionServiceCount'];
    this.subscriptionServiceCountLeft =
        data['subscriptionServiceCountLeft'];
    this.subscriptionId = data['subscriptionId'];
    this.subscriptionEndDate = data['subscriptionEndDate'];
    this.stripeCustomerId = data['stripeCustomerId'];

    this.name = data['name'] ?? "no name";
    this.legalEntity = data['legalEntity'] ?? 'no LE';
    this.sdi = data['sdi'] ?? 'no sdi';
    this.iban = data['iban'] ?? 'no iban';
    this.sedeLegaleFull = BaseAddressInfo.fromMap(data['sedeLegaleFull']??{});
    this.sedeLegale = data['sedeLegale'] ?? this.sedeLegaleFull?.fullAddress??'no SL';
    this.email = data['email'] ?? "no email";
    this.level = data['level'] ?? "no level";
    this.points = data['points'] ?? "no points";
    this.vat = data['vat'] ?? "no vat";
    this.phone = data['phone'] ?? "no phone";
    this.humanVerification = data['humanVerification'] ?? false;
    this.notificationsRead = data['notificationsRead'] ?? false;
    this.registrationDate =
        data['registrationDate'] ?? DateTime.now().millisecondsSinceEpoch;
    this.isActive = data['isActive'] != null ? data['isActive'] : true;

    this.address = data['address'] ?? "no address";
    this.streetNumber = data['streetNumber'] ?? "x";
    this.city = data['city'] ?? "no city";
    this.streetName = data['streetName'] ?? "no street name";
    this.postalCode = data['postalCode'] ?? "no postal code";
    this.province = data['province'] ?? "no province";
    this.region = data['region'] ?? "no region";
    this.country = data['country'] ?? "no country";
    this.latitude = data['latitude'] ?? "no latitude";
    this.longitude = data['longitude'] ?? "no longitude";
    this.fullAddress = data['fullAddress'] ?? this.toFullAddress();
    this.consentedToPrivacyPolicy = data['consentedToPrivacyPolicy'] ?? false;
    this.acceptedTermsConditions = data['acceptedTermsConditions'] ?? false;
    this.referencePerson =  BasePersonInfo(data['referencePerson']??{});
    this.formationType = data.containsKey('formationType') ? data['formationType'] : null;
    this.fiscalCode = data.containsKey('fiscalCode') ? data['fiscalCode'] : null;
    final docData = data['documentPath'];
    if (docData != null) {
      if (docData is List) {
        this.documentPath = List<Map>.from(docData);
      }else {
        this.documentPath = [];
      }
    } else {
      this.documentPath = [];
    }
    documents = this.documentPath?.isNotEmpty ?? false
        ? this.documentPath?.map((doc) => doc['filename'] ?? '').where((f) => f.isNotEmpty).toList()
        : [];
  }

  /*Map<String, Object?> toJson() {
    return {'name': name, 'email': email};
  }*/

  Map<String, dynamic> toMap() {
    return {
      'subscriptionPaymentDate': this.subscriptionPaymentDate,
      'subscriptionStartDate': this.subscriptionStartDate,
      'subscriptionServiceCount': this.subscriptionServiceCount,
      'subscriptionId': this.subscriptionId,
      'subscriptionEndDate': this.subscriptionEndDate,
      'subscriptionServiceCountLeft': this.subscriptionServiceCountLeft,
      'stripeCustomerId': this.stripeCustomerId,
      'name': this.name,
      'legalEntity': this.legalEntity,
      'sedeLegale': this.sedeLegale,
      'sedeLegaleFull': this.sedeLegaleFull?.toMap(),
      'sdi': this.sdi,
      'iban': this.iban,
      'email': this.email,
      'level': this.level,
      'points': this.points,
      'vat': this.vat,
      'humanVerification': this.humanVerification,
      'phone': this.phone,
      'notificationsRead': this.notificationsRead,
      'registrationDate': this.registrationDate,
      'isActive': this.isActive,
      'consentedToPrivacyPolicy': this.consentedToPrivacyPolicy,
      'acceptedTermsConditions': this.acceptedTermsConditions,
      'address': this.address,
      'streetNumber': this.streetNumber,
      'city': this.city,
      'streetName': this.streetName,
      'postalCode': this.postalCode,
      'province': this.province,
      'region': this.region,
      'country': this.country,
      'latitude': this.latitude,
      'longitude': this.longitude,
      'fullAddress': this.fullAddress,
      'referencePerson': this.referencePerson.toMap(),
      'formationType': this.formationType,
      'fiscalCode': this.fiscalCode,
      'renovationFee': this.renovationFee,
      'documentPath': this.documentPath,
    };
  }
}

class AgencyUser {
  String? id;
  String? agencyId;
  String? phone;
  String? email;
  String? name;
  String? surname;
  Agency? agency;
  User? user;
  String? type;
  String? role = 'agency';
  String? profilePicture;

  AgencyUser(Map<String, dynamic> userMap, String userId,
      Map<String, dynamic> agencyMap, String agencyId) {
    this.id = userId;
    this.name = userMap['name'];
    this.surname = userMap['surname'];
    this.phone = userMap['phone'] ?? "-";
    this.email = userMap['email'] ?? "-";
    this.agencyId = userMap['agencyId'];
    agencyMap['email'] = this.email;
    this.agency = Agency(agencyMap, agencyId);
    this.role = userMap['role'];
    this.profilePicture = userMap['profilePicture'];
    this.type = userMap['type'];
  }

  AgencyUser.fromDocument(Map<String, dynamic> data, String id) {
    this.id = id;
    this.name = data['name'];
    this.surname = data['surname'];
    this.phone = data['phone'] ?? "-";
    this.email = data['email'] ?? "-";
    this.agencyId = data['agencyId'];
    //this.agency = Agency(data, agencyId);
    this.role = data['role'];
    this.profilePicture = data['profilePicture'];
    this.type = data['type'];
  }

  AgencyUser.empty();

  Map<String, dynamic> toMap() {
    return {
      'id': this.id,
      'name': this.name,
      'surname': this.surname,
      'phone': this.phone,
      'email': this.email,
      'agencyId': this.agencyId,
      'type': this.type,
      'role': this.role,
      'profilePicture': this.profilePicture
    };
  }
}
