import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';

class BaseNewarcPopup extends StatefulWidget {
  final String? title;
  final Widget? column;
  final String? buttonText;
  final String? secondButtonText;
  final bool? noButton;
  final bool? isSecondButtonVisible;
  final Function? onPressed;
  final Function? onPressedSecondButton;
  final List<String> formErrorMessage;
  final bool? disableButton;
  final bool? disableSecondButton;
  final bool? isShowCloseIcon;
  final Color? buttonColor;
  final Color? secondButtonColor;

  const BaseNewarcPopup({
    required this.title,
    required this.column,
    this.buttonText,
    this.secondButtonText,
    this.onPressed,
    this.onPressedSecondButton,
    this.noButton = false,
    this.isSecondButtonVisible = false,
    this.formErrorMessage = const [],
    this.disableButton = false,
    this.disableSecondButton = false,
    this.isShowCloseIcon = true,
    this.buttonColor,
    this.secondButtonColor,
    Key? key,
  }) : super(key: key);

  @override
  State<BaseNewarcPopup> createState() => _BaseNewarcPopupState();
}

class _BaseNewarcPopupState extends State<BaseNewarcPopup> {
  TextStyle sectionTitleStyle =
      TextStyle(fontSize: 16, fontWeight: FontWeight.bold);

  final _formKey = GlobalKey<FormState>();
  bool loading = false;
  bool clicked = false;
  List<String>? formErrorMessage;
  bool disableButton = false;


  bool showOptionMissing(String? option) {
    try {
      return (option == null || option.isEmpty) && clicked;
    } catch (e, s) {
      // print({e,s});
      return false;
    }
  }

  @override
  void initState() {
    formErrorMessage = widget.formErrorMessage;
    super.initState();
  }

  @override
  void didUpdateWidget(BaseNewarcPopup oldWidget) {
    super.didUpdateWidget(oldWidget);
    formErrorMessage = widget.formErrorMessage;
    disableButton = widget.disableButton!;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      //width: 850, //MediaQuery.of(context).size.width * 0.6,
      //constraints: BoxConstraints(
      //      minWidth: 300,max),
      margin: EdgeInsets.symmetric(
        horizontal: 20,
      ),
      child: Material(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        child: loading
            ? Center(
                child: CircularProgressIndicator(
                    color: Theme.of(context).primaryColor),
              )
            : Stack(
                children: [
                  widget.isShowCloseIcon ?? true ?
                  Positioned(
                    top: 15,
                    right: 15,
                    child: Container(
                      height: 15,
                      width: 15,
                      child: MouseRegion(
                        cursor: SystemMouseCursors.click,
                        child: GestureDetector(
                            child: SvgPicture.asset(
                              'assets/icons/close-popup.svg',
                              width: 15,
                            ),
                            onTap: () {
                              Navigator.pop(context);
                            }),
                      ),
                    ),
                  ) : SizedBox.shrink(),
                  Form(
                    key: _formKey,
                    child: Padding(
                      padding: EdgeInsets.only(bottom: 20.0, top: widget.title != '' ? 20 : 0 ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          if( widget.title != '' ) Padding(
                            padding: EdgeInsets.symmetric(
                              horizontal: 20,
                            ),
                            child: NarFormLabelWidget(
                              label: widget.title,
                              textColor: Colors.black,
                              fontSize: 17,
                              fontWeight: '700',
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.only(
                              left: 40.0,
                              right: 40.0,
                              top: 25,
                            ),
                            child: widget.column,
                          ),
                          SizedBox(height: 10),
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              formErrorMessage!.length == 0 || formErrorMessage![0] == ''
                              ? Container()
                              : NarFormLabelWidget(
                                label: formErrorMessage!.length > 0
                                    ? formErrorMessage!.join('')
                                    : '',
                                fontSize: 12,
                                fontWeight: 'bold',
                              ),
                            ],
                          ),
                          SizedBox(height: 5),
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              widget.noButton!
                                  ? Container()
                                  : Row(
                                    children: [
                                      BaseNewarcButton(
                                          color: widget.buttonColor,
                                          disableButton: disableButton,
                                          notAccent: disableButton,
                                          buttonText: widget.buttonText == null
                                              ? 'OK'
                                              : widget.buttonText,
                                          onPressed: () async {
                                            if (_formKey.currentState!.validate()) {
                                              try {
                                                if (widget.onPressed != null) {
                                                  bool response =
                                                      await widget.onPressed!();
                                                  if (response) {
                                                    Navigator.of(context)
                                                        .pop(response);
                                                  } else if (response == false) {
                                                    setState(() {});
                                                    print("BaseNewarcPopup: No response");
                                                    // Navigator.of(context).pop(false);
                                                  } else {
                                                    Navigator.of(context)
                                                        .pop(response);
                                                  }
                                                } else {
                                                  Navigator.of(context).pop(true);
                                                }
                                              } catch (e) {
                                                Navigator.of(context).pop(true);
                                              }
                                            } else {
                                              setState(() {
                                                formErrorMessage!.clear();
                                                formErrorMessage!.add('Controlla tutti i campi.');
                                              });
                                            }
                                          }),

                                      widget.isSecondButtonVisible ?? false ?
                                      Padding(
                                        padding: const EdgeInsets.only(left: 10),
                                        child: BaseNewarcButton(
                                            color: widget.secondButtonColor,
                                            disableButton: widget.disableSecondButton,
                                            notAccent: widget.disableSecondButton,
                                            buttonText: widget.secondButtonText == null
                                                ? 'OK'
                                                : widget.secondButtonText,
                                            onPressed: () async {
                                              if (_formKey.currentState!.validate()) {
                                                try {
                                                  if (widget.onPressedSecondButton != null) {
                                                    bool response =
                                                    await widget.onPressedSecondButton!();
                                                    if (response) {
                                                      Navigator.of(context)
                                                          .pop(response);
                                                    } else if (response == false) {
                                                      setState(() {});
                                                      print("BaseNewarcPopup: No response");
                                                      // Navigator.of(context).pop(false);
                                                    } else {
                                                      Navigator.of(context)
                                                          .pop(response);
                                                    }
                                                  } else {
                                                    Navigator.of(context).pop(true);
                                                  }
                                                } catch (e) {
                                                  Navigator.of(context).pop(true);
                                                }
                                              } else {
                                                setState(() {
                                                  formErrorMessage!.clear();
                                                  formErrorMessage!.add('Controlla tutti i campi.');
                                                });
                                              }
                                            }),
                                      ) : Container()
                                    ],
                                  )
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
      ),
    );
  }
}
