import 'dart:developer';
import 'dart:js_interop';

import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/classes/newarcMaterial.dart';
import 'package:newarc_platform/classes/newarcProject.dart';
import 'package:newarc_platform/classes/process.dart';
import 'package:newarc_platform/classes/projectEconomic.dart';
import 'package:newarc_platform/classes/supplier.dart';
import 'package:newarc_platform/classes/user.dart';
import 'package:newarc_platform/utils/storage.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
import 'package:newarc_platform/widget/UI/color-bg-dropdown.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/image-select-box.dart';
import 'package:newarc_platform/widget/UI/select-box.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:intl/intl.dart';

import '../../../utils/color_schema.dart';
import '../../UI/tab/common_icon_button.dart';

class ProjectManageMaterial extends StatefulWidget {
  final NewarcProject? project;
  final Function? updateProject;

  const ProjectManageMaterial({Key? key, this.project, this.updateProject }) : super(key: key);

  @override
  State<ProjectManageMaterial> createState() => _ProjectManageMaterialState();
}

class _ProjectManageMaterialState extends State<ProjectManageMaterial> {
  NumberFormat localCurrencyFormat =
      NumberFormat.currency(locale: 'it_IT', symbol: '\€', decimalDigits: 0);
  NumberFormat localCurrencyFormatMain =
      NumberFormat.currency(locale: 'it_IT', symbol: '', decimalDigits: 0);

  TextEditingController contIndexPlace = new TextEditingController();
  TextEditingController contProductName = new TextEditingController();
  TextEditingController contSupplierName = new TextEditingController();
  TextEditingController contGroup = new TextEditingController();
  TextEditingController contTransportType = new TextEditingController();
  TextEditingController contUnit = new TextEditingController();
  TextEditingController contQuantity = new TextEditingController();
  TextEditingController contPrice = new TextEditingController();
  TextEditingController contCost = new TextEditingController();
  // TextEditingController contStatus = new TextEditingController();

  bool isAccount = true;
  TextEditingController contAccount = new TextEditingController();

  List<TextEditingController> contStatus = [];
  List<TextEditingController> contQuality = [];
  List<TextEditingController> contDeposit = [];

  List<String> groups = [
    '',
    'Pavimenti',
    'Sanitari',
    'Rivestimenti',
    'Illuminazione',
    'Arredi', 
    'Rubinetterie',
    'Idraulica', 
    'Materiali edili', 
    'Serramenti', 
    'Decorazioni', 
    'Varie'
  ];

  List<Map> deposit = [
    {
      'value': '',
      'label': '',
      'bgColor': Color(0xffF2F2F2),
      'textColor': Colors.black
    },
    {
      'value': 'Cantiere',
      'label': 'Cantiere',
      'bgColor': Color(0xffF2F2F2),
      'textColor': Colors.black
    },
    {
      'value': 'Magazzino',
      'label': 'Magazzino',
      'bgColor': Color(0xffF2F2F2),
      'textColor': Colors.black
    }
  ];

  List<Map> quality = [
    {
      'value': '',
      'label': '',
      'bgColor': Color(0xffA4A4A4),
      'textColor': Colors.white
    },
    {
      'value': 'Da verificare',
      'label': 'Da verificare',
      'bgColor': Color(0xffA4A4A4),
      'textColor': Colors.white
    },
    {
      'value': 'Verificata',
      'label': 'Verificata',
      'bgColor': Color(0xff489B79),
      'textColor': Colors.white
    },
    {
      'value': 'Da sostituire',
      'label': 'Da sostituire',
      'bgColor': Color(0xffD56D6D),
      'textColor': Colors.white
    }
  ];

  List<Map> statusDelivery = [
    {
      'value': '',
      'label': '',
      'bgColor': Color(0xffA4A4A4),
      'textColor': Colors.white
    },
    {
      'value': 'Da ordinare',
      'label': 'Da ordinare',
      'bgColor': Color(0xffA4A4A4),
      'textColor': Colors.white
    },
    {
      'value': 'Ordinato',
      'label': 'Ordinato',
      'bgColor': Color(0xffA4A4A4),
      'textColor': Colors.white
    },
    {
      'value': 'Consegnato',
      'label': 'Consegnato',
      'bgColor': Color(0xff489B79),
      'textColor': Colors.white
    }
  ];

  List<Map> statusCollection = [
    {
      'value': '',
      'label': '',
      'bgColor': Color(0xffA4A4A4),
      'textColor': Colors.white
    },
    {
      'value': 'Da ordinare',
      'label': 'Da ordinare',
      'bgColor': Color(0xffA4A4A4),
      'textColor': Colors.white
    },
    {
      'value': 'Ordinato',
      'label': 'Ordinato',
      'bgColor': Color(0xffA4A4A4),
      'textColor': Colors.white
    },
    {
      'value': 'Da ritirare',
      'label': 'Da ritirare',
      'bgColor': Color(0xffA4A4A4),
      'textColor': Colors.white
    },
    {
      'value': 'Ritirato Newarc',
      'label': 'Ritirato Newarc',
      'bgColor': Color(0xff489B79),
      'textColor': Colors.white
    },
    {
      'value': 'Ritirato Ditta',
      'label': 'Ritirato Ditta',
      'bgColor': Color(0xff489B79),
      'textColor': Colors.white
    }
  ];

  List<bool> isAnimated = [];
  String progressMessage = '';
  double budget = 1;
  double spent = 0;
  Color? spendColor;

  String? tmpTitle;
  bool? showTitle;

  /* 
  * This is a Hard coded value to test the Delete 
  * true: if wanted to allow to delete
  * false: if wanted to deny 
  */
  bool canDeleteProcess = true;

  @override
  void initState() {
    super.initState();

    try {
      if(widget.project!.provisionalAccountId != null && widget.project!.provisionalAccountId != ""){
        fetchProvisional();
      }
      setInitialValues();
    } catch (e,s) {
      print({e,s});
    }

    
  }

  @protected
  void didUpdateWidget(ProjectManageMaterial oldWidget) {
    super.didUpdateWidget(oldWidget);

    setInitialValues();
  }

  fetchProvisional() async {
    DocumentSnapshot<Map<String, dynamic>> collectionSnapshot =
        await FirebaseFirestore.instance
            .collection(appConfig.COLLECT_PROVISIONAL_ECONOMIC_ACCOUNT)
            .doc(widget.project!.provisionalAccountId)
            .get();

    ProjectEconomic? tempEconomic = ProjectEconomic.empty();

    if (collectionSnapshot.exists) {
      tempEconomic = ProjectEconomic.fromDocument(
          collectionSnapshot.data()!, collectionSnapshot.id);

      if (tempEconomic
              .restorationAndMaterail!['materiali'].runtimeType
              .toString() !=
          'Null') {
        setState(() {
          budget = double.tryParse(tempEconomic!.restorationAndMaterail!['materiali'])!;
        });
      }
    }
  }

  setInitialValues() {
    isAnimated.clear();

    // budget = 1;
    spent = 0;

    tmpTitle = '';
    showTitle = true;

    widget.project!.newarcMaterial!
        .sort((a, b) => a.group!.compareTo(b.group!));

    if (widget.project!.newarcMaterial!.length > 0) {
      for (var i = 0; i < widget.project!.newarcMaterial!.length; i++) {
        widget.project!.newarcMaterial![i].indexPlace = i;

        contStatus.add(new TextEditingController());
        contQuality.add(new TextEditingController());
        contDeposit.add(new TextEditingController());

        contStatus[i].text = widget.project!.newarcMaterial![i].status!;
        contQuality[i].text = widget.project!.newarcMaterial![i].quality!;
        contDeposit[i].text = widget.project!.newarcMaterial![i].deposit!;

        spent += widget.project!.newarcMaterial![i].cost!;
        isAnimated.add(false);
      }
    } else {
      spent = 0; 
    }

    if(widget.project?.type == "Ristrutturazione"){
      budget = widget.project?.budgetGastisciMaterialiRistrutturazione ?? 1.0 ;
    }

    double spendPercent = (spent / budget) * 100;
    if (spendPercent <= 25) {
      spendColor = Color(0xff4E9A7A);
    } else if (spendPercent > 25 && spendPercent <= 100) {
      spendColor = Color(0xffFFC702);
    } else if (spendPercent > 100) {
      spendColor = Color(0xffE82525);
    }

    setState(() {});
  }

  Widget statusIcon(bool status) {
    return Container(
      height: 16,
      width: 16,
      decoration: BoxDecoration(
        color: status ? Theme.of(context).primaryColor : Color(0xffD1D1D1),
        borderRadius: BorderRadius.circular(14),
      ),
      child: Center(
        child: Icon(
          status ? Icons.check : Icons.close_rounded,
          color: Colors.white,
          size: 12,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: double.infinity,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.max,
        children: [
          Expanded(
            child: ListView(
              children: [
                SizedBox(height: 20),
                Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    NarFormLabelWidget(
                      label: 'Gestisci Materiali',
                      fontSize: 20,
                      fontWeight: 'bold',
                    ),
                    BaseNewarcButton(
                      onPressed: () {
                        addMaterialPopup(context, NewarcMaterial.empty());
                      },
                      buttonText: 'Aggiungi Materiale',
                    )
                  ],
                ),
                SizedBox(height: 30),
                Container(
                  // color: Colors.grey,
                  // width: 200,
                  child: ListView(
                    shrinkWrap: true,
                    children: [
                      Row(
                        children: [
                          Container(
                            width: 200,
                            padding: EdgeInsets.symmetric(
                                vertical: 7, horizontal: 15),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(
                                width: 1,
                                color: Color.fromRGBO(214, 214, 214, 1),
                              ),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  mainAxisSize: MainAxisSize.max,
                                  children: [
                                    NarFormLabelWidget(
                                        label: 'Budget',
                                        fontSize: 12,
                                        fontWeight: '600',
                                        textColor: Colors.black),

                                    widget.project?.type == "Ristrutturazione" ?
                                    IconButtonWidget(
                                      onTap: () {
                                        showEditBudgetPopup();
                                      },
                                      iconPadding: EdgeInsets.zero,
                                      height: 15,
                                      width: 15,
                                      isSvgIcon: false,
                                      backgroundColor: AppColor.white,
                                      icon:
                                      'assets/icons/edit.png',
                                      iconColor: AppColor.greyColor,
                                    ) : SizedBox.fromSize(),
                                  ],
                                ),
                                SizedBox(
                                  height: 10,
                                ),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisSize: MainAxisSize.max,
                                  children: [
                                    NarFormLabelWidget(
                                        label: localCurrencyFormatMain.format(budget),
                                        fontSize: 25,
                                        fontWeight: 'bold',
                                        textColor: Colors.black),
                                    SizedBox(
                                      width: 20,
                                    ),
                                    NarFormLabelWidget(
                                        label: '€',
                                        fontSize: 17,
                                        fontWeight: 'bold',
                                        textColor: Colors.black),
                                  ],
                                )
                              ],
                            ),
                          ),
                          SizedBox(
                            width: 25,
                          ),
                          Container(
                            width: 190,
                            clipBehavior: Clip.hardEdge,
                            padding: EdgeInsets.symmetric(
                                vertical: 7, horizontal: 15),
                            decoration: BoxDecoration(
                              color: spendColor!,
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(
                                width: 1,
                                color: spendColor!,
                              ),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                NarFormLabelWidget(
                                  label: 'Costo raggiunto',
                                  fontSize: 12,
                                  fontWeight: '600',
                                  textColor: Colors.white,
                                ),
                                SizedBox(
                                  height: 10,
                                ),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisSize: MainAxisSize.max,
                                  children: [
                                    NarFormLabelWidget(
                                      label:
                                          localCurrencyFormatMain.format(spent),
                                      fontSize: 25,
                                      fontWeight: 'bold',
                                      textColor: Colors.white,
                                    ),
                                    SizedBox(
                                      width: 20,
                                    ),
                                    NarFormLabelWidget(
                                      label: '€',
                                      fontSize: 17,
                                      fontWeight: 'bold',
                                      textColor: Colors.white,
                                    ),
                                  ],
                                )
                              ],
                            ),
                          )
                        ],
                      ),
                      Column(
                        children: widget.project!.newarcMaterial!.map((e) {
                          return processWrapper(context, e);
                        }).toList(),
                      )
                    ],
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }


  Future<void> showEditBudgetPopup() async {
    TextEditingController budgetController = TextEditingController(text: budget.toString());
    List<String> formErrorMessage = [];
    await showDialog(
        context: context,
        builder: (BuildContext _context) {
          return StatefulBuilder(builder: (__context, _setState) {
            return Center(
                child: BaseNewarcPopup(
                  title: "Inserisci Budget",
                  buttonText: "Salva",
                  formErrorMessage: formErrorMessage,
                  onPressed: () async {
                    _setState((){
                      formErrorMessage.clear();
                      formErrorMessage.add("Salvataggio in corso...");
                    });
                    try{
                      double priceOnchange = double.tryParse(budgetController.text.trim().replaceAll('.', '').replaceAll(',', '.').toString()) ?? 0.0;

                      double spendPercent = (spent / priceOnchange) * 100;
                      if (spendPercent <= 25) {
                        spendColor = Color(0xff4E9A7A);
                      } else if (spendPercent > 25 && spendPercent <= 100) {
                        spendColor = Color(0xffFFC702);
                      } else if (spendPercent > 100) {
                        spendColor = Color(0xffE82525);
                      }
                      setState(() {
                        budget = priceOnchange;
                      });

                      await FirebaseFirestore.instance
                          .collection(appConfig.COLLECT_NEWARC_PROJECTS)
                          .doc(widget.project!.id)
                          .update({"budgetGastisciMaterialiRistrutturazione" : priceOnchange});
                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Salvato");
                      });
                    }catch(e){
                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Si è verificato un errore.");
                      });
                      log("Error while editing budget ----> ${e.toString()}");
                    }
                  },
                  buttonColor: Theme.of(context).primaryColor,
                  column: Container(
                    width: 400,
                    child: CustomTextFormField(
                      isExpanded: false,
                      isMoney: true,
                      label: "Budget",
                      controller: budgetController,
                      validator: (value) {
                        if (value == '') {
                          return 'Required!';
                        }
                        return null;
                      },
                    ),
                  ),
                ));
          });
        });
  }

  Widget processWrapper(BuildContext context, NewarcMaterial materialRow) {
    int count = widget.project!.newarcMaterial!.indexOf(materialRow);

    if (tmpTitle == materialRow.group) {
      showTitle = false;
    } else {
      showTitle = true;
      tmpTitle = materialRow.group!;
    }

    String paidOnDate = '';
    if (materialRow.paidOn! > 0 && materialRow.paidOn != null) {
      DateTime paidOn = DateTime.fromMillisecondsSinceEpoch(materialRow.paidOn!);
      paidOnDate = (paidOn.day > 9 ? paidOn.day.toString() : '0' + paidOn.day.toString()) + '/' +
          (paidOn.month > 9 ? paidOn.month.toString() : '0' + paidOn.month.toString()) + '/' +
          paidOn.year.toString();
    }

    String accountPaidOnDate = '';
    if (materialRow.accountPaidOn! > 0 && materialRow.accountPaidOn != null) {
      DateTime paidOn = DateTime.fromMillisecondsSinceEpoch(materialRow.accountPaidOn!);
      accountPaidOnDate = (paidOn.day > 9 ? paidOn.day.toString() : '0' + paidOn.day.toString()) + '/' +
          (paidOn.month > 9 ? paidOn.month.toString() : '0' + paidOn.month.toString()) + '/' +
          paidOn.year.toString();
    }

    // contStatus.text =

    return Column(
      children: [
        showTitle!
            ? Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 25),
                  NarFormLabelWidget(label: materialRow.group!.toUpperCase()),
                ],
              )
            : SizedBox(height: 15),
        SizedBox(
          height: showTitle! ? 8 : 0,
        ),
        Container(
          decoration: BoxDecoration(
            color: Color.fromRGBO(242, 242, 242, 1),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Column(
            children: [
              Padding(
                padding:
                    const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Expanded(
                          flex: 3,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              NarFormLabelWidget(
                                label: 'Prodotto',
                                fontSize: 12,
                                textColor: Color.fromRGBO(105, 105, 105, 1),
                                fontWeight: '500',
                              ),
                              SizedBox(
                                height: 10,
                              ),
                              NarFormLabelWidget(
                                label: materialRow.productName,
                                fontSize: 14,
                                fontWeight: 'bold',
                                textColor: Colors.black,
                              )
                            ],
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              NarFormLabelWidget(
                                label: 'Fornitore',
                                fontSize: 12,
                                textColor: Color.fromRGBO(105, 105, 105, 1),
                                fontWeight: '500',
                              ),
                              SizedBox(
                                height: 10,
                              ),
                              NarFormLabelWidget(
                                label: materialRow
                                    .supplierName, //.toString() +'€',
                                fontSize: 14,
                                fontWeight: 'bold',
                                textColor: Colors.black,
                              )
                            ],
                          ),
                        ),
                        Expanded(
                          flex: 1,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              NarFormLabelWidget(
                                label: 'Mq/Pz',
                                fontSize: 12,
                                textColor: Color.fromRGBO(105, 105, 105, 1),
                                fontWeight: '500',
                              ),
                              SizedBox(
                                height: 10,
                              ),
                              NarFormLabelWidget(
                                label: materialRow.quantity.toString() +
                                    materialRow.measureUnit!,
                                fontSize: 14,
                                fontWeight: 'bold',
                                textColor: Colors.black,
                              )
                            ],
                          ),
                        ),
                        Expanded(
                          flex: 1,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              NarFormLabelWidget(
                                label: 'Cons./Rit.',
                                fontSize: 12,
                                textColor: Color.fromRGBO(105, 105, 105, 1),
                                fontWeight: '500',
                              ),
                              SizedBox(
                                height: 10,
                              ),
                              NarFormLabelWidget(
                                label: materialRow.transportType,
                                fontSize: 14,
                                fontWeight: 'bold',
                                textColor: Colors.black,
                              )
                              // statusIcon(materialRow.hasPenalty!)
                              // ? Icon(Icons.check_circle, size: 14, color: Theme.of(context).primaryColor,)
                              // : Icon(Icons.close, size: 14, color: Theme.of(context).primaryColor,)
                            ],
                          ),
                        ),
                        Expanded(
                          flex: 1,
                          child: Column(
                            mainAxisSize: MainAxisSize.max,
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              IconButton(
                                iconSize: 20,
                                padding: EdgeInsets.all(0),
                                onPressed: () async {
                                  deleteDialog(context, materialRow);
                                },
                                icon: Image.asset(
                                    'assets/icons/trash-process.png',
                                    color: Color(0xff929292),
                                    height: 16),
                                constraints: BoxConstraints(
                                  minWidth:
                                      15.0, // Adjust these values to reduce padding
                                  minHeight: 15.0,
                                  maxHeight: 15,
                                  maxWidth: 15,
                                ),
                              ),
                              SizedBox(height: 10),
                              IconButton(
                                iconSize: 20,
                                padding: EdgeInsets.all(0),
                                onPressed: () {
                                  addMaterialPopup(context, materialRow);
                                },
                                icon: Image.asset(
                                    'assets/icons/edit-process.png',
                                    color: Color(0xff929292),
                                    height: 16),
                                constraints: BoxConstraints(
                                  minWidth:
                                      15.0, // Adjust these values to reduce padding
                                  minHeight: 15.0,
                                  maxHeight: 15,
                                  maxWidth: 15,
                                ),
                              ),
                            ],
                          ),
                        )
                      ],
                    ),
                    SizedBox(
                      height: 15,
                    ),
                    Row(
                      children: [
                        Expanded(
                          child: Row(
                            children: [
                              NarFormLabelWidget(
                                label: 'Stato',
                                fontSize: 12,
                                textColor: Color.fromRGBO(105, 105, 105, 1),
                                fontWeight: '500',
                              ),
                              SizedBox(
                                width: 7,
                              ),
                              NarColorBgDropdown(
                                iconColor: Colors.white,
                                controller: contStatus[materialRow.indexPlace!],
                                options: materialRow.transportType == 'Consegna'
                                    ? statusDelivery
                                    : statusCollection,
                                onChanged: () async {
                                  widget
                                      .project!
                                      .newarcMaterial![materialRow.indexPlace!]
                                      .status = contStatus[
                                          materialRow.indexPlace!]
                                      .text;

                                  final FirebaseFirestore _db =
                                      FirebaseFirestore.instance;
                                  try {
                                    await _db
                                        .collection(
                                            appConfig.COLLECT_NEWARC_PROJECTS)
                                        .doc(widget.project!.id)
                                        .update(widget.project!.toMap());

                                    setState(() {
                                      progressMessage = 'Saved!';
                                    });
                                  } catch (e) {
                                    setState(() {
                                      progressMessage = 'Error';
                                    });
                                  }

                                  tmpTitle = '';
                                  showTitle = false;

                                  // setInitialValues();
                                },
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          child: Row(
                            children: [
                              NarFormLabelWidget(
                                label: 'Qualità',
                                fontSize: 12,
                                textColor: Color.fromRGBO(105, 105, 105, 1),
                                fontWeight: '500',
                              ),
                              SizedBox(
                                width: 7,
                              ),
                              NarColorBgDropdown(
                                iconColor: Colors.white,
                                controller:
                                    contQuality[materialRow.indexPlace!],
                                options: quality,
                                onChanged: () async {
                                  widget
                                      .project!
                                      .newarcMaterial![materialRow.indexPlace!]
                                      .quality = contQuality[
                                          materialRow.indexPlace!]
                                      .text;
                                  final FirebaseFirestore _db =
                                      FirebaseFirestore.instance;
                                  try {
                                    await _db
                                        .collection(
                                            appConfig.COLLECT_NEWARC_PROJECTS)
                                        .doc(widget.project!.id)
                                        .update(widget.project!.toMap());

                                    setState(() {
                                      progressMessage = 'Saved!';
                                    });
                                  } catch (e) {
                                    setState(() {
                                      progressMessage = 'Error';
                                    });
                                  }

                                  tmpTitle = '';
                                  showTitle = false;

                                  // setInitialValues();
                                },
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          child: Row(
                            children: [
                              NarFormLabelWidget(
                                label: 'Deposito',
                                fontSize: 12,
                                textColor: Color.fromRGBO(105, 105, 105, 1),
                                fontWeight: '500',
                              ),
                              SizedBox(
                                width: 7,
                              ),
                              NarColorBgDropdown(
                                iconColor: Colors.black,
                                controller:
                                    contDeposit[materialRow.indexPlace!],
                                options: deposit,
                                borderColor: Color(0xffD5D5D5),
                                onChanged: () async {
                                  widget
                                      .project!
                                      .newarcMaterial![materialRow.indexPlace!]
                                      .deposit = contDeposit[
                                          materialRow.indexPlace!]
                                      .text;

                                  final FirebaseFirestore _db =
                                      FirebaseFirestore.instance;
                                  try {
                                    await _db
                                        .collection(
                                            appConfig.COLLECT_NEWARC_PROJECTS)
                                        .doc(widget.project!.id)
                                        .update(widget.project!.toMap());

                                    setState(() {
                                      progressMessage = 'Saved!';
                                    });
                                  } catch (e) {
                                    setState(() {
                                      progressMessage = 'Error';
                                    });
                                  }

                                  tmpTitle = '';
                                  showTitle = false;

                                  // setInitialValues();
                                },
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 15,
                    ),
                  ],
                ),
              ),
              Container(
                width: double.infinity,
                height: 1,
                decoration: BoxDecoration(
                  color: Color(0xFFDCDCDC),
                ),
                child: SizedBox(height: 0),
              ),
              isAnimated[count] == false
                  ? SizedBox(height: 0)
                  : Container(
                      // sizeFactor: _animation![count],
                      // axis: Axis.vertical,
                      child: Column(children: <Widget>[
                        Padding(
                          padding: EdgeInsets.all(10),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Expanded(
                                  child: Container(
                                      padding: EdgeInsets.symmetric(
                                          vertical: 10, horizontal: 15),
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                NarFormLabelWidget(
                                                  label: 'Costo',
                                                  fontSize: 12,
                                                  textColor: Color(0xFF696969),
                                                  fontWeight: '600',
                                                ),
                                                SizedBox(height: 7),
                                                NarFormLabelWidget(
                                                    label: localCurrencyFormat
                                                        .format(
                                                            materialRow.cost),
                                                    fontSize: 13,
                                                    textColor: Colors.black),
                                              ],
                                            ),
                                          ),
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                NarFormLabelWidget(
                                                  label: 'Acconto',
                                                  fontSize: 12,
                                                  textColor: Color(0xFF696969),
                                                  fontWeight: '600',
                                                ),
                                                SizedBox(height: 7),
                                                materialRow.account == 0
                                                    ? statusIcon(
                                                        materialRow.isAccount!)
                                                    : NarFormLabelWidget(
                                                        label: localCurrencyFormat
                                                            .format(materialRow
                                                                .account),
                                                        fontSize: 13,
                                                        textColor:
                                                            Colors.black),
                                              ],
                                            ),
                                          ),
                                          
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                NarFormLabelWidget(
                                                  label: 'Pagamento acconto',
                                                  fontSize: 12,
                                                  textColor: Color(0xFF696969),
                                                  fontWeight: '600',
                                                ),
                                                SizedBox(height: 7),
                                                
                                                Row(
                                                  children: [
                                                    statusIcon( materialRow.isAccountPaid! ),
                                                    SizedBox(width: 5),
                                                    materialRow.isAccountPaid == true
                                                    ? NarFormLabelWidget(
                                                      label: accountPaidOnDate,
                                                      fontSize: 10,
                                                      textColor: Colors.black)
                                                    : SizedBox(height: 0,)
                                                  ],
                                                ),
                                              ],
                                            ),
                                          ),
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                NarFormLabelWidget(
                                                  label: 'Pagamento completo',
                                                  fontSize: 12,
                                                  textColor: Color(0xFF696969),
                                                  fontWeight: '600',
                                                ),
                                                SizedBox(height: 7),
                                                
                                                Row(
                                                  children: [
                                                    statusIcon( materialRow.isPaid! ),
                                                    
                                                    SizedBox(width: 5),
                                                    materialRow.isPaid! == true 
                                                    ?  NarFormLabelWidget(
                                                        label: paidOnDate,
                                                        fontSize: 10,
                                                        textColor: Colors.black)
                                                    : SizedBox(height: 0,)
                                                    
                                                  ],
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      )))
                            ],
                          ),
                        )
                      ]),
                    ),
              TextButton(
                onPressed: () {
                  setState(() {
                    tmpTitle = '';
                    showTitle = false;
                    isAnimated[count] = !isAnimated[count];
                  });
                },
                style: ButtonStyle(
                    overlayColor:
                        MaterialStateProperty.all(Colors.transparent)),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    NarFormLabelWidget(
                      label: 'PAGAMENTI',
                      fontSize: 11,
                      textColor: Color(0xFF7d7d7d),
                    ),
                    SizedBox(width: 10),
                    isAnimated[count]
                        ? Image.asset(
                            'assets/icons/arrow_up.png',
                            height: 10,
                            color: Color(0xFF7d7d7d),
                          )
                        : Image.asset(
                            'assets/icons/arrow_down.png',
                            height: 10,
                            color: Color(0xFF7d7d7d),
                          )
                  ],
                ),
              ),
            ],
          ),
        )
      ],
    );
  }

  formatDateForParsing(String dateString) {
    List splittedDate = dateString.split('/');
    // print(splittedDate);
    return splittedDate[2] + '-' + splittedDate[1] + '-' + splittedDate[0];
  }

  messageDialog(BuildContext context, String title, String message) {
    return showDialog(
        context: context,
        builder: (BuildContext _bc1) {
          return StatefulBuilder(
              builder: (BuildContext _bc2, StateSetter setState) {
            return Center(
                child: BaseNewarcPopup(
              title: title,
              buttonText: 'Ok',
              onPressed: () async {
                // Navigator.pop(context);
                return true;
              },
              column: Container(
                  height: 99,
                  width: 465,
                  child: Center(
                    child: NarFormLabelWidget(
                        overflow: TextOverflow.visible,
                        label: message,
                        textAlign: TextAlign.center,
                        fontSize: 18,
                        fontWeight: '600',
                        height: 1.5,
                        textColor: Color(0xFF696969)),
                  )),
            ));
          });
        });
  }

  deleteDialog(BuildContext context, NewarcMaterial materialRow) {
    return showDialog(
        context: context,
        builder: (BuildContext _bc1) {
          return StatefulBuilder(
              builder: (BuildContext _bc2, StateSetter setState) {
            return Center(
                child: BaseNewarcPopup(
              title: 'Rimuovi materiale',
              buttonText: 'Rimuovi',
              onPressed: () async {
                widget.project!.newarcMaterial!
                    .removeAt(materialRow.indexPlace!);
                if (widget.project!.newarcMaterial!.length > 0) {
                  for (var i = 0;
                      i < widget.project!.newarcMaterial!.length;
                      i++) {
                    widget.project!.newarcMaterial![i].indexPlace = i;
                  }
                }

                final FirebaseFirestore _db = FirebaseFirestore.instance;

                try {
                  await _db
                      .collection(appConfig.COLLECT_NEWARC_PROJECTS)
                      .doc(widget.project!.id)
                      .update(widget.project!.toMap());

                  setState(() {
                    progressMessage = 'Saved!';
                  });
                } catch (e) {
                  setState(() {
                    progressMessage = 'Error';
                  });
                }

                setInitialValues();

                // Navigator.pop(context);
              },
              column: Container(
                  height: 99,
                  width: 465,
                  child: Center(
                    child: NarFormLabelWidget(
                        overflow: TextOverflow.visible,
                        label: 'Vuoi davvero eliminare questo materiale?',
                        textAlign: TextAlign.center,
                        fontSize: 18,
                        fontWeight: '600',
                        height: 1.5,
                        textColor: Color(0xFF696969)),
                  )),
            ));
          });
        });
  }

  addMaterialPopup(BuildContext context, NewarcMaterial materialRow) async {
    contIndexPlace.text = materialRow.indexPlace!.toString();
    contProductName.text = materialRow.productName!;
    contSupplierName.text = materialRow.supplierName!;
    contGroup.text = materialRow.group!;
    contTransportType.text = materialRow.transportType!;
    contUnit.text = materialRow.measureUnit!;
    contQuantity.text = materialRow.quantity.toString();
    contPrice.text = materialRow.price.toString();
    contCost.text = materialRow.cost.toString();
    isAccount = materialRow.isAccount!;
    contAccount.text = materialRow.account.toString();
    progressMessage = '';

    return showDialog(
        context: context,
        builder: (BuildContext _bc1) {
          return StatefulBuilder(
              builder: (BuildContext _bc2, StateSetter setState) {
            return Center(
                child: BaseNewarcPopup(
              title: 'Aggiungi Materiale',
              buttonText: 'Aggiungi',
              onPressed: () async {
                try {
                  setState(() {
                    progressMessage = 'Salvataggio in corso...';
                  });

                  int processIndex = int.parse(contIndexPlace.text);
                  bool isEditMode = false;
                  if (processIndex >= 0) {
                    isEditMode = true;
                  }

                  NewarcMaterial newarcMaterial = NewarcMaterial({
                    'productName': contProductName.text,
                    'supplierName': contSupplierName.text,
                    'group': contGroup.text,
                    'transportType': contTransportType.text,
                    'measureUnit': contUnit.text,
                    'quantity': double.parse(contQuantity.text),
                    'price': double.parse(contPrice.text),
                    'cost': double.parse(contCost.text),
                    'isAccount': isAccount,
                    'isPaid': materialRow.isPaid ?? false,
                    'paidOn': 0,
                    'account': contAccount.text == '' ? 0 : double.parse(contAccount.text),
                    'status': '',
                    'quality': '',
                    'deposit': '',
                    'accountPaidOn': 0,
                    'isAccountPaid': false,
                    "uniqueId": (materialRow.uniqueId?.isEmpty ?? false) ? generateRandomString(20) : materialRow.uniqueId,
                    "paidAmount": (materialRow.paidAmount == null || materialRow.paidAmount == 0) ? 0 : materialRow.paidAmount,
                    "invoicePath": (materialRow.invoicePath != null && (materialRow.invoicePath?.isNotEmpty ?? false)) ? materialRow.invoicePath : {},
                  });

                  log("newarcMaterial ===> ${newarcMaterial.toMap()}");

                  if (newarcMaterial.account! > newarcMaterial.cost!) {
                    setState(() {
                      progressMessage = 'Error: Acconto can not be more than Costo.';
                    });
                    return;
                  }

                  if (!isEditMode) {
                    newarcMaterial.indexPlace = widget.project!.newarcMaterial!.length;
                    widget.project!.newarcMaterial!.add(newarcMaterial);
                  } else {
                    newarcMaterial.indexPlace = processIndex;
                    widget.project!.newarcMaterial![processIndex] = newarcMaterial;
                  }

                  final FirebaseFirestore _db = FirebaseFirestore.instance;

                  await _db
                      .collection(appConfig.COLLECT_NEWARC_PROJECTS)
                      .doc(widget.project!.id)
                      .update(widget.project!.toMap());

                  widget.updateProject!(widget.project);

                  setState(() {
                    progressMessage = 'Saved!';
                  });

                  setInitialValues();
                } catch (e) {
                  log("Error in onPressed Save Material: ${e.toString()}");
                  setState(() {
                    progressMessage = 'Error during save.';
                  });
                }
              },
              column: Container(
                height: 500,
                width: 650,
                child: ListView(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CustomTextFormField(
                          label: "Nome Materiale",
                          controller: contProductName,
                          validator: (value) {
                            if (value == '') {
                              return 'Required!';
                            }

                            return null;
                          },
                        ),
                        SizedBox(width: 7),
                        CustomTextFormField(
                          label: "Fornitore",
                          controller: contSupplierName,
                          validator: (value) {
                            if (value == '') {
                              return 'Required!';
                            }

                            return null;
                          },
                        ),
                      ],
                    ),
                    SizedBox(height: 20),
                    Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          flex: 1,
                          child: Column(
                            // mainAxisSize: MainAxisSize.max,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              NarFormLabelWidget(
                                label: "Gruppo",
                                textColor: Color(0xff696969),
                                fontSize: 14,
                                fontWeight: '600',
                              ),
                              SizedBox(height: 4),
                              NarSelectBoxWidget(
                                options: groups,
                                controller: contGroup,
                                validationType: 'required',
                                parametersValidate: 'Required!',
                                onChanged: (value) {},
                              ),
                            ],
                          ),
                        ),
                        SizedBox(width: 7),
                        Expanded(
                          flex: 1,
                          child: Column(
                            // mainAxisSize: MainAxisSize.max,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              NarFormLabelWidget(
                                label: "Consegna/Ritiro",
                                textColor: Color(0xff696969),
                                fontSize: 14,
                                fontWeight: '600',
                              ),
                              SizedBox(height: 4),
                              NarSelectBoxWidget(
                                options: ['Consegna', 'Ritiro'],
                                controller: contTransportType,
                                validationType: 'required',
                                parametersValidate: 'Required!',
                                onChanged: (value) {},
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 20),
                    Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          flex: 1,
                          child: Column(
                            // mainAxisSize: MainAxisSize.max,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              NarFormLabelWidget(
                                label: "Unità di misura",
                                textColor: Color(0xff696969),
                                fontSize: 14,
                                fontWeight: '600',
                              ),
                              SizedBox(height: 4),
                              NarSelectBoxWidget(
                                options: ['mq', 'pz'],
                                controller: contUnit,
                                validationType: 'required',
                                parametersValidate: 'Required!',
                                onChanged: (value) {},
                              ),
                            ],
                          ),
                        ),
                        SizedBox(width: 7),
                        CustomTextFormField(
                          label: "Quantità",
                          controller: contQuantity,
                          validator: (value) {
                            if (value == '') {
                              return 'Required!';
                            }

                            return null;
                          },
                        ),
                      ],
                    ),
                    SizedBox(height: 20),
                    Row(children: [
                      NarFormLabelWidget(
                        label: "Pagamenti",
                        fontWeight: '800',
                        fontSize: 16,
                      )
                    ]),
                    SizedBox(height: 10),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        CustomTextFormField(
                          label: "Costo",
                          controller: contCost,
                          suffixIcon: Column(
                            mainAxisSize: MainAxisSize.max,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              NarFormLabelWidget(
                                label: '€',
                                fontSize: 14,
                                textColor: Color.fromRGBO(181, 181, 181, 1),
                              ),
                            ],
                          ),
                          validator: (value) {
                            if (value == '') {
                              return 'Required!';
                            }

                            if (isNumber(value) == false)
                              return 'Not a valid value!';

                            return null;
                          },
                        ),
                        SizedBox(width: 7),
                        Row(
                          children: [
                            Padding(
                              // padding: const EdgeInsets.only( top: 6.0, bottom: 18, left: 0),
                              padding: const EdgeInsets.all(0),
                              child: Switch(
                                // This bool value toggles the switch.
                                value: isAccount,
                                activeColor: Theme.of(context).primaryColor,

                                onChanged: (bool value) async {
                                  setState(() {
                                    isAccount = value;
                                    contAccount.text = '';
                                  });
                                },
                              ),
                            ),
                            SizedBox(
                              width: 5,
                            ),
                            NarFormLabelWidget(
                                label: 'Acconto',
                                fontSize: 14,
                                textColor: Colors.black),
                          ],
                        ),
                        SizedBox(width: 14),
                        CustomTextFormField(
                          label: "Acconto",
                          controller: contAccount,
                          enabled: isAccount,
                          suffixIcon: Column(
                            mainAxisSize: MainAxisSize.max,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              NarFormLabelWidget(
                                label: '€',
                                fontSize: 14,
                                textColor: Color.fromRGBO(181, 181, 181, 1),
                              ),
                            ],
                          ),
                          validator: (value) {
                            if (value != '' && isNumber(value) == false)
                              return 'Not a valid value!';

                            return null;
                          },
                        ),
                        SizedBox(width: 7),
                        Expanded(
                            child: SizedBox(
                              width: 0,
                            ),
                            flex: 2)
                      ],
                    ),

                    Row(
                      children: [
                        NarFormLabelWidget(label: progressMessage)
                      ],
                    )
                  ],
                ),
              ),
            ));
          });
        });
  }
}
