import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/services.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/app_const.dart' as appConst;
import 'package:newarc_platform/classes/renovationQuotation.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/utils/downloadQuotationPDF.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/link.dart';
import 'package:newarc_platform/widget/UI/multi-select-dropdown.dart';
import 'package:newarc_platform/widget/UI/select-box.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import 'package:newarc_platform/activitiesForEstimate.dart' as aef;

import '../UI/resizable_note_text_field.dart';


class RenovationQuotationSingle extends StatefulWidget {
  final RenovationQuotation? renovationQuotation;
  final Function? updateViewCallback;

  const RenovationQuotationSingle(
      {Key? key, this.renovationQuotation, this.updateViewCallback})
      : super(key: key);

  @override
  State<RenovationQuotationSingle> createState() =>
      _RenovationQuotationSingleState();
}

class _RenovationQuotationSingleState extends State<RenovationQuotationSingle> {
  RenovationQuotation? initRenovationQuotation;
  bool changedDetected = false;
  bool _loading = false;


  NumberFormat localCurrencyFormatMain = NumberFormat.currency(locale: 'it_IT', symbol: '', decimalDigits: 2);

  List<String> unitOfMeasurement = [
    'AC',
    'GI',
    'MQ',
    'ML',
    'LT',
    'NR',
    'MC',
    'T',
    'H',
    'KG',
    '%'
  ];

  List<String> priceLevel = ['F1', 'F2', 'F3', 'Manuale'];

  List<String> categories = [];
  List<String> subCategories = [];
  List activities = [];

  List<String> paymentMode = ['Da definire'];

  List<String> constructionDuration = [
    'Da definire',
    '30 giorni lavorativi',
    '45 giorni lavorativi',
    '60 giorni lavorativi',
    '75 giorni lavorativi',
    '90 giorni lavorativi'
  ];

  List<String> discountTypes = [
    'Percentuale',
    'Fisso'
  ];

  List<String> discounts = [
    '0%',
    '5%',
    '10%',
    '15%',
    '20%'
  ];

  bool addManualActivity = false;

  TextEditingController? contNotes = new TextEditingController();
  TextEditingController? contCategory = new TextEditingController();
  TextEditingController? contSubCategory = new TextEditingController();
  TextEditingController? contActivity = new TextEditingController();
  TextEditingController? contManualActivity = new TextEditingController();
  TextEditingController? contUM = new TextEditingController();
  TextEditingController? contQuantity = new TextEditingController();
  TextEditingController? contUnitCost = new TextEditingController();
  TextEditingController? contTotal = new TextEditingController();
  TextEditingController? contDescription = new TextEditingController();
  TextEditingController? contPriceLevel = new TextEditingController();
  TextEditingController? contComment = new TextEditingController();

  TextEditingController? contConstructionDuration = new TextEditingController();
  TextEditingController? contPaymentMode = new TextEditingController();

  TextEditingController? contDiscountType = new TextEditingController();
  TextEditingController? contDiscount = new TextEditingController();

  Map<String, Map<String, List<Map>>> test = {};


  Map<String, Map<String, List<Map>>> renovationData = {};
  Map<String, Map<String, List<Map>>> initialRenovationData = {};
  List<String> categoryOrder = [];
  Map<String, TextEditingController> categoryControllers = {};

  Map categoryTotal = {};
  Map categoryTotalForOnlyToShow = {};
  Map<String, Map> subCategoryTotal = {};
  Map<String, Map> subCategoryTotalForOnlyToShow = {};
  List<String> formMessages = [''];
  GlobalKey<FormState> _formKey = new GlobalKey<FormState>();

  Map<String, bool> expandedCategories = {};

  List selectedActivities = [];

  double grandTotal = 0;

  void toggleCategory(String category) {
    setState(() {
      expandedCategories[category] = !(expandedCategories[category] ?? false);
    });
  }

  @override
  void initState() {
    super.initState();

    paymentMode.addAll(appConst.installmentList
        .map((item) => item.replaceAll(' - ', '-'))
        .toList());

    categories.addAll(aef.activitiesForEstimate.keys);

    initRenovationQuotation = widget.renovationQuotation;

    contConstructionDuration!.text =
        widget.renovationQuotation!.constructionDuration != null
            ? widget.renovationQuotation!.constructionDuration!
            : '';
    contPaymentMode!.text = widget.renovationQuotation!.paymentMode != null
        ? widget.renovationQuotation!.paymentMode!
        : '';
    
    contDiscountType!.text = widget.renovationQuotation!.discountType == null ? 'Percentuale' : 
                              widget.renovationQuotation!.discountType == "" ? 'Percentuale' :
                              widget.renovationQuotation!.discountType == 'Percentage' ? 'Percentuale' : 
                              widget.renovationQuotation!.discountType == 'Percentuale' ? 'Percentuale' : 'Fisso';


    contDiscount!.text = widget.renovationQuotation!.discountType == "Fisso" ? (widget.renovationQuotation?.discount?.isNotEmpty ?? false ? localCurrencyFormatMain.format(double.tryParse(widget.renovationQuotation?.discount ?? "")) : "") : widget.renovationQuotation?.discount ?? '0%';


    if (widget.renovationQuotation!.renovationActivity != null) {
      for (var i = 0;
          i < widget.renovationQuotation!.renovationActivity!.length;
          i++) {
        RenovationActivityCategory tmpRenovationActivityCategory =
            widget.renovationQuotation!.renovationActivity![i];

        if (!renovationData.containsKey(tmpRenovationActivityCategory.category)) {
          
          renovationData[tmpRenovationActivityCategory.category!] = {};

        }

        for (var j = 0;
            j < tmpRenovationActivityCategory.activity!.length;
            j++) {


          Map rowData = {
            'index': tmpRenovationActivityCategory.activity![j].index,
            'title': tmpRenovationActivityCategory.activity![j].title,
            'measurementUnit':
                tmpRenovationActivityCategory.activity![j].measurementUnit,
            'quantity': tmpRenovationActivityCategory.activity![j].quantity,
            'unitPrice': localCurrencyFormatMain.format(tmpRenovationActivityCategory.activity![j].unitPrice ?? 0.0),
            'description':
                tmpRenovationActivityCategory.activity![j].description,
            'priceLevel': tmpRenovationActivityCategory.activity![j].priceLevel,
            'subCategory':
                tmpRenovationActivityCategory.activity![j].subCategory,
            'code': tmpRenovationActivityCategory.activity![j].code,
            'comment': tmpRenovationActivityCategory.activity![j].comment,
            'isDiscounted': tmpRenovationActivityCategory.activity![j].isDiscounted,
            'isManualActivity': tmpRenovationActivityCategory.activity![j].isManualActivity,
          };


          setDataRow(tmpRenovationActivityCategory.category!, rowData);
        }
      }
    }

    if( renovationData.containsKey('N - Newarc') ) {
      renovationData = {
        'N - Newarc': renovationData['N - Newarc']!,
        ...renovationData..remove('N - Newarc'),
      };
    }
    

    contConstructionDuration!.addListener(_checkForChanges);
    contPaymentMode!.addListener(_checkForChanges);
    contCategory!.addListener(_checkForChanges);
    contSubCategory!.addListener(_checkForChanges);
    contActivity!.addListener(_checkForChanges);
    contManualActivity!.addListener(_checkForChanges);
    contUM!.addListener(_checkForChanges);
    contQuantity!.addListener(_checkForChanges);
    contPriceLevel!.addListener(_checkForChanges);
    contUnitCost!.addListener(_checkForChanges);
    contTotal!.addListener(_checkForChanges);
    contDescription!.addListener(_checkForChanges);
    contDiscountType!.addListener(_checkForChanges);
    contDiscount!.addListener(_checkForChanges);

    initialRenovationData = renovationData;

    subCategoryTotalCalc();

    setState(() {});
  }

  @override
  void dispose() {
    contConstructionDuration!.removeListener(_checkForChanges);
    contPaymentMode!.removeListener(_checkForChanges);
    contCategory!.removeListener(_checkForChanges);
    contSubCategory!.removeListener(_checkForChanges);
    contActivity!.removeListener(_checkForChanges);
    contManualActivity!.removeListener(_checkForChanges);
    contUM!.removeListener(_checkForChanges);
    contQuantity!.removeListener(_checkForChanges);
    contPriceLevel!.removeListener(_checkForChanges);
    contUnitCost!.removeListener(_checkForChanges);
    contTotal!.removeListener(_checkForChanges);
    contDescription!.removeListener(_checkForChanges);
    contDiscountType!.removeListener(_checkForChanges);
    contDiscount!.removeListener(_checkForChanges);

    super.dispose();
  }

  @protected
  void didUpdateWidget(RenovationQuotationSingle oldWidget) {
    super.didUpdateWidget(oldWidget);

    // setInitialValues();
  }

  setSubCategories() {
    subCategories.clear();
    activities.clear();
    String selectedCategory = contCategory!.text;
    subCategories.addAll(aef.activitiesForEstimate[selectedCategory]!.keys);
  }

  subCategoryTotalCalc(){

    subCategoryTotal.clear();
    subCategoryTotalForOnlyToShow.clear();
    categoryTotalForOnlyToShow.clear();
    categoryTotal.clear();
    grandTotal = 0;

    categoryOrder.clear();

    categoryOrder = renovationData.keys.toList();

    Map<String, bool> previousExpandedCategories = Map.from(expandedCategories);

    renovationData.keys.map((category){

      //expandedCategories[category] = true;
      expandedCategories[category] = previousExpandedCategories[category] ?? true;

      categoryControllers[category] = TextEditingController();

      if (!categoryTotal.containsKey(category)) {
        categoryTotal[category] = 0;
      }
      double catTotal = 0;
      double catTotalForPdf = 0;
      bool isCategoryDiscounted = false;
      renovationData[category]!.keys.map((subCategory) {
          renovationData[ category]![subCategory]! .map((entry) {

              if( !subCategoryTotal.containsKey(category) ){
                subCategoryTotal[category] = {};
              }

              if (!subCategoryTotal[category]!.containsKey(subCategory)) {
                subCategoryTotal[category]![subCategory] = 0;
              }

              if (!subCategoryTotalForOnlyToShow.containsKey(category)) {
                subCategoryTotalForOnlyToShow[category] = {};
              }

              if (!subCategoryTotalForOnlyToShow[category]!.containsKey(subCategory)) {
                subCategoryTotalForOnlyToShow[category]![subCategory] = {
                  "subCategoryTotal": 0,
                  "isDiscounted": false,
                };
              }

              bool isSubCategoryDiscounted = false;

              if( !entry['isDiscounted'] ) {

                double qty = (double.tryParse( entry['quantity'].toString()) ?? 0.0);

                double price = (double.tryParse(entry['unitPrice'].toString().replaceAll(".", "").replaceAll(",", ".")) ?? 0.0);

                double total = qty * price;

                subCategoryTotal[category]![subCategory] += total;
                if (subCategoryTotalForOnlyToShow.containsKey(category)) {
                  subCategoryTotalForOnlyToShow[category]![subCategory]!["subCategoryTotal"] += total;
                }
                catTotal += total;
                catTotalForPdf += total;
                grandTotal += total;

             }else{
                double qty = (double.tryParse( entry['quantity'].toString()) ?? 0.0);
                double price = (double.tryParse(entry['unitPrice'].toString().replaceAll(".", "").replaceAll(",", ".")) ?? 0.0);

                double total = qty * price;

                catTotalForPdf += total;
                if (subCategoryTotalForOnlyToShow.containsKey(category)) {
                  subCategoryTotalForOnlyToShow[category]![subCategory]!["subCategoryTotal"] += total;
                }
                isSubCategoryDiscounted = true;
              }

              //  If any entry in this subcategory was discounted, mark the whole subcategory as discounted
              if (isSubCategoryDiscounted) {
                subCategoryTotalForOnlyToShow[category]![subCategory]!["isDiscounted"] = true;
                isCategoryDiscounted = true; // The entire category should also be marked discounted
              }
          }).toList();
      }).toList();

      categoryTotal[category] = catTotal;

      double catDiscountedAmount = catTotal;
      if((widget.renovationQuotation?.discount?.isNotEmpty ?? false) && widget.renovationQuotation!.discount!.contains("%")){
        double wholeQuotationDiscountDouble = double.tryParse(widget.renovationQuotation!.discount!.replaceAll("%", "")) ?? 0.0;
        double catValueWithDiscount = catTotal * wholeQuotationDiscountDouble / 100;
        catDiscountedAmount = catTotal - catValueWithDiscount;
      }

      categoryTotalForOnlyToShow[category] = {
        "catTotal":catTotal,
        "catTotalForPdf":catTotalForPdf,
        "isDiscounted": isCategoryDiscounted,
        "catDiscountedAmount": catDiscountedAmount,
      };

    }).toList();
  }

  setActivities() {
    activities.clear();
    String selectedSubCategory = contSubCategory!.text;
    String selectedCategory = contCategory!.text;

    activities.addAll(aef
        .activitiesForEstimate[selectedCategory]![selectedSubCategory]!
        .map((value) =>
            {'label': value['activity'], 'value': value['activity']}));

    dynamic act = activities.first;
    contActivity!.text = act['value'];
    Map activityData = aef
        .activitiesForEstimate[selectedCategory]![selectedSubCategory]!
        .firstWhere((e) => e['activity'] == activities.first, orElse: () => {'um': ''});

    contUM!.text = activityData['um'];

    setState(() {});
  }

  _checkForChanges() {
    changedDetected = true;
  }

  setDataRow(String category, Map data) {
    try {

      String subCategory = data['subCategory'];
      if (!renovationData.containsKey(category)) {
        renovationData[category] = {};
      }

      if (!renovationData[category]!.containsKey(subCategory)) {
        renovationData[category]![subCategory] = [];
      }

      // if (!categoryTotal.containsKey(category)) {
      //   categoryTotal[category] = 0;
      // }

      int _index = -1;

      if (data.containsKey('index')) {
        _index = data['index'];
      } else {
        _index = renovationData[category]![subCategory]!.indexWhere((e) {
          return e['title'] == data['title'];
        });
      }


      double total = double.tryParse(data['quantity'].toString())! *
          double.tryParse(data['unitPrice'].toString().replaceAll(".", "").replaceAll(",", "."))!;


      data['index'] = renovationData[category]![subCategory]!.length;

      data['total'] = total;
      data['contUM'] = new TextEditingController();
      data['contQuantity'] = new TextEditingController();
      data['contPriceLevel'] = new TextEditingController();
      data['contUnitPrice'] = new TextEditingController();
      data['contTotal'] = new TextEditingController();
      data['contComment'] = new TextEditingController();


      data['contUM'].text = data['measurementUnit'].toString();
      data['contQuantity'].text = data['quantity'].toString();
      data['contUnitPrice'].text = data['unitPrice'].trim();
      data['contPriceLevel'].text = data['priceLevel'].toString();
      data['contComment'].text = data['comment'].toString();
      data['comment'] = data['comment'].toString();
      data['contTotal'].text = localCurrencyFormatMain.format(total).trim();
      data['showComment'] = data['comment'] != '' ? true : false;
      data['isManualActivity'] = data['isManualActivity']??false;

      if (_index > -1) {
        data['index'] = _index;

        if (renovationData[category]![subCategory]!.length >= (_index + 1)) {
          renovationData[category]![subCategory]![_index] = data;
        } else {
          renovationData[category]![subCategory]!.add(data);
        }
      } else {
        renovationData[category]![subCategory]!.add(data);
      }

      data['contUM'].addListener(_checkForChanges);
      data['contQuantity'].addListener(_checkForChanges);
      data['contPriceLevel'].addListener(_checkForChanges);
      data['contUnitPrice'].addListener(_checkForChanges);
      data['contTotal'].addListener(_checkForChanges);
      data['contComment'].addListener(_checkForChanges);

    } catch (e, s) {
      print({e, s});
    }

    subCategoryTotalCalc();
    
  }

  setInitialValues() {
    setState(() {});
  }

  Widget headerRow(Map rowData) {
    return Container(
      // padding: const EdgeInsets.all(8.0),
      padding: const EdgeInsets.only(left: 0, right: 15, top: 3, bottom: 3),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            flex: 6,
            child: NarFormLabelWidget(
              label: rowData['code'],
              fontSize: 10,
              textColor: Color(0xff717171),
            ),
          ),
          SizedBox(
            width: 20,
          ),
          Expanded(
            flex: 1,
            child: NarFormLabelWidget(
              label: 'U.M.',
              fontSize: 10,
              textColor: Color(0xff717171),
            ),
          ),
          SizedBox(
            width: 5,
          ),
          Expanded(
            flex: 1,
            child: NarFormLabelWidget(
              label: 'Quantità',
              fontSize: 10,
              textColor: Color(0xff717171),
            ),
          ),
          SizedBox(
            width: 5,
          ),
          rowData['isManualActivity']
          ? Container()
          : Expanded(
            flex: 1,
            child: NarFormLabelWidget(
              label: 'F. prezzo',
              fontSize: 10,
              textColor: Color(0xff717171),
            ),
          ),
          SizedBox(
            width: 5,
          ),
          Expanded(
            flex: 2,
            child: NarFormLabelWidget(
              label: 'Unitario',
              fontSize: 10,
              textColor: Color(0xff717171),
            ),
          ),
          SizedBox(
            width: 5,
          ),
          Expanded(
            flex: 2,
            child: NarFormLabelWidget(
              label: 'Costo',
              fontSize: 10,
              textColor: Color(0xff717171),
            ),
          ),
        ],
      ),
    );
  }

  sortData() {
    renovationData.forEach((category, categoryData) {
      renovationData[category]!.forEach((subCategory, subCategoryData) {
        renovationData[category]![subCategory]!
            .sort((a, b) => a['index'].compareTo(b['index']));
      });
    });
  }

  Widget dataRow(String category,Map rowData) {
    String subCategory = rowData['subCategory'];

    int rowIndex = renovationData[category]![subCategory]!.indexWhere((e) {
      return e['index'] == rowData['index'];
    });

    renovationData[category]![subCategory]![rowIndex]['contUM'].text =
        renovationData[category]![subCategory]![rowIndex]['contUM']
            .text
            .toString()
            .toUpperCase();

    Map activityData =
        aef.activitiesForEstimate[category]![subCategory]!.firstWhere(
      (e) => e['activity'] == rowData['title'],
      orElse: () => {},
    );

    bool manual = false;
    // if (activityData.isEmpty) {
    //   manual = true;
    // }

    if( renovationData[category]![subCategory]![rowIndex] ['contPriceLevel'].text == 'Manuale' ) {
      // renovationData[category]![subCategory]![rowIndex]['contUnitPrice'].text = '0';
      manual = true;  
      
    }

    return Container(
      decoration: BoxDecoration(
        color: renovationData[category]![subCategory]![rowIndex]['isDiscounted'] ? Color(0xffDBF0E7) : Color(0xffF4F4F4),
        borderRadius: BorderRadius.circular(10),
      ),
      margin: EdgeInsets.only(bottom: 10),
      padding: const EdgeInsets.only(left: 15, right: 15, top: 5, bottom: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Column(
            children: [
              headerRow(renovationData[category]![subCategory]![rowIndex]),
              SizedBox(
                height: 1,
              ),
              Row(
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    flex: 6,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Tooltip(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(7),
                            color: Colors.white,
                            border: Border.all(color: Color(0xffE3E3E3), width: 1 )

                          ),
                          richMessage: WidgetSpan(
                            alignment: PlaceholderAlignment.baseline,
                            baseline: TextBaseline.alphabetic,
                            child: Container(
                              // padding: EdgeInsets.all(10),
                              constraints: const BoxConstraints(maxWidth: 380),
                              child: NarFormLabelWidget(
                                label: renovationData[category]![subCategory]![rowIndex]['title'],
                                fontSize: 11,
                                textColor: Colors.black,
                                overflow: TextOverflow.visible,
                                fontWeight: '600',
                                height: 1.5,
                              ),
                            )
                          ),
                          height: 50,
                          padding: const EdgeInsets.all(15),
                          preferBelow: true,
                          // textStyle: const TextStyle(
                          //   fontSize: 11,
                          // ),
                          showDuration: const Duration(seconds: 2),
                          waitDuration: const Duration(seconds: 1),
                          child: NarFormLabelWidget(
                            label: renovationData[category]![subCategory]![rowIndex]['title'],
                            fontSize: 12,
                            textColor: Colors.black,
                          )
                        ),
                        SizedBox(
                          height: 5,
                        ),

                        Row(
                          children: [
                            if( rowData['isManualActivity'] ) Container(
                              margin: EdgeInsets.only(right:10),
                              child: NarLinkWidget(
                                  fontSize: 12,
                                  //fontWeight: 'bold',
                                  text: 'Modifica',
                                  textDecoration: TextDecoration.underline,
                                  onClick: () {
                                    addVocePopup(category: category,quotationData: renovationData[category]![subCategory]![rowIndex]);
                                  },
                                ),
                            ),
                            if( !rowData['showComment'] ) NarLinkWidget(
                                fontSize: 12,
                                //fontWeight: 'bold',
                                text: 'Aggiungi nota',
                                textDecoration: TextDecoration.underline,
                                onClick: () {
                                  setState(() {
                                    renovationData[category]![subCategory]![
                                        rowIndex]['showComment'] = true;
                                  });
                                },
                              ),

                          ],
                        ),

                        SizedBox(
                          height: 5,
                        ),
                        rowData['showComment'] == true
                            ? Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  ResizableNoteField(
                                    controllerFontSize: 12,
                                    controller: renovationData[category]![
                                    subCategory]![rowIndex]
                                    ['contComment'],
                                    label: "",
                                    onChanged: (value) {
                                      renovationData[category]![
                                      subCategory]![rowIndex]
                                      ['comment'] = value;
                                    },
                                  ),
                                  SizedBox(
                                    height: 3,
                                  ),
                                  NarLinkWidget(
                                    fontSize: 10,
                                    fontWeight: 'bold',
                                    text: 'Elimina nota',
                                    textDecoration: TextDecoration.underline,
                                    onClick: () {
                                      setState(() {
                                        renovationData[category]![subCategory]![
                                                rowIndex]['contComment']
                                            .text = '';
                                        renovationData[category]![subCategory]![
                                            rowIndex]['comment'] = '';
                                        renovationData[category]![subCategory]![
                                            rowIndex]['showComment'] = false;
                                      });
                                    },
                                  ),
                                ],
                              )
                            : Container()
                      ],
                    ),
                  ),
                  SizedBox(
                    width: 5,
                  ),
                  Expanded(
                      flex: 1,
                      child: NarSelectBoxWidget(
                          options: unitOfMeasurement,
                          controller: renovationData[category]![subCategory]![rowIndex] ['contUM'],
                          contentPadding: EdgeInsets.only(
                              top: 12, bottom: 12, right: 2, left: 10),
                          onChanged: (value) {
                            renovationData[category]![subCategory]![rowIndex]
                                ['measurementUnit'] = renovationData[category]![
                                    subCategory]![rowIndex]['contUM']
                                .text;
                          })),

                  SizedBox(
                    width: 5,
                  ),

                  /*renovationData[category]![rowIndex]['contUM'].text == 'Pz'
                  ? Expanded(
                    flex: 2,
                    child: NarSelectBoxWidget (
                      options: List<String>.generate(20, (i) => (i + 1).toString()),
                      controller: renovationData[category]![rowIndex]['contQuantity'],
                      onChanged: (){

                        renovationData[category]![rowIndex]['quantity'] = renovationData[category]![rowIndex]['contQuantity'].text;
                        if( isNumber(renovationData[category]![rowIndex]['contUnitPrice'].text)
                        && isNumber(renovationData[category]![rowIndex]['contQuantity'].text)
                        &&  renovationData[category]![rowIndex]['contUnitPrice'].text != ''
                        && renovationData[category]![rowIndex]['contQuantity'].text != '' ) {

                          renovationData[category]![rowIndex]['contTotal'].text = ( double.tryParse(renovationData[category]![rowIndex]['contUnitPrice'].text)! * double.tryParse(renovationData[category]![rowIndex]['contQuantity'].text)! ).toString();

                          renovationData[category]![rowIndex]['total'] = renovationData[category]![rowIndex]['contTotal'].text;
                        }
                        double total = 0;
                        renovationData[category]!.forEach((value){
                          total += double.tryParse( value['total'].toString() )!;
                        });
                        categoryTotal[category] = total;

                        setState(() {});
                      },
                      validationType: 'required',
                      parametersValidate: 'Required!',
                      contentPadding: EdgeInsets.only(top: 15, bottom: 17, left: 21.0, right: 8.0),
                    ),
                  )
                  : */
                  CustomTextFormField(
                    flex: 1,
                    suffixIcon: null,
                    label: '',
                    contentPadding: EdgeInsets.only(top: 12, bottom: 12, right: 2,left: 10),
                    // enabled: false,
                    controller: renovationData[category]![subCategory]![rowIndex]['contQuantity'],
                    onChangedCallback: (value) {
                      renovationData[category]![subCategory]![rowIndex]['quantity'] = value;

                      if (isNumber(double.tryParse(renovationData[category]![subCategory]![rowIndex]['contUnitPrice'].text.toString().replaceAll(".", "").replaceAll(",", "."))) &&
                          isNumber(double.tryParse(renovationData[category]![subCategory]![rowIndex]['contQuantity'].text)) &&
                          renovationData[category]![subCategory]![rowIndex]['contUnitPrice'].text != '' &&
                          renovationData[category]![subCategory]![rowIndex]['contQuantity'].text != '') {
                        double unitPrice = double.tryParse(renovationData[category]![subCategory]![rowIndex]['contUnitPrice'].text.replaceAll(".", "").replaceAll(",", ".")) ?? 0.0;
                        double parsedQty = double.tryParse(value.toString()) ?? 0.0;
                        double totalPrice = (unitPrice * parsedQty);
                        renovationData[category]![subCategory]![rowIndex]['contTotal'].text = localCurrencyFormatMain.format(totalPrice);

                        renovationData[category]![subCategory]![rowIndex]['total'] = totalPrice;
                      } else if (renovationData[category]![subCategory]![rowIndex]['contQuantity'].text == '') {
                        renovationData[category]![subCategory]![rowIndex]
                            ['total'] = 0;
                      }

                      subCategoryTotalCalc();

                      setState(() {});
                    },
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'[0-9,.]')),
                    ],
                  ),
                  SizedBox(
                    width: 5,
                  ),
                  if( !rowData['isManualActivity'] ) Expanded(
                    flex: 1,
                    child:
                    NarSelectBoxWidget(
                      options: priceLevel,
                      controller: renovationData[category]![subCategory]![rowIndex]['contPriceLevel'],
                      onChanged: (value) {

                        renovationData[category]![subCategory]![rowIndex] ['priceLevel'] = renovationData[category]![subCategory]![rowIndex] ['contPriceLevel'].text;

                        if( renovationData[category]![subCategory]![rowIndex] ['contPriceLevel'].text == 'Manuale' ) {
                          renovationData[category]![subCategory]![rowIndex]['contUnitPrice'].text = '0';

                          manual = true;
                          setState(() {});
                          subCategoryTotalCalc();
                          return;
                        }

                        Map activityPrices = aef
                            .activitiesForEstimate[category]![subCategory]!
                            .firstWhere((value) =>
                                value['activity'] == rowData['title']);

                        final unitPrice = activityPrices[renovationData[
                                        category]![subCategory]![rowIndex]
                                    ['priceLevel']
                                .toLowerCase()]
                            .toString();

                        renovationData[category]![subCategory]![rowIndex]['unitPrice'] = localCurrencyFormatMain.format(double.tryParse(unitPrice)).trim();
                        renovationData[category]![subCategory]![rowIndex]['contUnitPrice'].text = localCurrencyFormatMain.format(double.tryParse(unitPrice)).trim();

                        if (isNumber(double.tryParse(renovationData[category]![subCategory]![rowIndex]['contUnitPrice'].text.toString().replaceAll(".", "").replaceAll(",", "."))) &&
                            isNumber(renovationData[category]![subCategory]![rowIndex]['contQuantity'].text) &&
                            renovationData[category]![subCategory]![rowIndex]['contUnitPrice'].text != '' &&
                            renovationData[category]![subCategory]![rowIndex]['contQuantity'].text != '') {
                          renovationData[category]![subCategory]![rowIndex]['contTotal'].text = localCurrencyFormatMain.format((double.tryParse(renovationData[category]![subCategory]![rowIndex]['contUnitPrice'].text.toString().replaceAll(".", "").replaceAll(",", "."))! *
                                  double.tryParse(renovationData[category]![subCategory]![rowIndex]['contQuantity'].text)!));
                          renovationData[category]![subCategory]![rowIndex]['total'] = double.tryParse(renovationData[category]![subCategory]![rowIndex]['contUnitPrice'].text.toString().replaceAll(".", "").replaceAll(",", "."))! *
                                  double.tryParse(renovationData[category]![subCategory]![rowIndex]['contQuantity'].text)!;
                        }
                        subCategoryTotalCalc();

                        setState(() {
                          manual = true;
                        });
                      },
                      validationType: 'required',
                      parametersValidate: 'Required!',
                      contentPadding: EdgeInsets.only(
                          top: 12, bottom: 12, right: 2, left: 10),
                    ),
                  ),
                  SizedBox(
                    width: 5,
                  ),
                  CustomTextFormField(
                      flex: 2,
                      label: '',
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp(r'[0-9,.]')),
                      ],
                      isMoney: true,
                      isShowPrefillMoneyIcon: false,
                      controller: renovationData[category]![subCategory]![rowIndex]['contUnitPrice'],
                      contentPadding: EdgeInsets.only(
                          top: 12, bottom: 12, right: 2, left: 10),
                      enabled: manual || rowData['isManualActivity'],
                      onChangedCallback: (value) {
                        final unitPrice = renovationData[category]![subCategory]![rowIndex]['contUnitPrice'].text;
                        renovationData[category]![subCategory]![rowIndex]['unitPrice'] = unitPrice;

                        if (isNumber(double.tryParse(renovationData[category]![subCategory]![rowIndex]['contUnitPrice'].text.toString().replaceAll(".", "").replaceAll(",", "."))) &&
                            isNumber(double.tryParse(renovationData[category]![subCategory]![rowIndex]['contQuantity'].text)) &&
                            renovationData[category]![subCategory]![rowIndex]['contUnitPrice'].text != '' &&
                            renovationData[category]![subCategory]![rowIndex]['contQuantity'].text != '') {
                          double qty = double.tryParse(renovationData[category]![subCategory]![rowIndex]['contQuantity'].text) ?? 0.0;
                          double parsedUnitPrice = double.tryParse(value.toString().replaceAll(".", "").replaceAll(",", ".")) ?? 0.0;
                          double totalPrice = (qty * parsedUnitPrice);

                          renovationData[category]![subCategory]![rowIndex]['contTotal'].text = localCurrencyFormatMain.format(totalPrice);

                          renovationData[category]![subCategory]![rowIndex]['total'] = totalPrice;
                        }

                        subCategoryTotalCalc();

                        setState(() {});
                      },
                      suffixIcon: Column(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              NarFormLabelWidget(
                                label: '€',
                                fontSize: 14,
                                textColor: Colors.black,
                              ),
                              SizedBox(
                                width: 5,
                              )
                            ],
                          ),
                        ],
                      )),
                  SizedBox(
                    width: 5,
                  ),
                  CustomTextFormField(
                    flex: 2,
                    label: '',
                    enabled: false,
                    isMoney: true,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'[0-9,.]')),
                    ],
                    controller: renovationData[category]![subCategory]![rowIndex]['contTotal'],
                    contentPadding: EdgeInsets.only(
                        top: 12, bottom: 12, right: 2, left: 10),
                    suffixIcon: Column(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            NarFormLabelWidget(
                              label: '€',
                              fontSize: 14,
                              textColor: Colors.black,
                            ),
                            SizedBox(
                              width: 5,
                            )
                          ],
                        ),
                      ],
                    ),
                    isShowPrefillMoneyIcon: false,
                    // enabled: false,
                  ),
                  // SizedBox(width: 5,),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Container(
                        height: 25,
                        width: 25,
                        decoration: BoxDecoration(
                          color: Color.fromRGBO(227, 227, 227, 1),
                          borderRadius: BorderRadius.circular(7.0),
                        ),
                        child: IconButton(
                          onPressed: () {
                            if (rowIndex == 0) {
                              renovationData[category]![subCategory]![rowIndex]['index'] =
                                  renovationData[category]![subCategory]!.length - 1;
                              for (var i = 1; i < renovationData[category]![subCategory]!.length; i++) {
                                renovationData[category]![subCategory]![i]['index'] =
                                    renovationData[category]![subCategory]![i]['index'] - 1;
                              }
                            } else {
                              int _index = renovationData[category]![subCategory]![rowIndex]['index'];

                              renovationData[category]![subCategory]![rowIndex]['index'] = renovationData[category]![subCategory]![rowIndex]['index'] - 1;

                              renovationData[category]![subCategory]![rowIndex - 1]['index'] = _index;
                            }

                            sortData();
                            setState(() {});
                          },
                          style: ButtonStyle(
                              overlayColor:
                                  WidgetStateProperty.all(Colors.transparent)),
                          padding: EdgeInsets.zero,
                          icon: Transform.rotate(
                            angle: -1.5708,
                            child: SvgPicture.asset(
                              "assets/icons/cash_in.svg",
                              color: Color(0xff5b5b5b),
                            ),
                          ),
                        ),
                      ),
                      SizedBox(width: 5),
                      Container(
                        height: 25,
                        width: 25,
                        decoration: BoxDecoration(
                          color: Color.fromRGBO(227, 227, 227, 1),
                          borderRadius: BorderRadius.circular(7.0),
                        ),
                        child: IconButton(
                          onPressed: () {
                            if (rowIndex ==
                                renovationData[category]![subCategory]!.length -
                                    1) {
                              renovationData[category]![subCategory]![rowIndex]
                                  ['index'] = 0;
                              for (var i = 0;
                                  i <
                                      renovationData[category]![subCategory]!
                                              .length -
                                          1;
                                  i++) {
                                renovationData[category]![subCategory]![i]
                                        ['index'] =
                                    renovationData[category]![subCategory]![i]
                                            ['index'] +
                                        1;
                              }
                            } else {
                              int _index = renovationData[category]![
                                  subCategory]![rowIndex]['index'];

                              renovationData[category]![subCategory]![rowIndex]
                                  ['index'] = renovationData[category]![
                                      subCategory]![rowIndex]['index'] +
                                  1;

                              renovationData[category]![subCategory]![
                                  rowIndex + 1]['index'] = _index;
                            }

                            sortData();
                            setState(() {});
                          },
                          style: ButtonStyle(
                              overlayColor:
                                  WidgetStateProperty.all(Colors.transparent)),
                          padding: EdgeInsets.zero,
                          icon: Transform.rotate(
                            angle: 1.5708,
                            child: SvgPicture.asset(
                              "assets/icons/cash_in.svg",
                              color: Color(0xff5b5b5b),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(
                    width: 5,
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      NarLinkWidget(
                        text: renovationData[category]![subCategory]![rowIndex]['isDiscounted']
                        ? 'Elimina sconto'
                        : 'Sconta voce',
                        fontSize: 10,
                        fontWeight: 'bold',
                        textDecoration: TextDecoration.underline,
                        onClick: () {
                          if( renovationData[category]![subCategory]![rowIndex]['isDiscounted'] ) {
                            renovationData[category]![subCategory]![rowIndex]['isDiscounted'] = false;
                          } else {
                            renovationData[category]![subCategory]![rowIndex]['isDiscounted'] = true;
                          }
                          subCategoryTotalCalc();

                          setState(() {});
                        },
                      ),
                      SizedBox(width: 10),
                      TextButton(
                        child: Container(
                          child: Image.asset('assets/icons/trash-process.png',
                              color: Color(0xff9B9B9B), height: 20),
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.transparent,
                            borderRadius: BorderRadius.circular(7.0),
                          ),
                        ),
                        onPressed: () {
                          showDialog(
                              context: context,
                              builder: (BuildContext _bc1) {
                                return StatefulBuilder(builder:
                                    (BuildContext _bc2, StateSetter setState) {
                                  return Center(
                                      child: BaseNewarcPopup(
                                    formErrorMessage: formMessages,
                                    buttonText: 'Ok',
                                    onPressed: () async {
                                      try {
                                        categoryTotal[category] -= double.tryParse(
                                            renovationData[category]![subCategory]![
                                                    rowIndex]['total']
                                                .toString().replaceAll(".", "").replaceAll(",", "."));
                                        renovationData[category]![subCategory]!
                                            .removeAt(rowIndex);

                                        if (renovationData[category]![subCategory]!
                                                .length ==
                                            0) {
                                          renovationData[category]!.removeWhere(
                                              (e, value) => e == subCategory);
                                        }

                                        if (renovationData[category]!.length == 0) {
                                          renovationData.removeWhere(
                                              (e, value) => e == category);
                                        }
                                      } catch (e, s) {
                                        print({e, s});
                                      }
                                      subCategoryTotalCalc();
                                      setInitialValues();
                                      return true;
                                    },
                                    title: "Attenzione!",
                                    column: Container(
                                      width: 400,
                                      margin: EdgeInsets.symmetric(vertical: 30),
                                      child: Center(
                                        child: ListView(
                                          shrinkWrap: true,
                                          children: [
                                            NarFormLabelWidget(
                                              label:
                                                  "Vuoi davvero eliminare questo?",
                                              fontSize: 18,
                                              textAlign: TextAlign.center,
                                              fontWeight: '600',
                                            )
                                          ],
                                        ),
                                      ),
                                    ),
                                  ));
                                });
                              });
                        },
                        style: ButtonStyle(
                          overlayColor: WidgetStateProperty.all(Colors.transparent),
                          splashFactory:
                              NoSplash.splashFactory, // Remove the splash effect
                          padding: WidgetStateProperty.all(
                              EdgeInsets.zero), // Remove padding
                          minimumSize: WidgetStateProperty.all(
                              Size.zero), // Remove minimum size constraints
                          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          )
        ],
      ),
    );
  }

  addVocePopup({category = null, quotationData = null}) {
    selectedActivities.clear();

    if ( quotationData == null) {
      contActivity!.text = '';
      contSubCategory!.text = '';
      contCategory!.text = '';
      contManualActivity!.text = '';
      contDescription!.text = '';
      contUM!.text = '';
      contQuantity!.text = '1';
      contUnitCost!.text = '';
      contTotal!.text = '';
      addManualActivity = false;
    } else {
      try {
        contActivity!.text = '';
        contSubCategory!.text = '';

        // print({'quotationData', quotationData});
        

        setState(() {});

        contCategory!.text = category;
        setSubCategories();
        if (quotationData['subCategory'] != '') {
          contSubCategory!.text = quotationData['subCategory'];
        } else {
          contSubCategory!.clear();
        }
        setActivities();


        int selectedActivity =
            activities.indexWhere((e){
              return e['value'] == quotationData['title'];
            });
        contActivity!.text =
            selectedActivity > -1 ? quotationData['title'] : '';
        contManualActivity!.text =
            selectedActivity == -1 ? quotationData['title'] : '';
        contDescription!.text = quotationData['description'];
        contUM!.text = quotationData['measurementUnit'];
        contQuantity!.text = quotationData['quantity'].toString();
        contUnitCost!.text = quotationData['unitPrice'].toString();
        contTotal!.text = quotationData['total'].toString();
        addManualActivity = selectedActivity == -1 ? true : false;


        if (quotationData['priceLevel'] != '') {
          contPriceLevel!.text = quotationData['priceLevel'];
        } else {
          contPriceLevel!.clear();
        }

        // contPriceLevel = quotationData['priceLevel'] == '' ? ;
      } catch (e, s) {
        print({e, s});
      }
    }

    setState(() {
      formMessages.clear();
      formMessages.add('');
    });

    return showDialog(
        context: context,
        builder: (BuildContext _context) {
          return StatefulBuilder(builder: (context, setState) {
            return Center(
                child: BaseNewarcPopup(
                    formErrorMessage: formMessages,
                    title: quotationData == null ? 'Aggiungi voce' : 'Modifica voce',
                    buttonText: quotationData == null ? "Inserisci voce" : "Modifica voce",
                    onPressed: () {
                      /*if( !isNumber(contQuantity!.text) ) {
                        setState((){
                          formMessages.clear();
                          formMessages.add('Invalid quantity.');
                          
                        });
                        return false;
                        
                      }*/

                      /*if( !isNumber(contUnitCost!.text) ) {

                        setState((){
                          formMessages.clear();
                          formMessages.add('Invalid unit cost.');
                        });
                        
                        return false;
                      }*/

                      // print({ 'selectedActivities', selectedActivities});
                      // return false;
                      if (selectedActivities.length > 0) {
                        for (var i = 0; i < selectedActivities.length; i++) {
                          try {
                            Map activityData = aef.activitiesForEstimate[
                                    contCategory!.text]![contSubCategory!.text]!
                                .firstWhere((e) =>
                                    e['activity'] ==
                                    selectedActivities[i]['value']);

                            Map rowData = {
                              'title': addManualActivity
                                  ? contManualActivity!.text
                                  : selectedActivities[i]['value'],
                              'subCategory': contSubCategory!.text,
                              'measurementUnit': activityData['um'],
                              'quantity': '1',
                              'unitPrice': localCurrencyFormatMain.format(activityData['f1']),
                              'description': '',
                              'priceLevel': 'F1',
                              'code': !addManualActivity
                                  ? activityData['code']
                                  : '',
                              'comment': '',
                              'isManualActivity': addManualActivity
                            };

                            if (quotationData != null) {
                              rowData['index'] = quotationData['index'];
                            }

                            rowData['isDiscounted'] = false;

                            // print({ 'rowData', rowData });
                            setDataRow(contCategory!.text, rowData);
                          } catch (e, s) {
                            print({e, s});
                          }

                          // setDataRow(contCategory!.text, rowData);
                        }
                      }else if (addManualActivity && quotationData != null) {
                        Map rowData = {
                          'title': contManualActivity!.text,
                          'isManualActivity': addManualActivity,
                          'subCategory': contSubCategory!.text,
                          'measurementUnit': contUM!.text,
                          'quantity': contQuantity!.text,
                          'unitPrice': contUnitCost!.text,
                          'description': contDescription!.text,
                          'priceLevel': contPriceLevel!.text,
                          'code': '',
                          'comment': contComment!.text
                        };

                        if (quotationData != null) {
                          rowData['index'] = quotationData['index'];
                        }

                        rowData['isDiscounted'] = false;

                        // print({ 'rowData', rowData });
                        setDataRow(contCategory!.text, rowData);
                      } else if (addManualActivity) {
                        Map rowData = {
                          'title': contManualActivity!.text,
                          'isManualActivity': addManualActivity,
                          'subCategory': contSubCategory!.text,
                          'measurementUnit': '',
                          'quantity': '1',
                          'unitPrice': '0',
                          'description': '',
                          'priceLevel': 'F1',
                          'code': '',
                          'comment': ''
                        };

                        if (quotationData != null) {
                          rowData['index'] = quotationData['index'];
                        }

                        rowData['isDiscounted'] = false;

                        // print({ 'rowData', rowData });
                        setDataRow(contCategory!.text, rowData);
                      }

                      setInitialValues();
                    },
                    column: Container(
                        width: 600,
                        child: Column(
                            children: [
                          Row(
                            children: [
                              Expanded(
                                  flex: 1,
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      NarFormLabelWidget(
                                        label: 'Seleziona categoria',
                                        fontSize: 13,
                                        textColor: Color(0xff696969),
                                        fontWeight: '600',
                                      ),
                                      SizedBox(
                                        height: 4,
                                      ),
                                      NarSelectBoxWidget(
                                        options: categories,
                                        controller: contCategory,
                                        validationType: 'required',
                                        parametersValidate: 'Required!',
                                        contentPadding: EdgeInsets.only(
                                            top: 15,
                                            bottom: 17,
                                            left: 21.0,
                                            right: 8.0),
                                        onChanged: (value) {
                                          selectedActivities.clear();
                                          contSubCategory!.clear();
                                          contActivity!.clear();
                                          contPriceLevel!.clear();
                                          contUnitCost!.clear();
                                          contTotal!.clear();

                                          setSubCategories();
                                          setState(() {});
                                        },
                                      )
                                    ],
                                  )),
                              SizedBox(
                                width: 10,
                              ),
                              Expanded(
                                  flex: 1,
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      NarFormLabelWidget(
                                        label: 'Seleziona sottocategoria',
                                        fontSize: 13,
                                        textColor: Color(0xff696969),
                                        fontWeight: '600',
                                      ),
                                      SizedBox(
                                        height: 4,
                                      ),
                                      NarSelectBoxWidget(
                                        options: subCategories,
                                        controller: contSubCategory,
                                        validationType: 'required',
                                        parametersValidate: 'Required!',
                                        contentPadding: EdgeInsets.only(
                                            top: 15,
                                            bottom: 17,
                                            left: 21.0,
                                            right: 8.0),
                                        onChanged: (value) {
                                          selectedActivities.clear();
                                          contActivity!.clear();
                                          contPriceLevel!.clear();
                                          contUnitCost!.clear();
                                          contTotal!.clear();

                                          setActivities();
                                          setState(() {});
                                        },
                                      )
                                    ],
                                  )),
                            ],
                          ),
                          SizedBox(
                            height: 10,
                          ),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Expanded(
                                  flex: 1,
                                  child: Opacity(
                                    opacity: addManualActivity ? 0.5 : 1,
                                    child: AbsorbPointer(
                                      absorbing: addManualActivity,
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          NarFormLabelWidget(
                                            label: 'Seleziona attività',
                                            fontSize: 13,
                                            textColor: Color(0xff696969),
                                            fontWeight: '600',
                                          ),
                                          SizedBox(
                                            height: 4,
                                          ),
                                          MultiSelectDropdownWidget(
                                            options: activities,
                                            initialValue: selectedActivities,
                                            validationType: 'required',
                                            parametersValidate: 'Obbligatorio',
                                            onChanged:
                                                (List<dynamic> selectedValues) {
                                              changedDetected = true;
                                              selectedActivities =
                                                  selectedValues;
                                              setState(() {});
                                            },
                                          ),
                                          // NarSelectBoxWidget (
                                          //   options: activities,
                                          //   controller: contActivity,
                                          //   validationType: addManualActivity == false ? 'required' : '',
                                          //   parametersValidate: addManualActivity == false ? 'Required' : '',
                                          //   contentPadding: EdgeInsets.only(top: 15, bottom: 17, left: 21.0, right: 8.0),
                                          //   onChanged: (){

                                          //     String selectedSubCategory = contSubCategory!.text;
                                          //     String selectedCategory = contCategory!.text;

                                          //     Map activityData = aef.activitiesForEstimate[selectedCategory]![selectedSubCategory]!.firstWhere((e) => e['activity'] == contActivity!.text);

                                          //     contUM!.text = activityData['um'];

                                          //     contPriceLevel!.clear();
                                          //     contUnitCost!.clear();
                                          //     contTotal!.clear();
                                          //     setState((){});
                                          //   },
                                          // )
                                        ],
                                      ),
                                    ),
                                  )),

                              // SizedBox(width: 10,),
                            ],
                          ),
                          SizedBox(
                            height: 10,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              NarFormLabelWidget(
                                label: 'oppure',
                                textColor: Color(0xff727272),
                                fontSize: 14,
                                textAlign: TextAlign.right,
                              ),
                              SizedBox(
                                width: 5,
                              ),
                              BaseNewarcButton(
                                  buttonText: 'Aggiungi manuale',
                                  fontSize: 14,
                                  textColor: Colors.black,
                                  fontWeight: 'bold',
                                  color: Color(0xffE6E6E6),
                                  notAccent: !addManualActivity,
                                  onPressed: () {
                                    setState(() {
                                      addManualActivity = !addManualActivity;

                                      selectedActivities.clear();

                                      contActivity!.clear();
                                      contPriceLevel!.clear();
                                      contUnitCost!.clear();
                                      contTotal!.clear();

                                      if (addManualActivity == true) {
                                        contActivity!.text = '';
                                      }
                                    });
                                  }),
                            ],
                          ),
                          addManualActivity == false
                              ? SizedBox(
                                  height: 0,
                                )
                              : SizedBox(
                                  height: 10,
                                ),
                          addManualActivity == false
                              ? SizedBox(
                                  height: 0,
                                )
                              : Wrap(
                                  // crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Container(
                                      margin: EdgeInsets.only(bottom: 4),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          NarFormLabelWidget(
                                            label: 'Aggiungi attività manuale',
                                            fontSize: 14,
                                            textColor: Color(0xff696969),
                                            fontWeight: '600',
                                          ),
                                          GestureDetector(
                                            onTap: () {
                                              contActivity!.clear();
                                              contPriceLevel!.clear();
                                              contUnitCost!.clear();
                                              contTotal!.clear();

                                              setState(() {
                                                addManualActivity = !addManualActivity;
                                              });
                                            },
                                            child: MouseRegion(
                                              cursor: SystemMouseCursors.click,
                                              child: NarFormLabelWidget(
                                                label: 'Elimina',
                                                textDecoration:
                                                    TextDecoration.underline,
                                                fontSize: 14,
                                                textColor: Color(0xffC24040),
                                                fontWeight: '600',
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    ResizableNoteField(
                                      controller: contManualActivity,
                                      label: "",
                                    )

                                  ],
                                ),
                          addManualActivity == false
                              ? SizedBox(
                                  height: 10,
                                )
                              : SizedBox(
                                  height: 0,
                                ),

                          
                        ]))));
          });
        });
  }
  final NumberFormat currencyFormat = NumberFormat("#,##0.00", "it_IT");

  @override
  Widget build(BuildContext context) {
    return Container(
      height: double.infinity,
      child: _loading ?
      Container(
          color: Colors.transparent,  // semi-transparent black
          child: Center(
            child: Container(
              padding: EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.transparent,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(color: Theme.of(context).primaryColor,),
                  SizedBox(height: 16),
                  Text(
                    'Creando PDF...',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
        )
      : Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.max,
        children: [
          Row(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                IconButton(
                  hoverColor: Colors.transparent,
                  focusColor: Colors.transparent,
                  onPressed: () {
                    if (changedDetected == false) {
                      widget.updateViewCallback!('renovation-quotation');
                    } else {
                      showDialog(
                          context: context,
                          builder: (_context) {
                            return StatefulBuilder(builder:
                                (BuildContext _bc2,
                                StateSetter setState) {
                              return Center(
                                child: BaseNewarcPopup(
                                  title: 'Attenzione!',
                                  buttonText: 'Esci senza salvare',
                                  onPressed: () async {
                                    widget.updateViewCallback!(
                                        'renovation-quotation');
                                    return true;
                                  },
                                  column: Container(
                                      height: 150,
                                      width: 465,
                                      child: Center(
                                        child: Column(
                                          children: [
                                            NarFormLabelWidget(
                                                overflow:
                                                TextOverflow.visible,
                                                label:
                                                "Stai uscendo dalla pagina senza aver\nsalvato le modifiche.",
                                                textAlign:
                                                TextAlign.center,
                                                fontSize: 18,
                                                fontWeight: '600',
                                                height: 1.5,
                                                textColor:
                                                Color(0xFF696969)),
                                            SizedBox(
                                              height: 10,
                                            ),
                                            NarFormLabelWidget(
                                                overflow:
                                                TextOverflow.visible,
                                                label:
                                                "Vuoi uscire senza salvare?",
                                                textAlign:
                                                TextAlign.center,
                                                fontSize: 18,
                                                fontWeight: 'bold',
                                                height: 1.5,
                                                textColor:
                                                Color(0xFF696969)),
                                          ],
                                        ),
                                      )),
                                ),
                              );
                            });
                          });
                    }
                  },
                  icon: SvgPicture.asset('assets/icons/arrow_left.svg',
                      height: 20, color: Colors.black),
                ),
                SizedBox(
                  width: 10,
                ),
                NarFormLabelWidget(
                  label: widget.renovationQuotation!.renovationContact!.addressInfo != null
                      ? "${widget.renovationQuotation!.renovationContact!.addressInfo!.toShortAddress()}"
                      : widget.renovationQuotation!.renovationContact!.streetAddress!,
                  // .streetAddress! +':'+ widget.renovationQuotation!.id!,
                  fontSize: 22,
                  fontWeight: 'bold',
                  textColor: Colors.black,
                ),
              ]),
          SizedBox(height: 20),
          Expanded(
            child: ListView(
              // shrinkWrap: true,
              children: [
                Container(
                  padding: EdgeInsets.all(15),
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(13),
                      border: Border.all(width: 1, color: Color(0Xffe7e7e7))),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      children: [
                        Row(
                          mainAxisSize: MainAxisSize.max,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            NarFormLabelWidget(
                              label: 'Preventivo ristrutturazione',
                              fontSize: 20,
                              fontWeight: 'bold',
                            ),
                            Row(
                              children: [
                                BaseNewarcButton(
                                  color: Colors.transparent,
                                  textColor: Theme.of(context).primaryColor,
                                  borderColor: Theme.of(context).primaryColor,
                                  buttonText: "Download PDF",
                                  onPressed: () async {
                                    setState(() {
                                      _loading = true;  // Start loading
                                    });
                                    try {
                                      await saveQuotation();
                                      subCategoryTotalCalc();
                                      await pdfDesign(widget.renovationQuotation!, renovationData, categoryTotal, subCategoryTotal,categoryTotalForOnlyToShow,subCategoryTotalForOnlyToShow);
                                    } finally {
                                      setState(() {
                                        _loading = false;
                                      });
                                    }
                                  }
                                ),
                                SizedBox(width: 10),
                                BaseNewarcButton(
                                    buttonText: "Aggiungi voce",
                                    onPressed: () {
                                      addVocePopup();
                                    }),
                              ],
                            )
                          ],
                        ),
                        SizedBox(height: 30),
                        renovationData.length == 0
                            ? SizedBox(height: 0)
                            : Column(
                                children: categoryOrder.map((category) {
                                  bool isExpanded = expandedCategories[category] ?? false;
                                  int index = categoryOrder.indexOf(category);
                                  return Wrap(
                                    children: [
                                      Container(
                                        margin: EdgeInsets.only(top: 15),
                                        padding: EdgeInsets.all(20),
                                        decoration: BoxDecoration(
                                            color: Colors.white,
                                            borderRadius: BorderRadius.only(
                                                topLeft: Radius.circular(13),
                                                topRight: Radius.circular(13),
                                                bottomRight: !isExpanded ?  Radius.circular(13) : Radius.zero,
                                                bottomLeft: !isExpanded ?  Radius.circular(13) : Radius.zero,

                                            ),
                                            border: Border.all(
                                                width: 1,
                                                color: Color(0Xffe7e7e7))),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.max,
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          children: [
                                            Row(
                                              children: [
                                                //------Up Move
                                                Container(
                                                  height: 25,
                                                  width: 25,
                                                  decoration: BoxDecoration(
                                                    color: Color.fromRGBO(227, 227, 227, 1),
                                                    borderRadius: BorderRadius.circular(7.0),
                                                  ),
                                                  child: IconButton(
                                                    onPressed: index > 0 ? () => moveCategory(category, true) : null,
                                                    style: ButtonStyle(
                                                        overlayColor: WidgetStateProperty.all(Colors.transparent)),
                                                    padding: EdgeInsets.zero,
                                                    icon: Transform.rotate(
                                                      angle: -1.5708,
                                                      child: SvgPicture.asset(
                                                          "assets/icons/cash_in.svg",
                                                        color: Color(0xff5b5b5b),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                                SizedBox(width: 5),

                                                //------- Down Move
                                                Container(
                                                  height: 25,
                                                  width: 25,
                                                  decoration: BoxDecoration(
                                                    color: Color.fromRGBO(227, 227, 227, 1),
                                                    borderRadius: BorderRadius.circular(7.0),
                                                  ),
                                                  child: IconButton(
                                                    onPressed: index < categoryOrder.length - 1 ? () => moveCategory(category, false) : null,
                                                    style: ButtonStyle(
                                                        overlayColor:
                                                        WidgetStateProperty.all(Colors.transparent)),
                                                    padding: EdgeInsets.zero,
                                                    icon: Transform.rotate(
                                                      angle: 1.5708,
                                                      child: SvgPicture.asset(
                                                        "assets/icons/cash_in.svg",
                                                        color: Color(0xff5b5b5b),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                                SizedBox(width: 10,),
                                                NarFormLabelWidget(
                                                  label: category,
                                                  fontSize: 18,
                                                  fontWeight: 'bold',
                                                  textColor: Theme.of(context).primaryColor,
                                                ),
                                              ],
                                            ),


                                            SizedBox(
                                              width: 200,
                                              child: Row(
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  NarFormLabelWidget(
                                                    label: 'Forza fascia',
                                                    fontSize: 14,
                                                    fontWeight: '600',
                                                    textColor: AppColor.black,
                                                  ),
                                                  SizedBox(width: 10),
                                                  Expanded(
                                                    child: NarSelectBoxWidget(
                                                      options: ['F1', 'F2', 'F3'],
                                                      contentPadding: EdgeInsets.only(top: 12, bottom: 12, right: 2, left: 10),
                                                      controller: categoryControllers[category],
                                                        onChanged: (value) {
                                                          if (renovationData.containsKey(category)) {
                                                            renovationData[category]!.forEach((subCategory, rows) {
                                                              for (var row in rows) {
                                                                // Skip rows with 'Manuale' price level
                                                                if (row['contPriceLevel'].text == 'Manuale') {
                                                                  log("Skipping update for row with Manuale price level");
                                                                  continue;
                                                                }

                                                                row['contPriceLevel'].text = categoryControllers[category]?.text;
                                                                row['priceLevel'] = categoryControllers[category]?.text;

                                                                Map? activityPrices = aef.activitiesForEstimate[category]?[subCategory]?.firstWhere(
                                                                      (value) => value['activity'] == row['title'],
                                                                  orElse: () => {},
                                                                );

                                                                if (activityPrices != null && activityPrices.isNotEmpty) {

                                                                  final unitPrice = activityPrices[categoryControllers[category]?.text.toLowerCase()] ?? 0;
                                                                  row['unitPrice'] = localCurrencyFormatMain.format(unitPrice).trim();
                                                                  row['contUnitPrice'].text = localCurrencyFormatMain.format(unitPrice).trim();

                                                                  if (isNumber(unitPrice) &&
                                                                      isNumber(row['contQuantity'].text) &&
                                                                      row['contUnitPrice'].text.isNotEmpty &&
                                                                      row['contQuantity'].text.isNotEmpty) {
                                                                    row['contTotal'].text = localCurrencyFormatMain.format((unitPrice *
                                                                        double.parse(row['contQuantity'].text)));
                                                                    row['total'] = row['contTotal'].text;
                                                                  }
                                                                } else {
                                                                  log("No matching activity price found for ${row['title']}");
                                                                }
                                                              }
                                                            });

                                                            subCategoryTotalCalc();

                                                            // Refresh UI
                                                            setState(() {});
                                                          } else {
                                                            log("Category not found in renovationData: $category");
                                                          }
                                                        },
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),

                                            Row(
                                              children: [
                                                NarFormLabelWidget(
                                                  label: localCurrencyFormatMain.format(categoryTotal[category] ?? 0) + '€',
                                                  fontSize: 16,
                                                  fontWeight: 'bold',
                                                  textColor: Theme.of(context).primaryColor,
                                                ),
                                                SizedBox(width: 10),
                                                IconButton(
                                                  hoverColor: Colors.transparent,
                                                  focusColor: Colors.transparent,
                                                  onPressed: (){
                                                    toggleCategory(category);
                                                  },
                                                  icon: isExpanded ? Icon(Icons.expand_less,color: AppColor.black,) : Icon(Icons.expand_more,color: AppColor.black,),
                                                ),
                                              ],
                                            )
                                          ],
                                        ),
                                      ),
                                      if (isExpanded)
                                      Container(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 15, vertical: 10),
                                        decoration: BoxDecoration(
                                            color: Colors.white,
                                            borderRadius: BorderRadius.only(
                                                bottomLeft: Radius.circular(13),
                                                bottomRight:
                                                    Radius.circular(13)),
                                            border: Border(
                                              left: BorderSide(
                                                  width: 1.0,
                                                  color: Color(0Xffe7e7e7)),
                                              right: BorderSide(
                                                  width: 1.0,
                                                  color: Color(0Xffe7e7e7)),
                                              bottom: BorderSide(
                                                  width: 1.0,
                                                  color: Color(0Xffe7e7e7)),
                                            )),
                                        child: Column(
                                          children: [
                                            Wrap(
                                              children:
                                                  renovationData[category]!.keys
                                                      .map((subcategory) {
                                                    List<String> subCategoryKeys = renovationData[category]!.keys.toList();
                                                    int subcategoryIndex = subCategoryKeys.indexOf(subcategory);
                                                return Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    SizedBox(height: 5),
                                                    Row(
                                                      children: [
                                                        //------Up Move
                                                        Container(
                                                          height: 25,
                                                          width: 25,
                                                          decoration: BoxDecoration(
                                                            color: Color.fromRGBO(227, 227, 227, 1),
                                                            borderRadius: BorderRadius.circular(7.0),
                                                          ),
                                                          child: IconButton(
                                                            onPressed: subcategoryIndex > 0 ? () => moveSubCategory(category,subcategory, true) : null,
                                                            style: ButtonStyle(
                                                                overlayColor:
                                                                WidgetStateProperty.all(Colors.transparent)),
                                                            padding: EdgeInsets.zero,
                                                            icon: Transform.rotate(
                                                              angle: -1.5708,
                                                              child: SvgPicture.asset(
                                                                "assets/icons/cash_in.svg",
                                                                color: Color(0xff5b5b5b),
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                        SizedBox(width: 5),

                                                        //------- Down Move
                                                        Container(
                                                          height: 25,
                                                          width: 25,
                                                          decoration: BoxDecoration(
                                                            color: Color.fromRGBO(227, 227, 227, 1),
                                                            borderRadius: BorderRadius.circular(7.0),
                                                          ),
                                                          child: IconButton(
                                                            onPressed: subcategoryIndex < renovationData[category]!.keys.length - 1 ? () => moveSubCategory(category,subcategory, false) : null,
                                                            style: ButtonStyle(
                                                                overlayColor:
                                                                WidgetStateProperty.all(Colors.transparent)),
                                                            padding: EdgeInsets.zero,
                                                            icon: Transform.rotate(
                                                              angle: 1.5708,
                                                              child: SvgPicture.asset(
                                                                "assets/icons/cash_in.svg",
                                                                color: Color(0xff5b5b5b),
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                        SizedBox(width: 10),
                                                        NarFormLabelWidget(
                                                          label: subcategory,
                                                          fontSize: 15,
                                                          fontWeight: 'bold',
                                                          textColor: Colors.black,
                                                        ),
                                                      ],
                                                    ),
                                                    SizedBox(height: 15),
                                                    ...renovationData[
                                                                category]![
                                                            subcategory]!
                                                        .map((entry) {
                                                      return dataRow(category, entry);
                                                    }).toList()
                                                  ],
                                                );
                                              }).toList(),
                                            ),
                                            SizedBox(
                                              height: 10,
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  );
                                }).toList(),
                              ),
                        Container(
                          decoration: BoxDecoration(
                            // color: Color.fromRGBO(72, 155, 121, 0.1),
                            // color: Color.fromRGBO(72, 155, 121, 0.1),
                            border: Border.all( width: 1, color: Color(0xffE7E7E7)),
                            borderRadius: BorderRadius.circular(13)
                          ),
                          padding: EdgeInsets.all(15),
                          margin:  EdgeInsets.only(top: 15),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            mainAxisSize: MainAxisSize.max,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              NarFormLabelWidget(
                                label: 'Modifica prezzi',
                                fontSize: 18,
                                textColor: Theme.of(context).primaryColor,
                                fontWeight: 'bold',
                              ),
                              Container(
                                decoration: BoxDecoration(
                                  color: Color.fromRGBO(72, 155, 121, 0.1),
                                  borderRadius: BorderRadius.circular(10)
                                ),
                                padding: EdgeInsets.all(10),
                                margin:  EdgeInsets.only(top: 10),
                                child: Row(
                                  children: [
                                    Expanded(
                                      child: NarFormLabelWidget(
                                        label: 'Sconta totale',
                                        fontSize: 15,
                                        textColor: Theme.of(context).primaryColor,
                                        fontWeight: 'bold',
                                      ),
                                    ),
                                    Expanded(
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          Container(
                                            width: 250,
                                            child: NarSelectBoxWidget(
                                              options: discountTypes,
                                              controller: contDiscountType,
                                              contentPadding: EdgeInsets.only(
                                                top: 15,
                                                bottom: 17,
                                                left: 21.0,
                                                right: 8.0
                                              ),
                                              onChanged: (value){
                                                contDiscount!.text = '';
                                                setState(() {});
                                              },
                                            ),
                                          ),
                                          Container(
                                            width: 250,
                                            child: 
                                            contDiscountType!.text == 'Percentuale'
                                            ? NarSelectBoxWidget(
                                              options: discounts,
                                              controller: contDiscount,
                                              contentPadding: EdgeInsets.only(
                                                top: 15,
                                                bottom: 17,
                                                left: 21.0,
                                                right: 8.0
                                              ),
                                              onChanged: (value){
                                                setState(() {});
                                              },
                                            )
                                            : Row(
                                              children: [
                                                CustomTextFormField(

                                                  controller: contDiscount,
                                                  label: '',
                                                  isMoney: true,
                                                  onChangedCallback: (value) {
                                                    setState(() {});
                                                  },
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              )
                            ],
                          ),
                        ),

                        Container(
                          decoration: BoxDecoration(
                            color: Theme.of(context).primaryColor,
                            borderRadius: BorderRadius.circular(15)
                          ),
                          padding: EdgeInsets.all(15),
                          margin: EdgeInsets.only(top: 15),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            mainAxisSize: MainAxisSize.max,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              NarFormLabelWidget(
                                label: 'Totale',
                                fontSize: 18,
                                textColor: Color(0xffffffff),
                                fontWeight: 'bold',
                              ),
                              Container(
                                width: 250,
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    NarFormLabelWidget(
                                      label: '€ ${localCurrencyFormatMain.format(((){
                                        double quotTotal = grandTotal;
                                        double? discount = double.tryParse(contDiscount!.text.replaceAll('%','').replaceAll('.','').replaceAll(',','.'));
                                        if(discount != null){
                                          if (discount>0){
                                            if (contDiscountType!.text == 'Percentuale'){
                                              quotTotal = grandTotal*((100 - discount )/100);
                                            } else {
                                              quotTotal = grandTotal - discount;
                                            }
                                          }
                                        }
                                        return quotTotal;})())} + iva',
                                      textAlign: TextAlign.right,
                                      fontSize: 20,
                                      textColor: Color(0xffffffff),
                                      fontWeight: 'bold',
                                    ),
                                    NarFormLabelWidget(
                                      label: '€ ${localCurrencyFormatMain.format(((){

                                        double quotTotal = grandTotal;
                                        double? discount = double.tryParse(contDiscount!.text.replaceAll('%','').replaceAll('.','').replaceAll(',','.'));
                                        if(discount != null){
                                          if (discount>0){
                                            if (contDiscountType!.text == 'Percentuale'){
                                              quotTotal = grandTotal*((100 - discount )/100);
                                            } else {
                                              quotTotal = grandTotal - discount;
                                            }
                                          }
                                        }
                                        return quotTotal*1.1;})())} iva inclusa',
                                      textAlign: TextAlign.right,
                                      fontSize: 12,
                                      textColor: Color(0xffffffff),
                                      fontWeight: '600',
                                    ),

                                  ],
                                ),
                              )
                            ],
                          ),
                        ),
                        // Container(
                        //     decoration: BoxDecoration(
                        //       color: Color(0xff489B79),
                        //       borderRadius: BorderRadius.circular(15)
                        //     ),
                        //     padding: EdgeInsets.all(15),
                        //     margin: EdgeInsets.only(top: 15),
                        //     height: 50,
                        //     child: Row(
                        //       mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        //       mainAxisSize: MainAxisSize.max,
                        //       crossAxisAlignment: CrossAxisAlignment.center,
                        //       children: [
                        //         Container(
                        //           width: 100,
                        //           child: NarFormLabelWidget(
                        //             label: 'Totale',
                        //             fontSize: 18,
                        //             textColor: Color(0xff489B79),
                        //             fontWeight: 'bold',
                        //           ),
                        //         ),
                                
                        //       ],
                        //     ),
                        //   ),
                        SizedBox(height: 15),

                        Column(mainAxisSize: MainAxisSize.min, children: [
                          SizedBox(height: 15),
                          Container(
                            decoration: BoxDecoration(
                                color: Color.fromRGBO(72, 155, 121, 0.1),
                                borderRadius: BorderRadius.circular(15)),
                            padding: EdgeInsets.all(15),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              mainAxisSize: MainAxisSize.max,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                NarFormLabelWidget(
                                  label: 'Modalità di pagamento',
                                  fontSize: 15,
                                  textColor: Theme.of(context).primaryColor,
                                  fontWeight: 'bold',
                                ),
                                Container(
                                  width: 250,
                                  child: NarSelectBoxWidget(
                                    options: paymentMode,
                                    controller: contPaymentMode,
                                    validationType: 'required',
                                    parametersValidate: 'Required!',
                                    contentPadding: EdgeInsets.only(
                                        top: 15,
                                        bottom: 17,
                                        left: 21.0,
                                        right: 8.0),
                                  ),
                                )
                              ],
                            ),
                          ),
                          SizedBox(height: 15),
                          Container(
                            decoration: BoxDecoration(
                                color: Color.fromRGBO(72, 155, 121, 0.1),
                                borderRadius: BorderRadius.circular(15)),
                            padding: EdgeInsets.all(15),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              mainAxisSize: MainAxisSize.max,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                NarFormLabelWidget(
                                  label: 'Durata presunta cantiere',
                                  fontSize: 15,
                                  textColor: Theme.of(context).primaryColor,
                                  fontWeight: 'bold',
                                ),
                                Container(
                                  width: 250,
                                  child: NarSelectBoxWidget(
                                    options: constructionDuration,
                                    controller: contConstructionDuration,
                                    validationType: 'required',
                                    parametersValidate: 'Required!',
                                    contentPadding: EdgeInsets.only(
                                        top: 15,
                                        bottom: 17,
                                        left: 21.0,
                                        right: 8.0),
                                  ),
                                )
                              ],
                            ),
                          ),
                          SizedBox(
                            height: 20,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              NarFormLabelWidget(label: formMessages[0]),
                            ],
                          ),
                          SizedBox(
                            height: 5,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              BaseNewarcButton(
                                  buttonText: 'Salva',
                                  onPressed: () async {
                                    await saveQuotation();
                                  }
                                ),
                            ],
                          )
                        ]),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void moveCategory(String category, bool moveUp) {
    int index = categoryOrder.indexOf(category);

    if ((moveUp && index > 0) || (!moveUp && index < categoryOrder.length - 1)) {
      setState(() {
        // Swap the category positions in categoryOrder
        String tempCategory = categoryOrder[index];
        categoryOrder[index] = categoryOrder[index + (moveUp ? -1 : 1)];
        categoryOrder[index + (moveUp ? -1 : 1)] = tempCategory;

        // Update the renovationData map to reflect the new order
        Map<String, Map<String, List<Map>>> newRenovationData = {};
        for (String cat in categoryOrder) {
          newRenovationData[cat] = renovationData[cat]!;
        }
        renovationData = newRenovationData;
      });
    }
  }

  void moveSubCategory(String category, String subCategory, bool moveUp) {
    // Get the current subcategory order
    List<String> subCategoryKeys = renovationData[category]!.keys.toList();
    int index = subCategoryKeys.indexOf(subCategory);

    if ((moveUp && index > 0) || (!moveUp && index < subCategoryKeys.length - 1)) {
      setState(() {
        // Swap subcategory keys
        String temp = subCategoryKeys[index];
        subCategoryKeys[index] = subCategoryKeys[index + (moveUp ? -1 : 1)];
        subCategoryKeys[index + (moveUp ? -1 : 1)] = temp;

        // Rebuild the inner map in new order
        Map<String, List<Map>> reorderedSubCategories = {};
        for (String key in subCategoryKeys) {
          reorderedSubCategories[key] = renovationData[category]![key]!;
        }

        renovationData[category] = reorderedSubCategories;
      });
    }
  }

 Future<void> saveQuotation() async {
    if (!_formKey.currentState!.validate()) {
      setState(() {
        formMessages.clear();
        formMessages
            .add('All values are required');
      });
      return;
    }

    setState(() {
      formMessages.clear();
      formMessages.add('Saving');
    });

    // widget.renovationQuotation;

    try {
      List<RenovationActivityCategory>
          renovationActivityCategory = [];

      int categoryCounter = 0;

      renovationData.forEach((_key, renovationDataSubCat) {
        List<RenovationActivity>
            renovationActivity = [];

        if (renovationDataSubCat.length > 0) {
          renovationDataSubCat.forEach((key, value) {
            int raCounter = 0;
            if (value.length > 0) {
              value.forEach((raValue) {
                RenovationActivity tmpRA = new RenovationActivity({
                  'index': raCounter++,
                  'title': raValue['title'],
                  'measurementUnit': raValue['measurementUnit'],
                  'quantity': double.tryParse(raValue['quantity'].toString()),
                  'unitPrice': double.tryParse(raValue['unitPrice'].toString().replaceAll(".", "").replaceAll(",", ".")),
                  'description':
                      raValue['description'],
                  'priceLevel':
                      raValue['priceLevel'],
                  'subCategory':
                      raValue['subCategory'],
                  'code': raValue['code'],
                  'comment': raValue['comment'],
                  'isDiscounted': raValue['isDiscounted'],
                  'isManualActivity': raValue['isManualActivity'],
                });
                renovationActivity.add(tmpRA);
              });
            }
          });
        }
        RenovationActivityCategory tmpRAC =
            new RenovationActivityCategory({
          'index': categoryCounter++,
          'category': _key,
          'activity': renovationActivity
        });

        renovationActivityCategory.add(tmpRAC);
      });

      widget.renovationQuotation!.renovationActivity =  renovationActivityCategory;
      widget.renovationQuotation!.paymentMode = contPaymentMode!.text;
      widget.renovationQuotation!.constructionDuration = contConstructionDuration!.text;

      widget.renovationQuotation!.discountType =  contDiscountType!.text;
      widget.renovationQuotation!.discount =  contDiscount!.text.replaceAll(".", "").replaceAll(",", ".");

      // set modification date
      widget.renovationQuotation!.modificationDate =  DateTime.now().millisecondsSinceEpoch;

      await FirebaseFirestore.instance
          .collection(appConfig
              .COLLECT_RENOVATION_QUOTATION)
          .doc(widget.renovationQuotation!.id)
          .update(widget.renovationQuotation!.toMap());

      setState(() {
        formMessages.clear();
        formMessages.add('Saved!');
      });

      changedDetected = false;
    } catch (e, s) {
      log("saveQuotation ERROR --------> ${e.toString()}");
      log("saveQuotation STACKTRACE --------> ${s.toString()}");
      print({e, s});
      setState(() {
        formMessages.clear();
        formMessages.add('Error occured');
      });
    }
  }
}
