import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/pages/agency/acquired_agency_view.dart';
import 'package:newarc_platform/pages/agency/acquired_contacts_view.dart';
import 'package:newarc_platform/pages/agency/agency_immagina/agency_immagina_view.dart';
import 'package:newarc_platform/pages/agency/agency_persone/agency_persone_view.dart';
import 'package:newarc_platform/pages/agency/inside_project_view.dart';
import 'package:newarc_platform/pages/agency/inside_request_view.dart';
import 'package:newarc_platform/pages/agency/newarc_properties_view.dart';
import 'package:newarc_platform/pages/agency/suggested_contacts/suggested_contacts_view.dart';
import 'package:newarc_platform/pages/agency/settings.dart';
import 'package:newarc_platform/pages/agency/terms_and_conditions.dart';
import 'package:newarc_platform/pages/agency/dashboard_view.dart';
import 'package:newarc_platform/pages/agency/operations_view.dart';
import 'package:newarc_platform/pages/agency/prices_sold.dart';
import 'package:newarc_platform/pages/agency/proposed_estates.dart';
import 'package:newarc_platform/pages/common/web_leads/web_leads_view.dart';
import 'package:newarc_platform/pages/login_page.dart';
import 'package:newarc_platform/utils/storage.dart';
import 'package:newarc_platform/widget/agency/abbonamento_view.dart';
import 'package:newarc_platform/widget/agency/custom_appbar_menu.dart';
import 'package:newarc_platform/widget/agency/custom_drawer.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import '../../widget/agency/custom_notification_tray.dart';
import 'agency_immagina_archive/agency_immagina_project_archive_view.dart';

class HomeAgency extends StatefulWidget {
  const HomeAgency({Key? key, required this.agencyUser}) : super(key: key);

  static const String route = '/home-agency';

  final AgencyUser agencyUser;

  @override
  State<HomeAgency> createState() => _HomeAgencyState();
}

class _HomeAgencyState extends State<HomeAgency> {
  String selectedView = 'progetti-attivi';
  String? profilePicture;
  Map? _projectArguments;

  ReceivedContactsPageFilters? receivedContactsPageFilters;
  var appBarHeight = AppBar().preferredSize.height;

  @override
  void initState() {
    // TODO: implement initState
    getProfilePicture();
    super.initState();
  }

  getProfilePicture() async {
    var url = await agencyProfileUrl(widget.agencyUser.agencyId, widget.agencyUser.profilePicture);

    if (url != '') {
      setState(() {
        profilePicture = url;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: LayoutBuilder(
        builder: (BuildContext context, BoxConstraints constraints) {
          if (constraints.maxWidth > 650) {
            return Scaffold(
              backgroundColor: Colors.white,
              body: Row(
                children: [
                  CustomDrawer(
                    updateViewCallback: _updateViewCallback,
                    agencyUser: widget.agencyUser,
                    selectedView: selectedView,
                  ),
                  Expanded(
                    child: Column(
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(right: 15),
                          child: AppBar(
                            backgroundColor: Colors.transparent,
                            elevation: 0,
                            leading: Container(),
                            actions: <Widget>[
                              getNotificationTray(),
                              getAppbarMenu(profilePicture),
                            ],
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(left: 10.0),
                          child: Container(
                            height: 1,
                            color: Color(0xffe0e0e0),
                          ),
                        ),
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 30.0, vertical: 20),
                            child: selectView(true),
                            // child: Text('Test'),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          } else {
            // Versione ridotta
            return Scaffold(
              backgroundColor: Color(0xffF9F9F9),
              appBar: AppBar(
                backgroundColor: Colors.black,
                actions: [
                  IconButton(
                    icon: Icon(
                      Icons.settings,
                      color: Colors.grey,
                    ),
                    onPressed: () {
                      // do something
                    },
                  ),
                  getNotificationTray(),
                  PopupMenuButton(
                    tooltip: "",
                    icon: SvgPicture.asset(
                      'assets/icons/account.svg',
                      color: Colors.grey,
                      width: 20,
                    ),
                    itemBuilder: (context) => [
                      PopupMenuItem(
                        enabled: false,
                        child: Column(children: [Text(widget.agencyUser.agency!.name!), Text(widget.agencyUser.email!)]),
                        value: 1,
                        onTap: () {},
                      ),
                      PopupMenuItem(
                        child: Text("Logout"),
                        value: 2,
                        onTap: () async {
                          // deleteUserRole();
                          await FirebaseAuth.instance.signOut();
                          Navigator.of(context).pushReplacementNamed(
                            LoginPage.route,
                          );
                        },
                      ),
                    ],
                  )
                ],
              ),
              drawer: CustomDrawer(
                updateViewCallback: _updateViewCallback,
                agencyUser: widget.agencyUser,
                selectedView: selectedView,
              ),
              body: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 20),
                child: Column(
                  children: [
                    Expanded(child: selectView(false)),
                  ],
                ),
              ),
            );
          }
        },
      ),
    );
  }

  void _updateViewCallback(String newSelectedView, {ReceivedContactsPageFilters? filter, Map projectArguments = const {}}) {
    setState(() {
      selectedView = newSelectedView;
      if (projectArguments.length > 0) {
        _projectArguments = projectArguments;
      }
    });

    if (newSelectedView == 'contatti-ricevuti') {
      setState(() {
        receivedContactsPageFilters = filter;
        //fare qualcosa con il filtro, passarlo alla pagina dei contatti...che bordello.
      });
    }
  }

  Widget getNotificationTray() {
    var agencyId = widget.agencyUser.agency!.id;
    return StreamBuilder(
      stream: FirebaseFirestore.instance
          .collection(
            '${appConfig.COLLECT_AGENCIES}/',
          )
          .doc(agencyId)
          .snapshots(),
      builder: (BuildContext context, AsyncSnapshot<DocumentSnapshot<Map<String, dynamic>>> snapshot) {
        //List<NewarcNotification> notifications = [];
        bool notificationsRead = true;

        if (snapshot.hasData) {
          Agency agency = Agency.fromDocument(snapshot.data!.data()!, snapshot.data!.id);
          notificationsRead = agency.notificationsRead!;
          /*snapshot.data!.docs.forEach((doc) {
            notifications
                .add(NewarcNotification.fromDocument(doc.data(), doc.id));
          });*/
        }
        return Stack(
          alignment: Alignment.center,
          children: [
            Container(
              height: 40,
              width: 40,
              color: Colors.transparent,
              child: CustomNotificationTray(agency: widget.agencyUser.agency!, notificationsRead: notificationsRead),
            ),
            notificationsRead
                ? Container()
                : Positioned(
                    right: 10,
                    top: 10,
                    child: Container(
                      width: 9,
                      height: 9,
                      decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor,
                        borderRadius: BorderRadius.circular(20),
                      ),
                    ),
                  )
          ],
        );
      },
    );
  }

  Widget getAppbarMenu(String? profilePicture) {
    return Container(
      height: 40,
      width: 40,
      color: Colors.transparent,
      child: CustomAppbarMenu(
        agency: widget.agencyUser.agency!,
        profilePicture: profilePicture,
        onSettingsTapped: () {
          selectedView = 'agency-setting';
          _updateViewCallback(selectedView);
        },
        onAbbonamentiTapped: () {
          selectedView = 'abbonamento';
          _updateViewCallback(selectedView);
        },
        onPersonaTapped: () {
          selectedView = 'agency-persona';
          _updateViewCallback(selectedView);
        },
      ),
    );
  }

  Widget selectView(bool responsive) {
    if (selectedView == 'dashboard') {
      return Dashboard(agency: widget.agencyUser.agency!, agencyUser: widget.agencyUser, responsive: responsive);
    } else if (selectedView == 'agenzie-iscritte') {
      return AcquiredAgencyView(
        agency: widget.agencyUser.agency!,
      );
    } else if (selectedView == 'contatti-ricevuti') {
      return AcquiredContactsView(agency: widget.agencyUser.agency!, agencyUser: widget.agencyUser, responsive: responsive);
    } else if (selectedView == 'operazioni-newarc') {
      return OperationsView(agency: widget.agencyUser.agency!, agencyUser: widget.agencyUser, responsive: responsive);
    } else if (selectedView == 'proponi-immobile') {
      return ProposedEstatesView(
        agency: widget.agencyUser.agency!,
        agencyUser: widget.agencyUser,
        responsive: responsive,
      );
    } else if (selectedView == 'prezzi-venduto') {
      return PricesSoldView(
        agency: widget.agencyUser.agency!,
        agencyUser: widget.agencyUser,
        responsive: responsive,
      );
    } else if (selectedView == 'term-condition') {
      return AgencyTermsAndConditions();
    } else if (selectedView == 'newarc-properties') {
      return NewarcPropertiesView();
    } else if (selectedView == 'agency-setting') {
      return AgencySetting(agency: widget.agencyUser.agency!, agencyUser: widget.agencyUser, getProfilePicture: getProfilePicture);
    }

    else if (selectedView == 'progetti-attivi') {
      return AgencyImmaginaView(
        projectArguments: {
          'projectFirebaseId': '',
          'property': '',
          'agencyUser': '',
          'updateViewCallback': '',
          'initialFetchProperties': '',
          'isFromRequest': false,
        },
        agencyUser: widget.agencyUser,
        updateViewCallback: _updateViewCallback,
        key: Key('progetti-attivi'),
      );
    }
    else if (selectedView == 'progetti-archiviati') {
      return AgencyImmaginaProjectArchiveView(
        projectArguments: {
          'projectFirebaseId': '',
          'property': '',
          'agencyUser': '',
          'updateViewCallback': '',
          'initialFetchProperties': '',
        },
        agencyUser: widget.agencyUser,
        updateViewCallback: _updateViewCallback,
        key: Key('progetti-archiviati'),
      );
    }
    else if (selectedView == 'inside-request') {
      return InsideRequestView(
        key: Key('inside-request'),
        isFromRequest: _projectArguments!['isFromRequest'],
        initialFetchProperties: _projectArguments!['initialFetchProperties'],
        agencyUser: _projectArguments!['agencyUser'],
        projectArguments: _projectArguments,
        updateViewCallback: _projectArguments!['updateViewCallback'],
        projectFirebaseId: _projectArguments!['projectFirebaseId'],
        isForProfessionals: _projectArguments!['isForProfessionals'],
      );
    } else if (selectedView == 'inside-project') {
      return InsideProjectView(
        isFromRequest: _projectArguments!['isFromRequest'],
        isFromProjectArchive: _projectArguments?['isFromProjectArchive'] ?? false, // for progetti-attivi view
        projectArguments: _projectArguments,
        projectFirebaseId: _projectArguments!["projectFirebaseId"],
        agencyUser: _projectArguments!["agencyUser"],
        key: Key('inside-project'),
        updateViewCallback: _updateViewCallback,
      );
    }
    else if (selectedView == 'abbonamento') {
      return AbbonamentoView(
        agencyUser: widget.agencyUser.agency!,
        key: Key('abbonamento'),
        updateViewCallback: _updateViewCallback,
      );
    }
    else if (selectedView == 'web-lead') {
      return WebLeadsView(
        responsive: responsive,
        agencyId: widget.agencyUser.agencyId,
      );
    } else if (selectedView == 'suggested-contacts') {
      return SuggestedContactsView(
        responsive: responsive, 
        agencyId: widget.agencyUser.agencyId,
      );
    }else if (selectedView == 'agency-persona') {
      return AgencyPersoneView(
        responsive: responsive,
        agencyId: widget.agencyUser.agencyId!,
        updateViewCallback: _updateViewCallback,
      );
    } else {
      return Text('La vista selezionata non è disponibile');
    }
  }
}
