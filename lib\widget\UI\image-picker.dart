import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:newarc_platform/widget/UI/alert.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
// import 'package:newarc_platform/widget/UI/button.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/link.dart';
import 'package:newarc_platform/utils/storage.dart';
import 'dart:html';
import 'package:http/http.dart';
// import 'package:newarc_platform/widget/UI/image-dialog.dart';
// import 'dart:io';

/// *  textInputType - The type of information for which to optimize the text input control.
/// *  hintText - Text that suggests what sort of input the field accepts.
/// *  prefixIcon - Pass Icon if required
/// *  defaultText - If there is predefined value is there for a text field
/// *  focusNode - Currently focus node
/// *  obscureText - Is Password field?
/// *  controller - Text controller
/// *  functionValidate - Validation function(currently I have used to check empty field)
/// *  parametersValidate - Value to validate
/// *  actionKeyboard - Keyboard action eg. next, done, search, etc
/// *  onSubmitField - Done click action
/// *  onFieldTap - On focus on TextField
/// *  storageDirectory - Define the directory for storing the images. Default newarcHomes
/// *  removeExistingOnChange - choose if you wanted to replace existing image with the new. This feature is useful when you are dealing with Profile Picture where you have to replace the existing image with new image.
/// *  removeExistingMessage - A confirmation message before you remove the existing image. The option is valid if removeExistingOnChange is set to TRUE.

class NarImagePickerWidget extends StatefulWidget {
  // final String? label;
  // final Color? textColor;
  // String? fontWeight = '800';
  // final double? fontSize;
  // final TextAlign? textAlign;
  // final NarButtonWidget? openPopup;

  // Popup and images config
  final bool? allowMultiple;
  final bool? removeButton;
  final String? removeButtonText;
  final BuildContext? pageContext;
  final String? removeButtonPosition;
  final String? uploadButtonPosition; //top, bottom
  final double? imageDimension;
  final double? imageBorderRadius;
  final num? imagesToDisplayInList;
  final String? showMoreButtonText;
  final String? displayFormat;

  // Button config

  final String? text;
  final Color? textColor;
  final Color? color;
  final Color? splashColor;
  final Color? hoverColor;
  final double? borderRadius;
  final double? minWidth;
  final double? height;
  final Color? borderSideColor;
  final TextStyle? style;
  final Widget? leadingIcon;
  final Widget? trailingIcon;
  final double? fontSize;
  final String? fontWeight;
  final List<dynamic>? images;
  final String? firebaseId;
  final List? preloadedImages;
  final String? storageDirectory;
  final bool? removeExistingOnChange;
  final String? removeExistingMessage;

  final bool? isDownloadable;
  final bool? hasPreview;

  final BoxDecoration? imageContainerBoxDecoration;
  final EdgeInsets? imageContainerPadding;
  final String? imageContainerUploadButtonAlignment;
  final Color? removeButtonTextColor;

  final int? imageQuality;

  final void Function(List<XFile>)?  onImageChange;


  NarImagePickerWidget(
      {this.allowMultiple = true,
      this.removeButton,
      this.removeButtonPosition = 'top',
      this.uploadButtonPosition = 'front',
      this.removeButtonText,
      this.imageDimension = 100,
      this.imagesToDisplayInList = 0,
      this.showMoreButtonText,
      this.displayFormat = 'row',
      this.text,
      this.textColor,
      this.color,
      this.splashColor,
      this.borderRadius,
      this.imageBorderRadius = 10,
      this.minWidth,
      this.height,
      this.borderSideColor,
      this.style,
      this.leadingIcon,
      this.trailingIcon,
      this.hoverColor,
      this.fontSize = 14,
      this.fontWeight = '800',
      this.preloadedImages,
      this.firebaseId,
      this.images,
      this.pageContext,
      this.storageDirectory = 'newarcHomes/',
      this.removeExistingOnChange,
      this.removeExistingMessage = '',
      this.imageContainerBoxDecoration =
          const BoxDecoration(color: Colors.white),
      this.imageContainerPadding = const EdgeInsets.all(0),
      this.imageContainerUploadButtonAlignment = 'start',
      this.isDownloadable = false,
      this.hasPreview = false,
      this.imageQuality = 50,
      this.removeButtonTextColor = Colors.black,
      this.onImageChange,
    });

  @override
  _NarImagePickerWidgetState createState() => _NarImagePickerWidgetState();
  
}

class _NarImagePickerWidgetState extends State<NarImagePickerWidget> {
  ImagePicker picker = ImagePicker();
  num hiddenImages = 0;
  num counter = 0;
  double? imageBorderRadius;
  List<String> preloadImages = [];

  @override
  void initState() {
    super.initState();

    try {
      getFirestoreImages(widget.storageDirectory);
    } catch (e) {
      // print('fire is here');
    }
  }

  @protected
  void didUpdateWidget(NarImagePickerWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    getFirestoreImages(widget.storageDirectory);
  }

  getFirestoreImages(directory) async {
    preloadImages.clear();

    if (widget.preloadedImages != null && widget.preloadedImages!.length > 0) {
      for (var i = 0; i < widget.preloadedImages!.length; i++) {
        await printUrl(directory, widget.firebaseId, widget.preloadedImages![i])
            .then((value) {
          if (preloadImages.indexOf(value) == -1) {
            preloadImages.add(value);
          }
        });

        setState(() {});
      }
    }

    // print({'',preloadImages});
  }

  getImages() {
    return widget.images;
  }

  Widget openDialogButton(context) {
    return BaseNewarcButton(
        buttonText: widget.text ?? '',
        onPressed: () {
          if (widget.removeExistingOnChange == true &&
              widget.removeExistingMessage != '') {
            // if( widget.removeExistingMessage != '' ) {

            // Display alert box if ther is a message to confirm before delete

            NarAlertDialog(context, 'Conferma', widget.removeExistingMessage, [
              GestureDetector(
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 14, vertical: 10),
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor,
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(
                    "Yes",
                    style: TextStyle(color: Colors.white),
                  ),
                ),
                onTap: () {
                  Navigator.of(context).pop(true);
                  // setState(() {
                  //   preloadImages.clear();
                  // });

                  return _showMyDialog(context);
                },
              ),
              GestureDetector(
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 14, vertical: 10),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(
                    "Annulla",
                    style: TextStyle(color: Colors.white),
                  ),
                ),
                onTap: () {
                  Navigator.of(context).pop(true);
                },
              )
            ]);
            // }
          } else {
            return _showMyDialog(context);
          }
        });
  }

  Widget openDialogButtonOld(context) {
    return ButtonTheme(
      height: 45,
      // minWidth: widget.minWidth??100,
      // padding: const EdgeInsets.symmetric(vertical: 30, horizontal: 20),
      child: ElevatedButton(
          style: ButtonStyle(
            padding: MaterialStateProperty.all(
                EdgeInsets.symmetric(vertical: 15, horizontal: 35)),
            shape: MaterialStateProperty.all<OutlinedBorder>(
                RoundedRectangleBorder(
                    borderRadius:
                        BorderRadius.circular(widget.borderRadius ?? 25),
                    side: BorderSide(
                        color: widget.borderSideColor ?? Colors.black))),
            foregroundColor: MaterialStateProperty.all<Color>(
                widget.textColor ?? Colors.white),
            backgroundColor: MaterialStateProperty.all<Color>(
                Theme.of(context).primaryColor),
            overlayColor: MaterialStateProperty.resolveWith<Color>(
              (Set<MaterialState> states) {
                if (states.contains(MaterialState.hovered))
                  return widget.hoverColor ?? Theme.of(context).primaryColor;
                if (states.contains(MaterialState.focused) ||
                    states.contains(MaterialState.pressed))
                  return Theme.of(context).primaryColor;
                return Theme.of(context)
                    .primaryColor; // Defer to the widget's default.
              },
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            // This is must when you are using Row widget inside Raised Button
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              // _buildLeadingIcon(widget.leadingIcon!),
              widget.leadingIcon == null
                  ? SizedBox(
                      height: 0,
                    )
                  : Row(
                      children: <Widget>[
                        widget.leadingIcon!,
                        SizedBox(width: 5)
                      ],
                    ),
              NarFormLabelWidget(
                label: widget.text ?? '',
                textColor: Colors.white,
                fontWeight: '600',
                fontSize: 15,
                textAlign: TextAlign.center,
              ),
              /*Text(
                widget.text ?? '',
              ),*/
              widget.trailingIcon == null
                  ? SizedBox(
                      height: 0,
                    )
                  : Row(
                      children: <Widget>[
                        widget.trailingIcon!,
                        SizedBox(width: 10)
                      ],
                    ),
            ],
          ),
          onPressed: () {
            if (widget.removeExistingOnChange == true &&
                widget.removeExistingMessage != '') {
              // if( widget.removeExistingMessage != '' ) {

              // Display alert box if ther is a message to confirm before delete

              NarAlertDialog(
                  context, 'Conferma', widget.removeExistingMessage, [
                GestureDetector(
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 14, vertical: 10),
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor,
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Text(
                      "Yes",
                      style: TextStyle(color: Colors.white),
                    ),
                  ),
                  onTap: () {
                    Navigator.of(context).pop(true);
                    // setState(() {
                    //   preloadImages.clear();
                    // });

                    return _showMyDialog(context);
                  },
                ),
                GestureDetector(
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 14, vertical: 10),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Text(
                      "Cancel",
                      style: TextStyle(color: Colors.white),
                    ),
                  ),
                  onTap: () {
                    Navigator.of(context).pop(true);
                  },
                )
              ]);
              // }
            } else {
              return _showMyDialog(context);
            }
          }),
    );
  }

  Widget? displayImages = NarFormLabelWidget(
    label: 'Nessuna immagine selezionata',
    fontWeight: '800',
  );

  _showMyDialog(BuildContext context) {
    return showDialog(
      context: context,
      barrierDismissible: false, // user must tap button!

      // barrierDismissible: ,
      builder: (_) {
        // builder: (context, setState) {

        return Center(
          child: BaseNewarcPopup(
              title: 'Carica immagini',
              noButton: true,
              column: StatefulBuilder(
                  builder: (BuildContext context, StateSetter setState) {
                return SingleChildScrollView(
                  child: Container(
                    height: 300,
                    width: 500,
                    child: Column(
                      children: [
                        Expanded(
                          flex: 40,
                          child: MouseRegion(
                            cursor: SystemMouseCursors.click,
                            child: GestureDetector(
                              onTap: () async {
                                await getImageFromGallery(context);
                                if (widget.onImageChange != null) {
                                widget.onImageChange!(widget.images as List<XFile>);
                              }
                              setState(() {});
                              },
                              child: Container(
                                decoration: BoxDecoration(
                                    color: Color.fromRGBO(240, 240, 240, 1),
                                    //shape: BoxShape.circle,
                                    borderRadius:
                                        BorderRadius.all(Radius.circular(10))),
                                width: 400,
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.upload_file,
                                      size: 60,
                                      color: Color.fromRGBO(128, 128, 128, 1),
                                    ),
                                    SizedBox(
                                      height: 30,
                                    ),
                                    NarFormLabelWidget(
                                      label: "Clicca per caricare",
                                      fontWeight: '700',
                                      fontSize: 18,
                                      textColor: Color.fromRGBO(128, 128, 128, 1),
                                      textAlign: TextAlign.center,
                                    )
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                        SizedBox(
                          height: 30,
                        ),
                        widget.imagesToDisplayInList == 0
                            ? Container()
                            : Expanded(
                                flex: 60,
                                child: displayImages!,
                              )
                      ],
                    ),
                  ),
                );
              })),
        );

        /*AlertDialog(
          title: Stack(
            alignment: Alignment.center,
            children: [
              NarFormLabelWidget(
                  label: 'Carica immagini',
                  fontSize: 22,
                  textColor: Colors.black,
                  fontWeight: '900',
                  textAlign: TextAlign.center),
              Positioned(
                top: 0.0,
                right: 0.0,
                child: ElevatedButton(
                  child: Icon(Icons.close),
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  style: ButtonStyle(
                    // padding: MaterialStateProperty.all(
                    //   EdgeInsets.symmetric(vertical: 22, horizontal: 20)
                    // ),
                    // shape: MaterialStateProperty.all<OutlinedBorder>(RoundedRectangleBorder(
                    //   borderRadius: BorderRadius.circular(widget.borderRadius ?? 25),
                    //   side: BorderSide(color: widget.borderSideColor ?? Colors.black))) ,
                    shadowColor:
                        MaterialStateProperty.all<Color>(Colors.transparent),
                    foregroundColor: MaterialStateProperty.all<Color>(
                        widget.textColor ?? Colors.black),
                    backgroundColor:
                        MaterialStateProperty.all<Color>(Colors.transparent),
                    overlayColor: MaterialStateProperty.all(Colors.transparent),
                  ),
                ),
              ),
            ],
          ),
          content: StatefulBuilder(
              builder: (BuildContext context, StateSetter setState) {
            return SingleChildScrollView(
              child: Container(
                height: 500,
                width: 800,
                child: Column(
                  children: [
                    Expanded(
                      flex: 40,
                      child: GestureDetector(
                        onTap: () async {
                          await getImageFromGallery(context);
                          setState(() {});
                        },
                        child: Container(
                          decoration: BoxDecoration(
                              color: Color.fromRGBO(240, 240, 240, 1),
                              //shape: BoxShape.circle,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(10))),
                          width: 400,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.upload_file,
                                size: 60,
                                color: Color.fromRGBO(128, 128, 128, 1),
                              ),
                              SizedBox(
                                height: 30,
                              ),
                              NarFormLabelWidget(
                                label:
                                    "Clicca per caricare\no trascina le immagini.",
                                fontWeight: '700',
                                fontSize: 18,
                                textColor: Color.fromRGBO(128, 128, 128, 1),
                                textAlign: TextAlign.center,
                              )
                            ],
                          ),
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 30,
                    ),
                    Expanded(
                      flex: 60,
                      child: displayImages!,
                    )
                  ],
                ),
              ),
            );
          }),
          actions: <Widget>[
            // TextButton(
            //   child: const Text('Approve'),
            //   onPressed: () {
            //     Navigator.of(context).pop();
            //   },
            // ),
          ],
        );*/
        // }
      },
    );
  }

  getImageFromGallery(BuildContext context) async {
    if (widget.allowMultiple == true) {
      await picker.pickMultiImage(imageQuality: widget.imageQuality ).then((value) {
        widget.images!.addAll(value);

        // If the option is true then remove all the images that are in the selection
        if (widget.removeExistingOnChange == true) {
          setState(() {
            preloadImages.clear();
          });
        }

        displayImages = Wrap(
            children: widget.images!.map((image) {
          return imageContainer(image);
        }).toList());
        updateCounters();
        setState(() {});
      });
    } else {
      await picker
          .pickImage(source: ImageSource.gallery, imageQuality: widget.imageQuality)
          .then((value) {
        setState(() {
          preloadImages.clear();
          widget.images!.clear();
        });

        widget.images!.add(value);

        displayImages = Wrap(
            children: widget.images!.map((image) {
          return imageContainer(image);
        }).toList());
        updateCounters();
        setState(() {});

        Navigator.of(context).pop(true);
      });
    }
  }

  updateCounters() {
    counter = 0;
    hiddenImages = widget.images!.length - widget.imagesToDisplayInList!;
  }

  Widget imageContainer(image) {
    return Column(
      children: [
        widget.removeButton == true && widget.removeButtonPosition == 'top'
            ? Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      widget.isDownloadable == true
                          ? NarLinkWidget(
                              text: 'Download',
                              fontWeight: '800',
                              onClick: () {
                                downloadFile(image);
                              },
                            )
                          : SizedBox(
                              width: 0,
                            ),
                      NarLinkWidget(
                        text: widget.removeButtonText,
                        fontWeight: '800',
                        textColor: widget.removeButtonTextColor,
                        onClick: () {
                          setState(() {
                            widget.images!.remove(image);
                            updateCounters();
                          });
                        },
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 10,
                  )
                ],
              )
            : SizedBox(
                height: 0,
              ),
        Container(
            height: widget.imageDimension,
            width: widget.imageDimension,
            margin: EdgeInsets.only(left: 5),
            decoration: BoxDecoration(
              image: DecorationImage(
                fit: BoxFit.cover,
                image: NetworkImage(image!.path),
              ),
              color: Colors.white,
              borderRadius: BorderRadius.all(
                Radius.circular(widget.imageBorderRadius!),
              ),
              border: Border.all(
                width: 1,
                color: Color(0xffdbdbdb),
              ),
            ),
            child: SizedBox(
              height: 0,
            )

            // ClipRRect(
            //   borderRadius: BorderRadius.circular(widget.imageBorderRadius!),
            //   child: Image.network(
            //     image!.path,
            //     height: widget.imageDimension,
            //   ),
            // )
            ),
        widget.removeButton == true && widget.removeButtonPosition == 'bottom'
            ? Column(
                children: [
                  SizedBox(
                    height: 10,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      widget.isDownloadable == true
                          ? NarLinkWidget(
                              text: 'Download',
                              fontWeight: '800',
                              onClick: () {
                                downloadFile(image);
                              },
                            )
                          : SizedBox(
                              width: 0,
                            ),
                      NarLinkWidget(
                        text: widget.removeButtonText,
                        textColor: widget.removeButtonTextColor,
                        fontWeight: '800',
                        onClick: () {
                          setState(() {
                            widget.images!.remove(image);
                            updateCounters();
                          });
                        },
                      ),
                    ],
                  ),
                ],
              )
            : SizedBox(
                height: 0,
              ),
        SizedBox(height: 10)
      ],
    );
  }

  downloadFile(String url) async {
    List<String> split0 = url.split('/');
    List<String> split1 = split0[split0.length - 1].split('?');
    List<String> split2 = split1[0].split('%2F');
    String filename = split2[split2.length - 1];

    // print({ 'download', filename, url});

    AnchorElement anchorElement = new AnchorElement(href: url);

    anchorElement.download = filename;
    anchorElement.target = '_blank';
    anchorElement.click();
  }

  Widget imageFromUrlContainer(index, image) {
    return Column(
      children: [
        Column(
          children: [
            widget.removeButton == true && widget.removeButtonPosition == 'top'
                ? Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          widget.isDownloadable == true
                              ? NarLinkWidget(
                                  text: 'Download',
                                  fontWeight: '800',
                                  onClick: () {
                                    downloadFile(image);
                                  },
                                )
                              : SizedBox(
                                  width: 0,
                                ),
                          NarLinkWidget(
                            text: widget.removeButtonText,
                            textColor: widget.removeButtonTextColor,
                            fontWeight: '800',
                            onClick: () async {
                              String filename = widget.preloadedImages![index];

                              await deleteFile(
                                  widget.storageDirectory! +
                                      '/' +
                                      widget.firebaseId! +
                                      '/',
                                  filename);
                              setState(() {
                                preloadImages.removeAt(index);
                                widget.preloadedImages!.removeAt(index);
                              });
                            },
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 10,
                      ),
                    ],
                  )
                : SizedBox(
                    height: 0,
                  ),
            GestureDetector(
              onTap: () {
                if (widget.hasPreview == false) return;

                print('tap');
              },
              child: Container(
                  height: widget.imageDimension,
                  width: widget.imageDimension,
                  margin: EdgeInsets.only(left: 5),
                  decoration: BoxDecoration(
                    image: DecorationImage(
                      fit: BoxFit.cover,
                      image: NetworkImage(image),
                    ),
                    color: Colors.white,
                    borderRadius: BorderRadius.all(
                      Radius.circular(widget.imageBorderRadius!),
                    ),
                    border: Border.all(
                      width: 1,
                      color: Color(0xffdbdbdb),
                    ),
                  ),
                  // child: Text('ddf')
                  child: SizedBox(
                    height: 0,
                  )),
            ),
            widget.removeButton == true &&
                    widget.removeButtonPosition == 'bottom'
                ? Column(
                    children: [
                      SizedBox(
                        height: 10,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          widget.isDownloadable == true
                              ? NarLinkWidget(
                                  text: 'Download',
                                  fontWeight: '800',
                                  onClick: () {
                                    downloadFile(image);
                                  },
                                )
                              : SizedBox(
                                  width: 0,
                                ),
                          NarLinkWidget(
                            text: widget.removeButtonText,
                            textColor: widget.removeButtonTextColor,
                            fontWeight: '800',
                            onClick: () async {
                              String filename = widget.preloadedImages![index];

                              await deleteFile(
                                  widget.storageDirectory! +
                                      '/' +
                                      widget.firebaseId! +
                                      '/',
                                  filename);
                              setState(() {
                                preloadImages.removeAt(index);
                                widget.preloadedImages!.removeAt(index);
                              });
                            },
                          ),
                        ],
                      ),
                    ],
                  )
                : SizedBox(
                    height: 0,
                  ),
            SizedBox(height: 10)
          ],
        ),
      ],
    );
  }

  Widget displayInColumn(context) {
    return SingleChildScrollView(
      scrollDirection: Axis.vertical,
      child: Column(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          openDialogButton(context),
          SizedBox(
            height: 20,
          ),
          widget.preloadedImages != null && preloadImages.length > 0
              ? Wrap(
                  children: preloadImages
                      .mapIndexed((index, element) =>
                          imageFromUrlContainer(index, element))
                      .toList(),
                )
              : NarFormLabelWidget(
                  label: '',
                  fontSize: 12,
                  fontWeight: 'bold',
                ),
          widget.images != null && widget.images!.length > 0
              ? Wrap(
                  children: widget.images!.map((image) {
                    counter++;
                    if (widget.imagesToDisplayInList!.toInt() > 0 &&
                        counter > widget.imagesToDisplayInList!.toInt())
                      return SizedBox(
                        height: 0,
                      );
                    return imageContainer(image);
                    // return Image.file(File(image.path));
                  }).toList(),
                )
              : SizedBox(height: 0),
          widget.imagesToDisplayInList! > 0 && hiddenImages > 0
              ? NarLinkWidget(
                  text: widget.showMoreButtonText! +
                      ' ' +
                      hiddenImages.toString(),
                  fontWeight: '800',
                  fontSize: 15,
                  onClick: () {
                    _showMyDialog(context);
                  },
                )
              : SizedBox(
                  height: 0,
                ),
        ],
      ),
    );
  }

  Widget displayInRow(context) {
    return Container(
      decoration: widget.imageContainerBoxDecoration,
      // width: double.infinity,
      padding: widget.imageContainerPadding,
      child: Wrap(
        crossAxisAlignment: WrapCrossAlignment.center,
        // mainAxisAlignment: MainAxisAlignment.center,
        // mainAxisSize: MainAxisSize.min,
        children: [
          widget.uploadButtonPosition == 'front'
              ? Row(
                  children: [openDialogButton(context), SizedBox(width: 15)],
                )
              : SizedBox(height: 0),
          SizedBox(
            height: 15,
          ),
          widget.preloadedImages != null && preloadImages.length > 0
              ? Wrap(
                  children: preloadImages
                      .mapIndexed(
                          (index, image) => imageFromUrlContainer(index, image))
                      .toList(),
                )
              : NarFormLabelWidget(
                  label: '',
                  fontSize: 12,
                  fontWeight: 'bold',
                ),
          widget.images!.length > 0
              ? Wrap(
                  children: widget.images!.map((image) {
                    counter++;
                    if (widget.imagesToDisplayInList!.toInt() > 0 &&
                        counter > widget.imagesToDisplayInList!.toInt())
                      return SizedBox(
                        height: 0,
                      );

                    return imageContainer(image);
                    // return Image.file(File(image.path));
                  }).toList(),
                )
              : SizedBox(height: 0),
          widget.imagesToDisplayInList! > 0 && hiddenImages > 0
              ? NarLinkWidget(
                  text: widget.showMoreButtonText! +
                      ' ' +
                      hiddenImages.toString(),
                  fontWeight: '800',
                  fontSize: 15,
                  onClick: () {
                    _showMyDialog(context);
                  },
                )
              : SizedBox(
                  height: 0,
                ),
          widget.uploadButtonPosition == 'back'
              ? Row(
                  mainAxisAlignment:
                      widget.imageContainerUploadButtonAlignment == 'end'
                          ? MainAxisAlignment.end
                          : MainAxisAlignment.start,
                  children: [SizedBox(width: 15), openDialogButton(context)],
                )
              : SizedBox(height: 0),
        ],
      ),
    );
  }

  double bottomPaddingToError = 12;

  @override
  Widget build(BuildContext context) {
    return widget.displayFormat == 'row'
        ? displayInRow(context)
        : displayInColumn(context);
  }
}
