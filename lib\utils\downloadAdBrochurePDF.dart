import 'dart:developer';
import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';  
import 'package:image_picker/image_picker.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/classes/newarcProject.dart';
import 'package:newarc_platform/classes/property.dart';
import 'package:newarc_platform/classes/renderImage.dart';
import 'package:newarc_platform/classes/renovationQuotation.dart';
import 'package:newarc_platform/classes/user.dart';
import 'package:intl/intl.dart';
import 'package:newarc_platform/utils/storage.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'dart:html' as html;
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:http/http.dart' show get;
import 'ILovePDFCompressor.dart';

getNetworkImage(url) async {
  // print({'loading image:', url});
  log("url ===> ${url}");
  if (url == '') {
    final Uint8List coverLogoData =
        await _loadImage('assets/logo_newarc_immagina.png');
    final coverLogo = pw.MemoryImage(coverLogoData);
    return coverLogo;
  }



  // print({'loading image:', url});

  var response = await get(Uri.parse(url));
  var data = response.bodyBytes;

  // final img.Image? original = img.decodeImage(data);
  // final img.Image resized = img.copyResize(original!, width: 300); // Resize to 300px width

  // return pw.MemoryImage(Uint8List.fromList(img.encodeJpg(resized)));

  log("getNetworkImage response.statusCode ===> ${response.statusCode}");
  log("getNetworkImage response ===> ${response}");

  return pw.MemoryImage(data);
}

Future<Uint8List> _loadImage(String path) async {
  final ByteData data = await rootBundle.load(path);
  return data.buffer.asUint8List();
}

pdfAdsBrochure(Property adData, Map projectDetails,{ValueNotifier<double>? progress}) async {
  progress?.value = 0.0;
  NumberFormat localCurrencyFormatMain =
      NumberFormat.decimalPattern('it_IT');

  try {
    
    
    final ByteData fontMediumData = await rootBundle.load('assets/fonts/Raleway-Medium.ttf');
    final ralewayMedium = pw.Font.ttf(fontMediumData.buffer.asByteData());

    progress?.value = 0.05;

    final ByteData fontBoldData =await rootBundle.load('assets/fonts/Raleway-Bold.ttf');
    final ralewayBold = pw.Font.ttf(fontBoldData.buffer.asByteData());

    progress?.value = 0.10;

    final ByteData fontItalicData =await rootBundle.load('assets/fonts/Raleway-BoldItalic.ttf');
    final ralewayItalic = pw.Font.ttf(fontItalicData.buffer.asByteData());

    progress?.value = 0.15;

    String coverImageUrl = '';
    var coverImage;
    if( adData.photoBrochureCoverPaths!.length > 0 ) {
      coverImageUrl = await printUrl(adData.photoBrochureCoverPaths![0]['location'], '', adData.photoBrochureCoverPaths![0]['filename']);
      coverImage = await getNetworkImage( coverImageUrl );
      progress?.value = 0.20;
    }

    final Uint8List coverLogoData = await _loadImage('assets/logo_newarc_immagina.png');
    final coverLogo = pw.MemoryImage(coverLogoData);

    final pdf = pw.Document();

    List<pw.Widget> pdfCover = [];

    String projectTitle = projectDetails['title'];

    // Plan Images
    String actualPlanImageUrl = await printUrl("newarcHomes/${adData.firebaseId}/current-plan/", '', adData.currentPlan![0]);
    final actualPlanImage = await getNetworkImage( actualPlanImageUrl );

    final Uint8List svgImageIcon = await _loadImage('assets/icons/acquisto.png');
    final svgImageIconPicture = pw.MemoryImage(svgImageIcon);

    // Actual Site Images
    List photoBrochureCoverActualPaths = adData.photoBrochureCoverActualPaths!;
    final List<RenderImageList> brochureActualImages = [];

    List actualImages = [];
    if( photoBrochureCoverActualPaths.isNotEmpty ) {
      for( int rd = 0; rd < photoBrochureCoverActualPaths.length; rd++ ) {

        String _ImageUrl = await printUrl(photoBrochureCoverActualPaths[rd]['location'], '', photoBrochureCoverActualPaths[rd]['filename']);

        RenderImage _tmp = RenderImage({
          'imageBytes': null,
          'tmpFile': null,
          'hasMatch': false,
          'isBrochure': false,
          'isNetworkImage': true,
          'picker': ImagePicker(),
          'filenameUrl': _ImageUrl,
          'filename': photoBrochureCoverActualPaths[rd]['filename'],
          'location': photoBrochureCoverActualPaths[rd]['location'],
          'room': photoBrochureCoverActualPaths[rd]['room'],
        });

        RenderImageList _tmpList = RenderImageList({
          'index': rd,
          'renderImageFirst': _tmp,
          'renderImageSecond': RenderImage.empty()
        });

        actualImages.add(await getNetworkImage( _ImageUrl ));

        brochureActualImages.add(_tmpList);
      }
    }

    progress?.value = 0.25;

    // Project Brochure Images
    String projectPlanImageUrl = await printUrl("newarcHomes/${adData.firebaseId}/", '', adData.picturePaths![0]);
    final projectPlanImage = await getNetworkImage( projectPlanImageUrl );
    List photoBrochureRenderPaths = adData.photoBrochureRenderPaths!;
    final List<RenderImageList> brochureRenderImages = [];
    List projectImages = [];
    if( photoBrochureRenderPaths.isNotEmpty ) {
      for( int rd = 0; rd < photoBrochureRenderPaths.length; rd++ ) {

        String _ImageUrl = await printUrl(photoBrochureRenderPaths[rd]['location'], '', photoBrochureRenderPaths[rd]['filename']);

        RenderImage _tmp = RenderImage({
          'imageBytes': null,
          'tmpFile': null,
          'hasMatch': false,
          'isBrochure': false,
          'isNetworkImage': true,
          'picker': ImagePicker(),
          'filenameUrl': _ImageUrl,
          'filename': photoBrochureRenderPaths[rd]['filename'],
          'location': photoBrochureRenderPaths[rd]['location'],
          'room': photoBrochureRenderPaths[rd]['room'],
        });

        RenderImageList _tmpList = RenderImageList({
          'index': rd,
          'renderImageFirst': _tmp,
          'renderImageSecond': RenderImage.empty()
        });

        projectImages.add(await getNetworkImage( _ImageUrl ));

        brochureRenderImages.add(_tmpList);
      }
    }
    progress?.value = 0.30;

    // Before After images
    List photoBeforeAfterPaths = adData.photoBeforeAfterPaths!;
    final List<RenderImageList> renderImageBeforeAfterList = [];
    List<Map> photoBeforeAfterPathsImages = [];

    
    if( photoBeforeAfterPaths.isNotEmpty ) {
      for( int rd = 0; rd < photoBeforeAfterPaths.length; rd++ ) {

        photoBeforeAfterPaths[rd]['after']['isBrochure'] = photoBeforeAfterPaths[rd]['after']['isBrochure']??false;

        if( !photoBeforeAfterPaths[rd]['after']['isBrochure'] ) continue;

        String _beforeImageUrl = await printUrl(photoBeforeAfterPaths[rd]['before']['location'], '', photoBeforeAfterPaths[rd]['before']['filename']);
        String _afterImageUrl = await printUrl(photoBeforeAfterPaths[rd]['after']['location'], '', photoBeforeAfterPaths[rd]['after']['filename']);

        RenderImage _beforeTmp = RenderImage({
          'imageBytes': null,
          'tmpFile': null,
          'hasMatch': photoBeforeAfterPaths[rd]['before']['hasMatch'],
          'isBrochure': photoBeforeAfterPaths[rd]['before']['isBrochure'],
          'isNetworkImage': true,
          'picker': ImagePicker(),
          'filenameUrl': _beforeImageUrl,
          'filename': photoBeforeAfterPaths[rd]['before']['filename'],
          'location': photoBeforeAfterPaths[rd]['before']['location'],
          'room': photoBeforeAfterPaths[rd]['before']['room'],
        });

        RenderImage _afterTmp = RenderImage({
          'imageBytes': null,
          'tmpFile': null,
          'hasMatch': photoBeforeAfterPaths[rd]['after']['hasMatch'],
          'isBrochure': photoBeforeAfterPaths[rd]['after']['isBrochure'],
          'isNetworkImage': true,
          'picker': ImagePicker(),
          'filenameUrl': _afterImageUrl,
          'filename': photoBeforeAfterPaths[rd]['after']['filename'],
          'location': photoBeforeAfterPaths[rd]['after']['location'],
          'room': photoBeforeAfterPaths[rd]['after']['room'],
        });

        if( _afterTmp.isBrochure == true ) {
          RenderImageList _tmpList = RenderImageList({
            'index': rd,
            'renderImageFirst': _beforeTmp,
            'renderImageSecond': _afterTmp
          });

          photoBeforeAfterPathsImages.add({
            'before': await getNetworkImage( _beforeImageUrl ),
            'after': await getNetworkImage( _afterImageUrl ),
          });

          renderImageBeforeAfterList.add(_tmpList);

        }
      }
    }

    debugPrint('Ready');

    List photoDayTimePaths = adData.photoDayTimePaths!;
    final List<RenderImageList> renderImageDayList = [];
    List photoDayTimePathsImages = [];
    
    
    if( photoDayTimePaths.isNotEmpty ) {
      for( int rd = 0; rd < photoDayTimePaths.length; rd++ ) {

        photoDayTimePaths[rd]['isBrochure'] = photoDayTimePaths[rd]['isBrochure'] ?? false;
        if( !photoDayTimePaths[rd]['isBrochure'] ) continue;

        String _imageUrl = await printUrl(photoDayTimePaths[rd]['location'], '', photoDayTimePaths[rd]['filename']);

        RenderImage _tmp = RenderImage({
          'imageBytes': null,
          'tmpFile': null,
          'hasMatch': false,
          'isBrochure': photoDayTimePaths[rd]['isBrochure'],
          'isNetworkImage': true,
          'picker': ImagePicker(),
          'filenameUrl': _imageUrl,
          'filename': photoDayTimePaths[rd]['filename'],
          'location': photoDayTimePaths[rd]['location'],
          'room': photoDayTimePaths[rd]['room'],
        });

        RenderImageList _tmpList = RenderImageList({
          'index': rd,
          'renderImageFirst': _tmp,
          'renderImageSecond': RenderImage.empty()
        });

        photoDayTimePathsImages.add( await getNetworkImage( _imageUrl ) );

        renderImageDayList.add(_tmpList);
      }
    }
    

    progress?.value = 0.35;

    String vtImageUrl = '';
    if( adData.videoBrochureVTPaths!.length > 0 ) {
      vtImageUrl = await printUrl(adData.videoBrochureVTPaths![0]['location'], '', adData.videoBrochureVTPaths![0]['filename']);
    }
    final vtImage = await getNetworkImage( vtImageUrl );

    String qrImageUrl = projectDetails['virtualTourUrl'];

    /*if (adData.qrPaths!.length > 0) {
      qrImageUrl = projectDetails['virtualTour'];

      //await printUrl("newarcHomes/${adData.firebaseId}/qr/", '', adData.qrPaths![0]);
    }*/

    //final qrImage = await getNetworkImage(qrImageUrl);

    final Uint8List playButtonData =
        await _loadImage('assets/icons/pdf_play_icon.png');
    final playButton = pw.MemoryImage(playButtonData);

    final Uint8List hammerIconData = await _loadImage('assets/icons/pdf-restructure-hammer.png');
    final hammerIcon = pw.MemoryImage(hammerIconData);

    final Uint8List arrowHouseData = await _loadImage('assets/icons/pdf-house-arrow.png');
    final arrowHouseIcon = pw.MemoryImage(arrowHouseData);

    final Uint8List materialPremiumData = await _loadImage('assets/icons/pdf-material-premium.png');
    final materialPremiumIcon = pw.MemoryImage(materialPremiumData);

    final Uint8List materialStandardData = await _loadImage('assets/icons/pdf-material-standard.png');
    final materialStandardIcon = pw.MemoryImage(materialStandardData);

    final Uint8List infissiData = await _loadImage('assets/icons/pdf-infissi.png');
    final infissiDataIcon = pw.MemoryImage(infissiData);

    final Uint8List arrowData = await _loadImage('assets/icons/pdf-arrow.png');
    final arrowIcon = pw.MemoryImage(arrowData);

    final Uint8List workDurationData = await _loadImage('assets/icons/work-duration.png');
    final workDurationIcon = pw.MemoryImage(workDurationData);

    final Uint8List backgroundImageData = await _loadImage('assets/background_last_page.jpg');
    final backgroundImage = pw.MemoryImage(backgroundImageData);

    final Uint8List risLogoData = await _loadImage('assets/rq-pdf-cover-logo.png');
    final risLogoImage = pw.MemoryImage(risLogoData);

    final Uint8List immaginaLogoData = await _loadImage('assets/immagina_logo_white.png');
    final immaginaLogoImage = pw.MemoryImage(immaginaLogoData);

    final Uint8List locationIconData = await _loadImage('assets/icons/pdf-location.png');
    final locationIcon = pw.MemoryImage(locationIconData);

    final Uint8List phoneIconData = await _loadImage('assets/icons/pdf-phone.png');
    final phoneIcon = pw.MemoryImage(phoneIconData);

    final Uint8List areaIconData = await _loadImage('assets/icons/pdf-icon-area.png');
    final areaIcon = pw.MemoryImage(areaIconData);

    final Uint8List roomsIconData = await _loadImage('assets/icons/pdf-icon-rooms.png');
    final roomIcon = pw.MemoryImage(roomsIconData);

    final Uint8List bathsIconData = await _loadImage('assets/icons/pdf-icon-baths.png');
    final bathsIcon = pw.MemoryImage(bathsIconData);

    progress?.value = 0.45;



    List standardFeatures = [
      'Allestimento cantiere',
      'Rifacimento completo bagni',
      'Controsoffitto bagni',
      'Ripristino pavimenti',
      'Posa porte interne',
      'Decorazioni e finiture'
    ];

    List premiumFeatures = [
      'Progettazione',
      'Pratiche',
      'Allestimento cantiere',
      'Demolizioni e costruzioni',
      'Massetti',
      'Controsoffitti',
      'Impianto elettrico',
      'Impianto idrico / gas',
      'Rifacimento completo bagni',
      'Posa pavimenti e rivestimenti',
      'Posa porte interne',
      'Predisposizione climatizzazione',
      'Rasature',
      'Decorazioni e finiture'
    ];

    // Fetch agency data
    var agencyLogo;
    String agencyImageUrl = '';
    Agency agencyData = Agency.empty();
    if( projectDetails['agencyId'] != '' ) {

      QuerySnapshot<Map<String, dynamic>> collectionSnapshot = await FirebaseFirestore.instance
      .collection(appConfig.COLLECT_USERS)
      .where('agencyId', isEqualTo: projectDetails['agencyId'])
      .limit(1)
      .get();

      DocumentSnapshot<Map<String, dynamic>> collectionSnapshotAgency = await FirebaseFirestore.instance
        .collection(appConfig.COLLECT_AGENCIES)
        .doc(projectDetails['agencyId'])
        .get();


      if( collectionSnapshot.docs.length > 0 ) {

        AgencyUser agency = AgencyUser.fromDocument(collectionSnapshot.docs[0].data() , collectionSnapshot.docs[0].id);
        agencyImageUrl = await agencyProfileUrl(agency.agencyId, agency.profilePicture);
        agencyLogo = await getNetworkImage( agencyImageUrl );
        progress?.value = 0.50;
      }

      if( collectionSnapshotAgency.exists ) {
        agencyData = Agency.fromDocument(collectionSnapshotAgency.data()!, collectionSnapshotAgency.id);
        agencyData.phone = agencyData.phone!.replaceFirst('+39 ', '');
      }

    }

    progress?.value = 0.55;




    // Cover page starts

    List<pw.Widget> pdfPage1 = [];
    String addressLine = '';
    if( adData.zone != '' ) {
     addressLine += '${adData.zone}, ';
    }

    if( adData.city != '' ) {
     addressLine += '${adData.city}';
    }

    pdfCover.add(
      pw.Container(
        child: pw.Row(
          children: [
            pw.Expanded(
              flex: 5,
              child: pw.Padding(
                padding: pw.EdgeInsets.symmetric(vertical: 45, horizontal: 30 ),
                child: pw.Column(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Image( coverLogo, height: 80 ),

                    pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [

                        pw.Text(
                          projectTitle,
                          style: pw.TextStyle(
                            fontSize: 40,
                            font: ralewayBold,
                            color: PdfColors.black
                          )
                        ),

                        pw.Text(
                          addressLine,
                          style: pw.TextStyle(
                            fontSize: 25,
                            font: ralewayMedium,
                            color: PdfColors.black
                          )
                        ),


                      ]
                    ),

                    pw.Container(
                      width: 200,
                      child: pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [

                          pw.Text(
                            "Questo immobile\nè in vendita con l’agenzia:",
                            style: pw.TextStyle(
                              fontSize: 16,
                              font: ralewayMedium,
                              color: PdfColors.black
                            )
                          ),

                          pw.SizedBox(height: 10),

                          pw.Image(agencyLogo, height: 59),

                          pw.SizedBox(height: 10),

                          pw.Text(
                            agencyData.name!,
                            style: pw.TextStyle(
                              fontSize: 20,
                              font: ralewayBold,
                              color: PdfColors.black
                            )
                          ),
                          pw.Text(
                            '${agencyData.toShortAddress()!}',
                            style: pw.TextStyle(
                              fontSize: 16,
                              font: ralewayMedium,
                              color: PdfColors.black
                            )
                          ),
                          pw.Text(
                              agencyData.phone!,
                            style: pw.TextStyle(
                              fontSize: 16,
                              font: ralewayMedium,
                              color: PdfColors.black
                            )
                          ),


                        ]
                      )
                    ),

                  ]
                )
              )

            ),
            pw.Expanded(
              flex: 7,
              child: coverImageUrl == ''
              ? pw.Container()
              : pw.Container(
                decoration: pw.BoxDecoration(
                  image: pw.DecorationImage( image: coverImage, fit: pw.BoxFit.cover)
                ),
                child: pw.Stack(
                  children: [
                    pw.Positioned(
                      bottom: 20,
                      right: 20,
                      child: pw.Opacity(
                        opacity: 0.4,
                        child: pw.Container(
                          height: 80,
                          width: 400,
                          decoration: pw.BoxDecoration(
                            borderRadius: pw.BorderRadius.circular(10),
                            color: PdfColors.black
                          ),
                        )
                      )

                    ),
                    pw.Positioned(
                      bottom: 20,
                      right: 20,
                      child: pw.Container(
                        height: 80,
                        width: 400,
                        decoration: pw.BoxDecoration(
                          borderRadius: pw.BorderRadius.circular(10),
                        ),
                        padding: pw.EdgeInsets.symmetric(horizontal: 30, vertical: 25),
                        child: pw.Center(
                          child: pw.Row(
                            mainAxisAlignment: pw.MainAxisAlignment.center,
                            mainAxisSize: pw.MainAxisSize.max,
                            children: [
                              pw.Text(
                                'Prezzo immobile: ',
                                style: pw.TextStyle(
                                  fontSize: 23,
                                  color: PdfColors.white,
                                  font: ralewayMedium
                                )
                              ),
                              pw.Text(
                                '${ localCurrencyFormatMain.format(double.tryParse(adData.styles![0].price??'0') )}€',
                                style: pw.TextStyle(
                                  fontSize: 23,
                                  color: PdfColors.white,
                                  font: ralewayBold
                                )
                              )
                            ]
                          )
                        )

                      )

                    )
                  ]
                )
              )
            ),
          ]
        )
      )

    );
    

    progress?.value = 0.60;


    pdf.addPage(
        pw.Page(
          theme:
              pw.ThemeData(defaultTextStyle: pw.TextStyle(font: ralewayMedium)),
          pageFormat: PdfPageFormat(1300, 940),
          orientation: pw.PageOrientation.landscape,
          margin: const pw.EdgeInsets.all(0),
          build: (pw.Context context) => pdfCover[0],
        )
    );

    // Cover page end

    // Actual Status

    pw.Widget pageFooter;

    pageFooter = pw.Padding(
      padding: pw.EdgeInsets.only(left: 45, right: 45, top: 20, bottom: 10 ),
      child: pw.Row(
        children: [
          pw.Expanded(
            flex: 3,
            child: pw.Container(
              padding: pw.EdgeInsets.symmetric( vertical: 20, horizontal: 20 ),
              decoration: pw.BoxDecoration(
                borderRadius: pw.BorderRadius.circular(12),
                color: PdfColor.fromHex('#F5F5F5')
              ),
              child: pw.Center(
                child: pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    pw.Text(
                      'UN PROGETTO',
                      style: pw.TextStyle(
                        fontSize: 11,
                        color: PdfColor.fromHex('#5A5A5A'),
                        font: ralewayMedium
                      )
                    ),

                    pw.Image(coverLogo, height: 35)
                  ]
                )
              )
            )
          ),
          pw.SizedBox(width: 20),
          pw.Expanded(
            flex: 7,
            child: pw.Container(
              padding: pw.EdgeInsets.symmetric( vertical: 20, horizontal: 20 ),
              decoration: pw.BoxDecoration(
                borderRadius: pw.BorderRadius.circular(12),
                color: PdfColor.fromHex('#F5F5F5')
              ),
              child: pw.Center(
                child: pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [

                    pw.Row(
                      children: [
                        pw.Text(
                          'PER',
                          style: pw.TextStyle(
                            fontSize: 11,
                            color: PdfColor.fromHex('#5A5A5A'),
                            font: ralewayMedium
                          )
                        ),
                        pw.SizedBox(width: 15),
                        pw.Image(agencyLogo, height: 35),
                        pw.SizedBox(width: 15),
                        pw.Text(
                          agencyData.name!,
                          style: pw.TextStyle(
                            fontSize: 14,
                            color: PdfColors.black,
                            font: ralewayBold
                          )
                        ),
                      ]
                    ),
                    pw.Text(
                      '${agencyData.toShortAddress()} • ${agencyData.phone}',
                      style: pw.TextStyle(
                        fontSize: 14,
                        color: PdfColors.black,
                        font: ralewayMedium
                      )
                    ),
                  ]
                )
              )
            )
          ),
        ]
      )
    );

    progress?.value = 0.65;

    int counterAI = 0;

    pdfPage1.add(
      pw.Container(
        constraints: pw.BoxConstraints(
          minHeight: 820
        ),
        padding: pw.EdgeInsets.symmetric(vertical: 40, horizontal: 45 ),
        child: pw.Column(
          children: [
            pw.Container(
              decoration: pw.BoxDecoration(
                border: pw.Border(
                  bottom: pw.BorderSide( width: 1, color: PdfColors.black )
                )
              ),
              padding: pw.EdgeInsets.only(bottom: 10),
              child: pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                crossAxisAlignment: pw.CrossAxisAlignment.center,
                children: [
                  pw.Text(
                    'Stato attuale',
                    style: pw.TextStyle(
                      fontSize: 30,
                      color: PdfColors.black,
                      font: ralewayBold
                    )
                  ),
                  pw.Text(
                    projectTitle,
                    style: pw.TextStyle(
                      fontSize: 16,
                      color: PdfColors.black,
                      font: ralewayBold
                    )
                  )
                ]
              )
            ),
            pw.SizedBox(height: 30),
            pw.Row(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Expanded(
                  flex: 5,
                  child: pw.Container(
                    child: pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.center,
                      children: [
                        pw.Image(
                          actualPlanImage, height: 410, width: 410
                        ),

                        pw.SizedBox(height: 55),
                        pw.Wrap(
                          runAlignment: pw.WrapAlignment.center,
                          children: [
                            pw.Row(
                              mainAxisSize: pw.MainAxisSize.min,
                              children: [
                                pw.Container(
                                  height: 45,
                                  padding: pw.EdgeInsets.symmetric(horizontal: 15 ),
                                  margin: pw.EdgeInsets.only(left: 5, right: 5, bottom: 5),
                                  decoration: pw.BoxDecoration(
                                    borderRadius: pw.BorderRadius.circular(22.5),
                                    border: pw.Border.all(
                                      width: 1,
                                      color: PdfColor.fromHex('#D8D8D8')
                                    )
                                  ),
                                  child: pw.Center(
                                    child: pw.Row(
                                      mainAxisSize: pw.MainAxisSize.min,
                                      children: [
                                        pw.Image( areaIcon, height: 25  ),
                                        pw.SizedBox(width: 10),
                                        pw.Text(
                                          adData.mq.toString()+' mq',
                                          style: pw.TextStyle(
                                            fontSize: 16,
                                            color: PdfColor.fromHex('#626262'),
                                            font: ralewayMedium
                                          )
                                        )

                                      ]
                                    )
                                  )
                                )
                              ]
                            ),
                            pw.Row(
                              mainAxisSize: pw.MainAxisSize.min,
                              children: [
                                pw.Container(
                                  height: 45,
                                  padding: pw.EdgeInsets.symmetric(horizontal: 15 ),
                                  margin: pw.EdgeInsets.only(left: 5, right: 5, bottom: 5),
                                  decoration: pw.BoxDecoration(
                                    borderRadius: pw.BorderRadius.circular(22.5),
                                    border: pw.Border.all(
                                      width: 1,
                                      color: PdfColor.fromHex('#D8D8D8')
                                    )
                                  ),
                                  child: pw.Center(
                                    child: pw.Row(
                                      mainAxisSize: pw.MainAxisSize.min,
                                      children: [
                                        pw.Image( roomIcon, height: 25  ),
                                        pw.SizedBox(width: 10),
                                        pw.Text(
                                          adData.locals.toString()+' locali',
                                          style: pw.TextStyle(
                                            fontSize: 16,
                                            color: PdfColor.fromHex('#626262'),
                                            font: ralewayMedium
                                          )
                                        )

                                      ]
                                    )
                                  )
                                )
                              ]
                            ),
                            pw.Row(
                              mainAxisSize: pw.MainAxisSize.min,
                              children: [
                                pw.Container(
                                  height: 45,
                                  padding: pw.EdgeInsets.symmetric(horizontal: 15 ),
                                  margin: pw.EdgeInsets.only(left: 5, right: 5, bottom: 5),
                                  decoration: pw.BoxDecoration(
                                    borderRadius: pw.BorderRadius.circular(22.5),
                                    border: pw.Border.all(
                                      width: 1,
                                      color: PdfColor.fromHex('#D8D8D8')
                                    )
                                  ),
                                  child: pw.Center(
                                    child: pw.Row(
                                      mainAxisSize: pw.MainAxisSize.min,
                                      children: [
                                        pw.Image( bathsIcon, height: 25  ),
                                        pw.SizedBox(width: 10),
                                        pw.Text(
                                          adData.baths.toString()+( int.tryParse(adData.baths??'0')! > 1 ? ' bagni' : '  bagno' ),
                                          style: pw.TextStyle(
                                            fontSize: 16,
                                            color: PdfColor.fromHex('#626262'),
                                            font: ralewayMedium
                                          )
                                        )

                                      ]
                                    )
                                  )
                                )
                              ]
                            ),
                          ]


                        )

                      ]
                    )
                  )

                ),
                pw.Expanded(
                  flex: 7,
                  child: pw.Column(
                    mainAxisAlignment: pw.MainAxisAlignment.start,
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Wrap(
                        runAlignment: pw.WrapAlignment.end,
                        children: brochureActualImages.map((bActualImage){
                          return pw.Container(
                            margin: pw.EdgeInsets.only(right: 10, bottom: 10),
                            // height: 215,
                            width: 225,
                            child: pw.Column(
                              mainAxisAlignment: pw.MainAxisAlignment.start,
                              crossAxisAlignment: pw.CrossAxisAlignment.center,
                              children: [
                                pw.Container(
                                  decoration: pw.BoxDecoration(
                                    borderRadius: pw.BorderRadius.circular(10),
                                    image: pw.DecorationImage(image: actualImages[counterAI++], fit: pw.BoxFit.cover )
                                  ),
                                  height: 175,
                                  width: 225,
                                ),
                                pw.SizedBox(height: 5),
                                pw.Text(
                                  bActualImage.renderImageFirst!.room!,
                                  style: pw.TextStyle(
                                    fontSize: 16,
                                    color: PdfColor.fromHex('#626262'),
                                    font: ralewayMedium
                                  )
                                )

                              ]
                            )
                          );
                        }).toList()
                      ),
                      pw.SizedBox( height: 50),

                      pw.Paragraph(
                        text: adData.brochureActualDescription!,
                        style: pw.TextStyle(fontSize: 13, color: PdfColor.fromHex('#6E6E6E'), font: ralewayMedium),
                      ),
                    ]
                  )
                )
              ]
            ),
          ]
        )
      )
    );

    counterAI = 0;
 
    // pw.Widget pdfPage2 =

    pdfPage1.add(
      pw.Container(
        constraints: pw.BoxConstraints(
          minHeight: 820
        ),
        padding: pw.EdgeInsets.symmetric(vertical: 40, horizontal: 45 ),
        child: pw.Column(
          children: [
            pw.Container(
              decoration: pw.BoxDecoration(
                border: pw.Border(
                  bottom: pw.BorderSide( width: 1, color: PdfColors.black )
                )
              ),
              padding: pw.EdgeInsets.only(bottom: 10),
              child: pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                crossAxisAlignment: pw.CrossAxisAlignment.center,
                children: [
                  pw.Text(
                    'Progetto',
                    style: pw.TextStyle(
                      fontSize: 30,
                      color: PdfColors.black,
                      font: ralewayBold
                    )
                  ),
                  pw.Text(
                    projectTitle,
                    style: pw.TextStyle(
                      fontSize: 16,
                      color: PdfColors.black,
                      font: ralewayBold
                    )
                  )
                ]
              )
            ),
            pw.SizedBox(height: 30),
            pw.Row(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Expanded(
                  flex: 5,
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.center,
                    children: [
                      pw.Image(projectPlanImage, height: 410, width: 410),

                      pw.SizedBox(height: 55),
                      pw.Wrap(
                        runAlignment: pw.WrapAlignment.center,
                        children: [
                          pw.Row(
                            mainAxisSize: pw.MainAxisSize.min,
                            children: [
                              pw.Container(
                                height: 45,
                                padding: pw.EdgeInsets.symmetric(horizontal: 15 ),
                                margin: pw.EdgeInsets.only(left: 5, right: 5, bottom: 5),
                                decoration: pw.BoxDecoration(
                                  borderRadius: pw.BorderRadius.circular(22.5),
                                  border: pw.Border.all(
                                    width: 1,
                                    color: PdfColor.fromHex('#D8D8D8')
                                  )
                                ),
                                child: pw.Center(
                                  child: pw.Row(
                                    mainAxisSize: pw.MainAxisSize.min,
                                    children: [
                                      pw.Image( areaIcon, height: 25  ),
                                      pw.SizedBox(width: 10),
                                      pw.Text(
                                        adData.mq.toString()+' mq',
                                        style: pw.TextStyle(
                                          fontSize: 16,
                                          color: PdfColor.fromHex('#626262'),
                                          font: ralewayMedium
                                        )
                                      )

                                    ]
                                  )
                                )
                              )
                            ]
                          ),
                          pw.Row(
                            mainAxisSize: pw.MainAxisSize.min,
                            children: [
                              pw.Container(
                                height: 45,
                                padding: pw.EdgeInsets.symmetric(horizontal: 15 ),
                                margin: pw.EdgeInsets.only(left: 5, right: 5, bottom: 5),
                                decoration: pw.BoxDecoration(
                                  borderRadius: pw.BorderRadius.circular(22.5),
                                  border: pw.Border.all(
                                    width: 1,
                                    color: PdfColor.fromHex('#D8D8D8')
                                  )
                                ),
                                child: pw.Center(
                                  child: pw.Row(
                                    mainAxisSize: pw.MainAxisSize.min,
                                    children: [
                                      pw.Image( roomIcon, height: 25  ),
                                      pw.SizedBox(width: 10),
                                      pw.Text(
                                        adData.localsIns.toString()+' locali',
                                        style: pw.TextStyle(
                                          fontSize: 16,
                                          color: PdfColor.fromHex('#626262'),
                                          font: ralewayMedium
                                        )
                                      )

                                    ]
                                  )
                                )
                              )
                            ]
                          ),
                          pw.Row(
                            mainAxisSize: pw.MainAxisSize.min,
                            children: [
                              pw.Container(
                                height: 45,
                                padding: pw.EdgeInsets.symmetric(horizontal: 15 ),
                                margin: pw.EdgeInsets.only(left: 5, right: 5, bottom: 5),
                                decoration: pw.BoxDecoration(
                                  borderRadius: pw.BorderRadius.circular(22.5),
                                  border: pw.Border.all(
                                    width: 1,
                                    color: PdfColor.fromHex('#D8D8D8')
                                  )
                                ),
                                child: pw.Center(
                                  child: pw.Row(
                                    mainAxisSize: pw.MainAxisSize.min,
                                    children: [
                                      pw.Image( bathsIcon, height: 25  ),
                                      pw.SizedBox(width: 10),
                                      pw.Text(
                                        adData.bathsIns.toString()+ ( int.tryParse(adData.bathsIns??'0')! > 1 ? ' bagni' : '  bagno' ),
                                        style: pw.TextStyle(
                                          fontSize: 16,
                                          color: PdfColor.fromHex('#626262'),
                                          font: ralewayMedium
                                        )
                                      )

                                    ]
                                  )
                                )
                              )
                            ]
                          ),
                        ]


                      )

                    ]
                  )
                ),
                pw.Expanded(
                  flex: 7,
                  child: pw.Column(
                    mainAxisAlignment: pw.MainAxisAlignment.start,
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Wrap(
                        runAlignment: pw.WrapAlignment.end,
                        children: brochureRenderImages.map((bActualImage){
                          return pw.Container(
                            margin: pw.EdgeInsets.only(right: 10, bottom: 10),
                            width: 225,
                            child: pw.Column(
                              mainAxisAlignment: pw.MainAxisAlignment.start,
                              crossAxisAlignment: pw.CrossAxisAlignment.center,
                              children: [
                                pw.Container(
                                  decoration: pw.BoxDecoration(
                                    borderRadius: pw.BorderRadius.circular(10),
                                    image: pw.DecorationImage(image: projectImages[counterAI++], fit: pw.BoxFit.cover )
                                  ),
                                  height: 175,
                                  width: 225,
                                ),
                                pw.SizedBox(height: 5),
                                pw.Text(
                                  bActualImage.renderImageFirst!.room!,
                                  textAlign: pw.TextAlign.left,
                                  style: pw.TextStyle(
                                    fontSize: 16,
                                    color: PdfColor.fromHex('#626262'),
                                    font: ralewayMedium
                                  )
                                )

                              ]
                            )
                          );
                        }).toList()
                      ),
                      pw.SizedBox( height: 50),

                      // pw.Text(
                      //   adData.brochureProjectDescription!,
                      //   overflow: pw.TextOverflow.visible,
                      //   style: pw.TextStyle(
                      //     height: 25,
                      //     fontSize: 13,
                      //     color: PdfColor.fromHex('#6E6E6E'),
                      //     font: ralewayMedium
                      //   )
                      // )
                      pw.Paragraph(
                        text: adData.brochureProjectDescription!,
                        style: pw.TextStyle(fontSize: 13, color: PdfColor.fromHex('#6E6E6E'), font: ralewayMedium),
                      ),
                    ]
                  )
                )
              ]
            ),
          ]
        )
      )
    );

    progress?.value = 0.70;


    // Before After Image
    counterAI = 0;
    for (var i = 0; i < renderImageBeforeAfterList.length; i++) {
      pdfPage1.add(
        pw.Container(
          // height: 800,
          padding: pw.EdgeInsets.symmetric(vertical: 20, horizontal: 45 ),
          child: pw.Column(
            children: [
              pw.Container(
                decoration: pw.BoxDecoration(
                  border: pw.Border(
                    bottom: pw.BorderSide( width: 1, color: PdfColors.black )
                  )
                ),
                padding: pw.EdgeInsets.only(bottom: 10),
                child: pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: pw.CrossAxisAlignment.center,
                  children: [
                    pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        pw.Text(
                          'CONFRONTO',
                          style: pw.TextStyle(
                            fontSize: 15,
                            color: PdfColor.fromHex('#888888'),
                            font: ralewayMedium
                          )
                        ),
                        pw.Text(
                          renderImageBeforeAfterList[i].renderImageSecond!.room!,
                          style: pw.TextStyle(
                            fontSize: 30,
                            color: PdfColors.black,
                            font: ralewayBold
                          )
                        ),
                      ]
                    ),

                    pw.Text(
                      projectTitle,
                      style: pw.TextStyle(
                        fontSize: 16,
                        color: PdfColors.black,
                        font: ralewayBold
                      )
                    )
                  ]
                )
              ),
              pw.SizedBox(height: 30),
              pw.Container(
                height: 680,
                width: 1200,
                child: pw.Stack(
                  children: [
                    if( photoBeforeAfterPathsImages[i]['before'] != '' ) pw.Positioned(
                      child: pw.Container(
                        width: 821,
                        height: 591,
                        decoration: pw.BoxDecoration(
                          color: PdfColors.amber,
                          borderRadius: pw.BorderRadius.circular(10),
                          image: pw.DecorationImage(image: photoBeforeAfterPathsImages[i]['before'], fit: pw.BoxFit.cover )
                        ),
                        child: pw.Stack(
                          children: [
                            pw.Positioned(
                              right: 15,
                              top: 15,
                              child: pw.Container(
                                height: 32,
                                padding: pw.EdgeInsets.symmetric( horizontal: 10 ),
                                decoration: pw.BoxDecoration(
                                  color: PdfColors.white,
                                  borderRadius: pw.BorderRadius.circular(16),
                                ),
                                child: pw.Center(
                                  child: pw.Text(
                                    'Progetto',
                                    overflow: pw.TextOverflow.visible,
                                    style: pw.TextStyle(
                                      height: 14,
                                      fontSize: 13,
                                      color: PdfColor.fromHex('#000000'),
                                      font: ralewayMedium
                                    )
                                  )
                                )
                              )
                            )
                          ]
                        )
                      ),
                      left: 0,
                      top: 0
                    ),
                    if( photoBeforeAfterPathsImages[i]['after'] != '' ) pw.Positioned(
                      child: pw.Container(
                        width: 456,
                        height: 300,
                        decoration: pw.BoxDecoration(
                          color: PdfColors.amber100,
                          borderRadius: pw.BorderRadius.circular(10),
                          image: pw.DecorationImage(image: photoBeforeAfterPathsImages[i]['after'], fit: pw.BoxFit.cover )
                        ),
                        child: pw.Stack(
                          children: [
                            pw.Positioned(
                              right: 15,
                              top: 15,
                              child: pw.Container(
                                height: 32,
                                padding: pw.EdgeInsets.symmetric( horizontal: 10 ),
                                decoration: pw.BoxDecoration(
                                  color: PdfColors.white,
                                  borderRadius: pw.BorderRadius.circular(16),
                                ),
                                child: pw.Center(
                                  child: pw.Text(
                                    'Attuale',
                                    overflow: pw.TextOverflow.visible,
                                    style: pw.TextStyle(
                                      height: 14,
                                      fontSize: 13,
                                      color: PdfColor.fromHex('#000000'),
                                      font: ralewayMedium
                                    )
                                  )
                                )
                              )
                            )
                          ]
                        )
                      ),
                      bottom: 0,
                      right: 0
                    ),

                  ]
                )
              )

            ]
          )
        )
      );
    }
    progress?.value = 0.75;

    // Project Images
    counterAI = 0;
    for (var i = 0; i < renderImageDayList.length; i++) {

      if( photoDayTimePathsImages[i] != '' ) pdfPage1.add(
        pw.Container(
          height: 820,
          padding: pw.EdgeInsets.symmetric(vertical: 20, horizontal: 45 ),
          child: pw.Column(
            children: [
              pw.Container(
                decoration: pw.BoxDecoration(
                  border: pw.Border(
                    bottom: pw.BorderSide( width: 1, color: PdfColors.black )
                  )
                ),
                padding: pw.EdgeInsets.only(bottom: 10),
                child: pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: pw.CrossAxisAlignment.center,
                  children: [
                    pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        pw.Text(
                          'PROGETTO',
                          style: pw.TextStyle(
                            fontSize: 15,
                            color: PdfColor.fromHex('#888888'),
                            font: ralewayMedium
                          )
                        ),
                        pw.Text(
                          renderImageDayList[i].renderImageFirst!.room!,
                          style: pw.TextStyle(
                            fontSize: 30,
                            color: PdfColors.black,
                            font: ralewayBold
                          )
                        ),
                      ]
                    ),

                    pw.Text(
                      projectTitle,
                      style: pw.TextStyle(
                        fontSize: 16,
                        color: PdfColors.black,
                        font: ralewayBold
                      )
                    )
                  ]
                )
              ),
              pw.SizedBox(height: 30),
              pw.Container(
                height: 680,
                width: 1200,
                child: pw.Stack(
                  children: [
                    pw.Positioned(
                      child: pw.Container(
                        width: 1200,
                        height: 680,
                        decoration: pw.BoxDecoration(
                          color: PdfColors.amber,
                          borderRadius: pw.BorderRadius.circular(10),
                          image: pw.DecorationImage(image: photoDayTimePathsImages[i], fit: pw.BoxFit.cover )
                        ),
                        child: pw.Stack(
                          children: [
                            pw.Positioned(
                              right: 15,
                              top: 15,
                              child: pw.Container(
                                height: 32,
                                padding: pw.EdgeInsets.symmetric( horizontal: 10 ),
                                decoration: pw.BoxDecoration(
                                  color: PdfColors.white,
                                  borderRadius: pw.BorderRadius.circular(16),
                                ),
                                child: pw.Center(
                                  child: pw.Text(
                                    'Progetto',
                                    overflow: pw.TextOverflow.visible,
                                    style: pw.TextStyle(
                                      height: 14,
                                      fontSize: 13,
                                      color: PdfColor.fromHex('#000000'),
                                      font: ralewayMedium
                                    )
                                  )
                                )
                              )
                            )
                          ]
                        )
                      ),
                      left: 0,
                      top: 0
                    ),


                  ]
                )
              )

            ]
          )
        )
      );
    }

    progress?.value = 0.80;

    // Virtual Tour Page
    counterAI = 0;
    pdfPage1.add(
      pw.Padding(
        padding: pw.EdgeInsets.symmetric(vertical: 20, horizontal: 45 ),
        child: pw.Column(
          children: [
            pw.Container(
              decoration: pw.BoxDecoration(
                border: pw.Border(
                  bottom: pw.BorderSide( width: 1, color: PdfColors.black )
                )
              ),
              padding: pw.EdgeInsets.only(bottom: 10),
              child: pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                crossAxisAlignment: pw.CrossAxisAlignment.center,
                children: [
                  pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                        'PROGETTO',
                        style: pw.TextStyle(
                          fontSize: 15,
                          color: PdfColor.fromHex('#888888'),
                          font: ralewayMedium
                        )
                      ),
                      pw.Text(
                        'Virtual Tour',
                        style: pw.TextStyle(
                          fontSize: 30,
                          color: PdfColors.black,
                          font: ralewayBold
                        )
                      ),
                    ]
                  ),

                  pw.Text(
                    projectTitle,
                    style: pw.TextStyle(
                      fontSize: 16,
                      color: PdfColors.black,
                      font: ralewayBold
                    )
                  )
                ]
              )
            ),
            pw.SizedBox(height: 30),
            pw.Container(
              height: 680,
              width: 1200,
              child: pw.Stack(
                children: [
                  pw.Positioned(
                    child: pw.Container(
                      width: 1200,
                      height: 680,
                      decoration: pw.BoxDecoration(
                        borderRadius: pw.BorderRadius.circular(10),
                        image: pw.DecorationImage(image: vtImage, fit: pw.BoxFit.cover )
                      ),
                      child: pw.Stack(
                        children: [
                          pw.Opacity(
                            opacity: 0.2,
                            child: pw.Container(
                              width: 1200,
                              height: 680,
                              decoration: pw.BoxDecoration(
                                borderRadius: pw.BorderRadius.circular(10),
                                color: PdfColors.black
                              ),

                            ),
                          ),
                          pw.Container(
                            width: 1200,
                            height: 680,
                            child: pw.Center(
                              child: pw.Column(
                                mainAxisAlignment: pw.MainAxisAlignment.center,
                                children: [
                                  pw.UrlLink(
                                    child: pw.Container(
                                      height: 100,
                                      width: 100,
                                      // padding: pw.EdgeInsets.all(5),
                                      decoration: pw.BoxDecoration(
                                        // color: PdfColors.black,
                                        // borderRadius: pw.BorderRadius.circular(50),
                                      ),
                                      child: pw.Image(
                                        playButton,
                                        height: 100,
                                        width: 100,
                                        fit: pw.BoxFit.cover

                                      )
                                    ),
                                    destination: adData.virtualTour!
                                  ),
                                  pw.SizedBox(height: 10),
                                  pw.UrlLink(
                                    child: pw.Text(
                                      'Clicca ed entra in casa',
                                      style: pw.TextStyle(
                                        decoration: pw.TextDecoration.underline,
                                        fontSize: 16,
                                        font: ralewayBold,
                                        color: PdfColors.white
                                      )
                                    ),
                                    destination: adData.virtualTour!
                                  )

                                ]
                              )
                            )
                          ),
                          pw.Positioned(
                            right: 15,
                            bottom: 15,
                            child: pw.Opacity(
                              opacity: 0.5,
                              child: pw.Container(
                                width: 335,
                                height: 180,

                                padding: pw.EdgeInsets.symmetric( horizontal: 10 ),
                                decoration: pw.BoxDecoration(
                                  color: PdfColors.white,
                                  borderRadius: pw.BorderRadius.circular(10),
                                ),

                              )
                            )

                          ),
                          pw.Positioned(
                              right: 15,
                              bottom: 15,
                              child: pw.Container(
                                  width: 335,
                                  height: 180,
                                  padding: pw.EdgeInsets.symmetric(
                                      horizontal: 15, vertical: 5),
                                  decoration: pw.BoxDecoration(
                                      // color: PdfColors.white,
                                      // borderRadius: pw.BorderRadius.circular(16),
                                      ),
                                  child: pw.Center(
                                      child: pw.Row(children: [
                                    pw.Expanded(
                                        child: pw.Padding(
                                            padding: pw.EdgeInsets.all(10),
                                            child: pw.Text(
                                                'Scansiona il QR code per vivere la casa con il tuo dispositivo mobile.',
                                                overflow:
                                                    pw.TextOverflow.visible,
                                                style: pw.TextStyle(
                                                    height: 20,
                                                    fontSize: 16,
                                                    color: PdfColor.fromHex(
                                                        '#ffffff'),
                                                    font: ralewayMedium)))),
                                    pw.SizedBox(width: 20),
                                    pw.Expanded(
                                      child: pw.Container(
                                        padding: pw.EdgeInsets.symmetric(
                                            vertical: 10, horizontal: 10),
                                        decoration: pw.BoxDecoration(
                                          color: PdfColors.white,
                                          borderRadius:
                                              pw.BorderRadius.circular(10),
                                        ),
                                        child: pw.BarcodeWidget(
                                          barcode: pw.Barcode.qrCode(),
                                          data: qrImageUrl,
                                          width: 120,
                                          height: 120,
                                        ),
                                      ),
                                    )
                                  ])))),
                        ])),
                    left: 0,
                    top: 0),
              ]))
        ])));

        

    progress?.value = 0.85;

    // Restoration Page

    pdfPage1.add(
      pw.Container(
        height: 820,
        padding: pw.EdgeInsets.symmetric(vertical: 20, horizontal: 45 ),
        child: pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          mainAxisSize: pw.MainAxisSize.max,
          children: [
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              mainAxisAlignment: pw.MainAxisAlignment.start,
              children: [


                pw.Container(
                  decoration: pw.BoxDecoration(
                    border: pw.Border(
                      bottom: pw.BorderSide( width: 1, color: PdfColors.black )
                    )
                  ),
                  padding: pw.EdgeInsets.only(bottom: 10),
                  child: pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: pw.CrossAxisAlignment.center,
                    children: [
                      pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          pw.Text(
                            'Ristrutturazione',
                            style: pw.TextStyle(
                              fontSize: 30,
                              color: PdfColors.black,
                              font: ralewayBold
                            )
                          ),
                        ]
                      ),

                      pw.Text(
                        projectTitle,
                        style: pw.TextStyle(
                          fontSize: 16,
                          color: PdfColors.black,
                          font: ralewayBold
                        )
                      )
                    ]
                  )
                ),
                pw.SizedBox(height: 30),
                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Expanded(
                      flex: 5,
                      child: pw.Column(
                        mainAxisAlignment: pw.MainAxisAlignment.start,
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          pw.Text(
                            'Lavori',
                            style: pw.TextStyle(
                              fontSize: 18,
                              color: PdfColor.fromHex('#000000'),
                              font: ralewayMedium
                            )
                          ),
                          pw.SizedBox(height: 20),
                          pw.Row(
                            mainAxisAlignment: pw.MainAxisAlignment.start,
                            crossAxisAlignment: pw.CrossAxisAlignment.start,
                            children: [
                              pw.Expanded(
                                child: pw.Container(
                                  height: 550,
                                  decoration: pw.BoxDecoration(
                                    borderRadius: pw.BorderRadius.circular(10),
                                    color: PdfColor.fromHex('#66B393')
                                  ),
                                  child: pw.Column(
                                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                                    children: [
                                      pw.Padding(
                                        padding: pw.EdgeInsets.symmetric(vertical: 20, horizontal: 10),
                                        child: pw.Row(
                                          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                                          children: [
                                            pw.Row(
                                              crossAxisAlignment: pw.CrossAxisAlignment.start,
                                              children: [
                                                pw.Image(
                                                  hammerIcon,
                                                  height: 25,
                                                  width: 25
                                                ),
                                                pw.SizedBox(width: 10),
                                                pw.Container(
                                                  width: 345,
                                                  child: pw.Column(
                                                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                                                    children: [
                                                      pw.SizedBox(height: 3),
                                                      pw.Row(
                                                        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                                        mainAxisSize: pw.MainAxisSize.max,
                                                        children: [

                                                          pw.Text(
                                                            'Leggera',
                                                            style: pw.TextStyle(
                                                              fontSize: 16,
                                                              color: PdfColors.white,
                                                              font: ralewayMedium
                                                            )
                                                          ),

                                                          pw.Row(
                                                            children: [
                                                              pw.Text(
                                                                '${localCurrencyFormatMain.format(double.tryParse(adData.LighRenoInsMin.toString()))}-${localCurrencyFormatMain.format(double.tryParse(adData.LighRenoInsMax.toString()))}€',
                                                                style: pw.TextStyle(
                                                                  fontSize: 18,
                                                                  color: PdfColors.white,
                                                                  font: ralewayBold
                                                                )
                                                              ),
                                                              pw.Text(
                                                                '+iva',
                                                                style: pw.TextStyle(
                                                                  fontSize: 13,
                                                                  color: PdfColors.white,
                                                                  font: ralewayBold
                                                                )
                                                              ),
                                                            ]
                                                          )
                                                        ]
                                                      ),
                                                      pw.SizedBox(height: 30),
                                                      ...standardFeatures.map((e){
                                                        return pw.Column(
                                                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                                                          children: [
                                                            pw.Text(
                                                            e,
                                                            style: pw.TextStyle(
                                                              fontSize: 14,
                                                              color: PdfColors.white,
                                                              font: ralewayMedium
                                                            )
                                                          ),
                                                          pw.SizedBox(height: 8),
                                                          ]
                                                        );
                                                      }).toList(),

                                                    ]
                                                  ),

                                                )
                                                
                                              ]
                                            ),
                                            // pw.SizedBox(width: 10),
                                            /* pw.Column(
                                              children: [
                                                pw.SizedBox(height: 1),
                                                

                                              ]
                                            ) */


                                          ]
                                        )
                                      ),
                                      pw.Row(
                                        children: [
                                          pw.Expanded(
                                            child: pw.Container(
                                              padding: pw.EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                                              decoration: pw.BoxDecoration(
                                                border: pw.Border(
                                                  top: pw.BorderSide( width: 1, color: PdfColor.fromHex('#AFD8C7') ),
                                                  right: pw.BorderSide( width: 1, color: PdfColor.fromHex('#AFD8C7') ),
                                                )
                                              ),
                                              child: pw.Column(
                                                crossAxisAlignment: pw.CrossAxisAlignment.end,
                                                children: [
                                                  pw.Row(
                                                    crossAxisAlignment: pw.CrossAxisAlignment.center,
                                                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                                    children: [
                                                      pw.Image(
                                                        materialStandardIcon,
                                                        height: 25,
                                                        width: 25
                                                      ),
                                                      pw.Text(
                                                        'Materiali Standard',
                                                        style: pw.TextStyle(
                                                          fontSize: 16,
                                                          color: PdfColors.white,
                                                          font: ralewayMedium
                                                        )
                                                      ),
                                                    ],

                                                  ),
                                                  pw.Row(
                                                    mainAxisAlignment: pw.MainAxisAlignment.end,
                                                    children: [
                                                      pw.Text(
                                                        '${localCurrencyFormatMain.format(double.tryParse(adData.materialStandardLightMin.toString()))}-${localCurrencyFormatMain.format(double.tryParse(adData.materialStandardLightMax.toString()))}€',
                                                        style: pw.TextStyle(
                                                          fontSize: 16,
                                                          color: PdfColors.white,
                                                          font: ralewayBold
                                                        )
                                                      ),
                                                      pw.Text(
                                                        '+iva',
                                                        style: pw.TextStyle(
                                                          fontSize: 13,
                                                          color: PdfColors.white,
                                                          font: ralewayBold
                                                        )
                                                      ),
                                                    ]
                                                  )
                                                ]
                                              )
                                            )

                                          ),
                                          pw.Expanded(
                                            child: pw.Container(
                                              padding: pw.EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                                              decoration: pw.BoxDecoration(
                                                border: pw.Border(
                                                  top: pw.BorderSide( width: 1, color: PdfColor.fromHex('#AFD8C7') ),
                                                )
                                              ),
                                              child: pw.Column(
                                                crossAxisAlignment: pw.CrossAxisAlignment.end,
                                                children: [
                                                  pw.Row(
                                                    crossAxisAlignment: pw.CrossAxisAlignment.center,
                                                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                                    children: [
                                                      pw.Image(
                                                        materialPremiumIcon,
                                                        height: 25,
                                                        width: 31
                                                      ),
                                                      pw.Text(
                                                        'Materiali Premium',
                                                        style: pw.TextStyle(
                                                          fontSize: 16,
                                                          color: PdfColors.white,
                                                          font: ralewayMedium
                                                        )
                                                      ),
                                                    ],

                                                  ),
                                                  pw.Row(
                                                    mainAxisAlignment: pw.MainAxisAlignment.end,
                                                    children: [
                                                      pw.Text(
                                                        '${localCurrencyFormatMain.format(double.tryParse(adData.materialPremiumLightMin.toString()))}-${localCurrencyFormatMain.format(double.tryParse(adData.materialPremiumLightMax.toString()))}€',
                                                        style: pw.TextStyle(
                                                          fontSize: 16,
                                                          color: PdfColors.white,
                                                          font: ralewayBold
                                                        )
                                                      ),
                                                      pw.Text(
                                                        '+iva',
                                                        style: pw.TextStyle(
                                                          fontSize: 13,
                                                          color: PdfColors.white,
                                                          font: ralewayBold
                                                        )
                                                      ),
                                                    ]
                                                  )
                                                ]
                                              )
                                            )

                                          ),
                                        ]
                                      )
                                    ]
                                  )
                                )
                              ),

                              pw.SizedBox(width:30),

                              pw.Expanded(
                                child: pw.Container(
                                  height: 550,
                                  decoration: pw.BoxDecoration(
                                    borderRadius: pw.BorderRadius.circular(10),
                                    color: PdfColor.fromHex('#449272')
                                  ),
                                  child: pw.Column(
                                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                                    children: [
                                      pw.Padding(
                                        padding: pw.EdgeInsets.symmetric(vertical: 20, horizontal: 10),
                                        child: pw.Row(
                                          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                                          children: [
                                            pw.Row(
                                              crossAxisAlignment: pw.CrossAxisAlignment.start,
                                              children: [
                                                pw.Image(
                                                  hammerIcon,
                                                  height: 25,
                                                  width: 25
                                                ),
                                                pw.SizedBox(width: 10),
                                                pw.Container(
                                                  width: 345,
                                                  child: pw.Column(
                                                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                                                    children: [
                                                      pw.SizedBox(height: 3),
                                                      pw.Row(
                                                        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                                        mainAxisSize: pw.MainAxisSize.max,
                                                        children: [
                                                          pw.Text(
                                                            'Completa',
                                                            style: pw.TextStyle(
                                                              fontSize: 16,
                                                              color: PdfColors.white,
                                                              font: ralewayMedium
                                                            )
                                                          ),

                                                          pw.Row(
                                                            children: [
                                                              pw.Text(
                                                                '${localCurrencyFormatMain.format(double.tryParse(adData.FullRenoInsMin.toString()))}-${localCurrencyFormatMain.format(double.tryParse(adData.FullRenoInsMax.toString()))}€',
                                                                style: pw.TextStyle(
                                                                  fontSize: 18,
                                                                  color: PdfColors.white,
                                                                  font: ralewayBold
                                                                )
                                                              ),
                                                              pw.Text(
                                                                '+iva',
                                                                style: pw.TextStyle(
                                                                  fontSize: 13,
                                                                  color: PdfColors.white,
                                                                  font: ralewayBold
                                                                )
                                                              ),
                                                            ]
                                                          )
                                                        ]
                                                      ),
                                                      pw.SizedBox(height: 30),
                                                      ...premiumFeatures.map((e){
                                                        return pw.Column(
                                                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                                                          children: [
                                                            pw.Text(
                                                            e,
                                                            style: pw.TextStyle(
                                                              fontSize: 14,
                                                              color: PdfColors.white,
                                                              font: ralewayMedium
                                                            )
                                                          ),
                                                          pw.SizedBox(height: 8),
                                                          ]
                                                        );
                                                      }).toList(),

                                                    ]
                                                  ),

                                                )
                                                
                                              ]
                                            ),
                                            // pw.SizedBox(width: 10),
                                            

                                          ]
                                        )
                                      ),
                                      pw.Column(
                                        children: [
                                          pw.Container(
                                            padding: pw.EdgeInsets.all(10),
                                            decoration: pw.BoxDecoration(
                                              border: pw.Border(
                                                top: pw.BorderSide( width: 1, color: PdfColor.fromHex('#AFD8C7') ),
                                              )
                                            ),
                                            child: pw.Row(
                                              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                              crossAxisAlignment: pw.CrossAxisAlignment.center,
                                              children: [
                                                pw.Row(
                                                  children: [
                                                    pw.Image(
                                                      infissiDataIcon,
                                                      height: 25,
                                                      width: 25
                                                    ),
                                                    pw.SizedBox(width: 20),
                                                    pw.Text(
                                                      'Infissi',
                                                      style: pw.TextStyle(
                                                        fontSize: 16,
                                                        color: PdfColors.white,
                                                        font: ralewayMedium
                                                      )
                                                    ),
                                                  ],

                                                ),

                                                pw.Row(
                                                  mainAxisAlignment: pw.MainAxisAlignment.end,
                                                  children: [
                                                    pw.Text(
                                                      '${localCurrencyFormatMain.format(double.tryParse(adData.materialFixtureFullMin.toString()))}-${localCurrencyFormatMain.format(double.tryParse(adData.materialFixtureFullMax.toString()))}€',
                                                      style: pw.TextStyle(
                                                        fontSize: 16,
                                                        color: PdfColors.white,
                                                        font: ralewayBold
                                                      )
                                                    ),
                                                    pw.Text(
                                                      '+iva',
                                                      style: pw.TextStyle(
                                                        fontSize: 13,
                                                        color: PdfColors.white,
                                                        font: ralewayBold
                                                      )
                                                    ),
                                                  ]
                                                )
                                              ]
                                            )

                                          ),
                                          pw.Row(
                                            children: [
                                              pw.Expanded(
                                                child: pw.Container(
                                                  padding: pw.EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                                                  decoration: pw.BoxDecoration(
                                                    border: pw.Border(
                                                      top: pw.BorderSide( width: 1, color: PdfColor.fromHex('#AFD8C7') ),
                                                      right: pw.BorderSide( width: 1, color: PdfColor.fromHex('#AFD8C7') ),
                                                    )
                                                  ),
                                                  child: pw.Column(
                                                    crossAxisAlignment: pw.CrossAxisAlignment.end,
                                                    children: [
                                                      pw.Row(
                                                        crossAxisAlignment: pw.CrossAxisAlignment.center,
                                                        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                                        children: [
                                                          pw.Image(
                                                            materialStandardIcon,
                                                            height: 25,
                                                            width: 25
                                                          ),
                                                          pw.Text(
                                                            'Materiali Standard',
                                                            style: pw.TextStyle(
                                                              fontSize: 16,
                                                              color: PdfColors.white,
                                                              font: ralewayMedium
                                                            )
                                                          ),
                                                        ],

                                                      ),
                                                      pw.Row(
                                                        mainAxisAlignment: pw.MainAxisAlignment.end,
                                                        children: [
                                                          pw.Text(
                                                            '${localCurrencyFormatMain.format(double.tryParse(adData.materialStandardFullMin.toString()) )}-${localCurrencyFormatMain.format(double.tryParse(adData.materialStandardFullMax.toString()) )}€',
                                                            style: pw.TextStyle(
                                                              fontSize: 16,
                                                              color: PdfColors.white,
                                                              font: ralewayBold
                                                            )
                                                          ),
                                                          pw.Text(
                                                            '+iva',
                                                            style: pw.TextStyle(
                                                              fontSize: 13,
                                                              color: PdfColors.white,
                                                              font: ralewayBold
                                                            )
                                                          ),
                                                        ]
                                                      )
                                                    ]
                                                  )
                                                )

                                              ),
                                              pw.Expanded(
                                                child: pw.Container(
                                                  padding: pw.EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                                                  decoration: pw.BoxDecoration(
                                                    border: pw.Border(
                                                      top: pw.BorderSide( width: 1, color: PdfColor.fromHex('#AFD8C7') ),
                                                    )
                                                  ),
                                                  child: pw.Column(
                                                    crossAxisAlignment: pw.CrossAxisAlignment.end,
                                                    children: [
                                                      pw.Row(
                                                        crossAxisAlignment: pw.CrossAxisAlignment.center,
                                                        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                                        children: [
                                                          pw.Image(
                                                            materialPremiumIcon,
                                                            height: 25,
                                                            width: 31
                                                          ),
                                                          pw.Text(
                                                            'Materiali Premium',
                                                            style: pw.TextStyle(
                                                              fontSize: 16,
                                                              color: PdfColors.white,
                                                              font: ralewayMedium
                                                            )
                                                          ),
                                                        ],

                                                      ),
                                                      pw.Row(
                                                        mainAxisAlignment: pw.MainAxisAlignment.end,
                                                        children: [
                                                          pw.Text(
                                                            '${localCurrencyFormatMain.format( double.tryParse(adData.materialPremiumFullMin.toString()))}-${localCurrencyFormatMain.format( double.tryParse(adData.materialPremiumFullMax.toString()))}€',
                                                            style: pw.TextStyle(
                                                              fontSize: 16,
                                                              color: PdfColors.white,
                                                              font: ralewayBold
                                                            )
                                                          ),
                                                          pw.Text(
                                                            '+iva',
                                                            style: pw.TextStyle(
                                                              fontSize: 13,
                                                              color: PdfColors.white,
                                                              font: ralewayBold
                                                            )
                                                          ),
                                                        ]
                                                      )
                                                    ]
                                                  )
                                                )

                                              ),
                                            ]
                                          )
                                        ]
                                      ),

                                    ]
                                  )
                                )
                              )
                            ]
                          )
                        ]
                      )

                    ),

                    pw.SizedBox(width: 50),

                    pw.Expanded(
                      flex: 2,
                      child: pw.Column(
                        mainAxisAlignment: pw.MainAxisAlignment.start,
                        mainAxisSize: pw.MainAxisSize.max,
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          pw.Text(
                            'Classe energetica',
                            style: pw.TextStyle(
                              fontSize: 18,
                              color: PdfColor.fromHex('#000000'),
                              font: ralewayMedium
                            )
                          ),
                          pw.SizedBox(height: 3),
                          pw.Text(
                            'Miglioramento stimato sostituendo gli infissi.',
                            style: pw.TextStyle(
                              fontSize: 10,
                              color: PdfColor.fromHex('#8E8E8E'),
                              fontItalic: ralewayItalic
                            )
                          ),
                          pw.SizedBox(height: 5),
                          pw.Row(
                            children: [
                              pw.Expanded(
                                child: pw.Container(
                                  height: 550,
                                  width: 300,
                                  decoration: pw.BoxDecoration(
                                    borderRadius: pw.BorderRadius.circular(10),
                                    // color: PdfColor.fromHex('#66B393')
                                  ),
                                  child: pw.Column(
                                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                                    children: [

                                      pw.Row(
                                        crossAxisAlignment: pw.CrossAxisAlignment.center,
                                        children: [
                                          pw.Container(
                                            height: 78,
                                            width: 125,
                                            padding: pw.EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                                            decoration: pw.BoxDecoration(
                                              borderRadius: pw.BorderRadius.circular(10),
                                              border: pw.Border.all(
                                                width: 1,
                                                color: PdfColor.fromHex('#D3D3D3')
                                              )

                                            ),

                                            child: pw.Row(
                                              children: [
                                                pw.Image(
                                                  arrowHouseIcon,
                                                  width: 54,
                                                  height: 40,
                                                ),
                                                pw.Container(
                                                  height: 43,
                                                  width: 43,
                                                  decoration: pw.BoxDecoration(
                                                    color: getEnergyColorCode(adData.actualEnergyClass!),
                                                    borderRadius: pw.BorderRadius.circular(21.5)
                                                  ),
                                                  child: pw.Center(
                                                    child: pw.Text(
                                                      adData.actualEnergyClass!,
                                                      style: pw.TextStyle(
                                                        font: ralewayBold,
                                                        fontSize: 30,
                                                        color: PdfColors.white
                                                      )
                                                    )
                                                  )

                                                )
                                              ]
                                            )
                                          ),
                                          pw.Container(
                                            height: 97,
                                            width: 10,
                                            child: pw.Stack(
                                              overflow: pw.Overflow.visible,

                                              children: [
                                                pw.Positioned(
                                                  child: pw.Image(
                                                    arrowIcon,
                                                    width: 37,
                                                    height: 28,
                                                  ),
                                                  top: 33,
                                                  left: -7
                                                )
                                              ]
                                            )
                                          ),
                                          pw.Container(
                                            height: 97,
                                            width: 160,
                                            padding: pw.EdgeInsets.only(right: 15, left: 30, top: 10, bottom: 10),
                                            decoration: pw.BoxDecoration(
                                              borderRadius: pw.BorderRadius.circular(10),
                                              border: pw.Border.all(
                                                width: 1,
                                                color: PdfColor.fromHex('#D3D3D3')
                                              )

                                            ),

                                            child: pw.Row(
                                              children: [
                                                pw.Image(
                                                  arrowHouseIcon,
                                                  width: 64,
                                                  height: 48,
                                                ),
                                                pw.Container(
                                                  height: 43,
                                                  width: 43,
                                                  decoration: pw.BoxDecoration(
                                                    color: getEnergyColorCode(adData.projectEnergyClass!),
                                                    borderRadius: pw.BorderRadius.circular(21.5)
                                                  ),
                                                  child: pw.Center(
                                                    child: pw.Text(
                                                      adData.projectEnergyClass!,
                                                      style: pw.TextStyle(
                                                        font: ralewayBold,
                                                        fontSize: 30,
                                                        color: PdfColors.white
                                                      )
                                                    )
                                                  )

                                                )
                                              ]
                                            )
                                          )

                                        ]
                                      ),


                                      pw.Column(
                                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                                        children: [
                                          pw.Text(
                                            'Bonus ristrutturazioni 2025',
                                            style: pw.TextStyle(
                                              fontSize: 18,
                                              color: PdfColor.fromHex('#000000'),
                                              font: ralewayMedium
                                            )
                                          ),
                                          pw.SizedBox(height: 10),
                                          pw.Container(
                                            decoration: pw.BoxDecoration(
                                              border: pw.Border.all(
                                                width: 1,
                                                color: PdfColor.fromHex('#D3D3D3'),
                                              ),
                                              borderRadius: pw.BorderRadius.circular(10)
                                            ),
                                            padding: pw.EdgeInsets.symmetric(vertical: 15, horizontal: 10),
                                            child: pw.Column(
                                              crossAxisAlignment: pw.CrossAxisAlignment.start,
                                              children: [
                                                pw.Row(
                                                  mainAxisAlignment: pw.MainAxisAlignment.start,
                                                  crossAxisAlignment: pw.CrossAxisAlignment.center,
                                                  children: [
                                                    pw.Container(
                                                      height: 41,
                                                      width: 41,
                                                      decoration: pw.BoxDecoration(
                                                        borderRadius: pw.BorderRadius.circular(20.5),
                                                        color: PdfColor.fromHex('#489B79')
                                                      ),
                                                      margin: pw.EdgeInsets.only(right:10),
                                                      child: pw.Center(
                                                        child: pw.Text(
                                                          '50%',
                                                          style: pw.TextStyle(
                                                            fontSize: 17,
                                                            color: PdfColor.fromHex('#ffffff'),
                                                            font: ralewayBold
                                                          )
                                                        ),
                                                      )

                                                    ),
                                                    pw.Text(
                                                      'Sulla prima casa',
                                                      style: pw.TextStyle(
                                                        fontSize: 16,
                                                        color: PdfColor.fromHex('#489B79'),
                                                        font: ralewayBold
                                                      )
                                                    ),


                                                  ]
                                                ),
                                                pw.Row(
                                                  mainAxisAlignment: pw.MainAxisAlignment.start,
                                                  crossAxisAlignment: pw.CrossAxisAlignment.center,
                                                  children: [
                                                    pw.Container(
                                                      height: 36,
                                                      width: 36,
                                                      decoration: pw.BoxDecoration(
                                                        borderRadius: pw.BorderRadius.circular(18),
                                                        color: PdfColor.fromHex('#489B79')

                                                      ),
                                                      margin: pw.EdgeInsets.only(right:10, left: 40),
                                                      child: pw.Center(
                                                        child: pw.Text(
                                                          '36%',
                                                          style: pw.TextStyle(
                                                            fontSize: 14,
                                                            color: PdfColor.fromHex('#ffffff'),
                                                            font: ralewayBold
                                                          )
                                                        ),
                                                      )

                                                    ),
                                                    pw.Text(
                                                      'Sulla seconda casa',
                                                      style: pw.TextStyle(
                                                        fontSize: 14,
                                                        color: PdfColor.fromHex('#489B79'),
                                                        font: ralewayBold
                                                      )
                                                    ),


                                                  ]
                                                ),
                                                pw.SizedBox(height: 15),
                                                pw.Text(
                                                  'Il bonus fiscale consiste in una detrazione dall’Irpef, da ripartire in 10 quote annuali di pari importo, del 50% delle spese sostenute sulla prima casa fino ad un massimo di 96.000€ e 36% sulla seconda casa fino ad un massimo di 48.000€.',
                                                  overflow: pw.TextOverflow.visible,
                                                  textAlign: pw.TextAlign.justify,
                                                  style: pw.TextStyle(

                                                    fontSize: 11,
                                                    height: 16,
                                                    letterSpacing: 0.01,
                                                    color: PdfColor.fromHex('#737373'),
                                                    font: ralewayMedium
                                                  )
                                                ),
                                              ]

                                            )
                                          ),

                                        ]
                                      ),

                                      pw.Column(
                                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                                        children: [
                                          pw.Text(
                                            'Tempistiche',
                                            style: pw.TextStyle(
                                              fontSize: 18,
                                              color: PdfColor.fromHex('#000000'),
                                              font: ralewayMedium
                                            )
                                          ),
                                          pw.SizedBox(height: 10),
                                          pw.Container(
                                            decoration: pw.BoxDecoration(
                                              border: pw.Border.all(
                                                width: 1,
                                                color: PdfColor.fromHex('#D3D3D3'),
                                              ),
                                              borderRadius: pw.BorderRadius.circular(10)
                                            ),
                                            padding: pw.EdgeInsets.symmetric(vertical: 15, horizontal: 20),
                                            child: pw.Column(
                                              crossAxisAlignment: pw.CrossAxisAlignment.start,
                                              children: [
                                                pw.Row(
                                                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                                  crossAxisAlignment: pw.CrossAxisAlignment.center,
                                                  children: [
                                                    pw.Text(
                                                      'Durata presunta cantiere',
                                                      style: pw.TextStyle(
                                                        fontSize: 14,
                                                        color: PdfColor.fromHex('#737373'),
                                                        font: ralewayMedium
                                                      )
                                                    ),
                                                    pw.Container(
                                                      height: 35,
                                                      width: 35,
                                                      decoration: pw.BoxDecoration(
                                                        image: pw.DecorationImage(image: workDurationIcon, fit: pw.BoxFit.cover)
                                                      ),

                                                    ),
                                                  ]
                                                ),
                                                pw.SizedBox(height: 15),
                                                pw.Row(
                                                  mainAxisAlignment: pw.MainAxisAlignment.start,
                                                  crossAxisAlignment: pw.CrossAxisAlignment.center,
                                                  children: [
                                                    pw.Text(
                                                      '75 giorni lavorativi',
                                                      style: pw.TextStyle(
                                                        fontSize: 22,
                                                        color: PdfColor.fromHex('#000000'),
                                                        font: ralewayBold
                                                      )
                                                    ),
                                                  ]
                                                ),
                                                pw.SizedBox(height: 5),
                                                pw.Text(
                                                  'da inizio lavori',
                                                  overflow: pw.TextOverflow.visible,
                                                  textAlign: pw.TextAlign.justify,
                                                  style: pw.TextStyle(

                                                    fontSize: 14,
                                                    color: PdfColor.fromHex('#000000'),
                                                    font: ralewayMedium
                                                  )
                                                ),
                                              ]

                                            )
                                          )
                                        ]
                                      )


                                    ]
                                  )
                                )
                              )
                            ]
                          )
                        ]
                      )


                    )

                  ]
                ),

                pw.SizedBox(height: 20),

              ]
            ),
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [

                pw.Text(
                  'La presente quotazione è una stima e potrebbe subire variazioni in sede di preventivazione definitiva. Si consiglia di venire in sede per avere un preventivo gratuito e personalizzato. ',
                  overflow: pw.TextOverflow.visible,
                  textAlign: pw.TextAlign.justify,
                  style: pw.TextStyle(

                    fontSize: 12,
                    height: 18,
                    letterSpacing: 0.01,
                    color: PdfColor.fromHex('#4B4B4B'),
                    font: ralewayBold
                  )
                ),
                pw.SizedBox(height: 5),

                pw.Text(
                'Nella ristrutturazione leggera i materiali inclusi sono: porte interne, pavimenti e rivestimenti bagno, rubinetterie, sanitari, kit doccia incasso, piatti doccia, box doccia, mobili bagno e termoarredi. Nella ristrutturazione completa oltre ai materiali precedenti sono inclusi anche i pavimenti di tutta casa, battiscopa, materiale elettrico e illuminotecnico. I materiali Standard sono i materiali da capitolato di buona finitura e dall’ottimo rapporto qualità/prezzo. I materiali Premium sono materiali di prima scelta e dal massimo livello qualitativo.',
                overflow: pw.TextOverflow.visible,
                textAlign: pw.TextAlign.justify,
                style: pw.TextStyle(

                  fontSize: 10,
                  height: 13,
                  letterSpacing: 0.02,
                  color: PdfColor.fromHex('#8E8E8E'),
                  font: ralewayMedium
                )
              ),
              ]
            )
          ]
        )
      )
    );

    progress?.value = 0.90;

    pdf.addPage(
        pw.MultiPage(
          theme:
              pw.ThemeData(defaultTextStyle: pw.TextStyle(font: ralewayMedium)),
          pageFormat: PdfPageFormat(1300, 940),
          orientation: pw.PageOrientation.landscape,
          margin: const pw.EdgeInsets.all(0),
          footer: (context) => pageFooter,
          // build: (pw.Context context) => [beforeAfter[0]],
          build: (pw.Context context) => pdfPage1,
        )
    );

    // Last Page

    pw.Widget lastPage = pw.Container(
      height: 940,
      width: 1300,
      child: pw.Stack(
        children: [
          pw.Positioned(
            top: 0,
            left: 0,
            child: pw.Container(
              height: 940,
              width: 1300,
              decoration: pw.BoxDecoration(
                image: pw.DecorationImage(image: backgroundImage, fit: pw.BoxFit.cover)
              )
            )
          ),
          pw.Positioned(
            top: 0,
            left: 0,
            child: pw.Container(
              padding: pw.EdgeInsets.symmetric(vertical: 40, horizontal: 45),
              height: 940,
              width: 1300,
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.center,
                mainAxisAlignment: pw.MainAxisAlignment.center,
                mainAxisSize: pw.MainAxisSize.max,
                children: [
                  pw.Image(
                    immaginaLogoImage,
                    height: 97,
                  ),
                  pw.SizedBox(height: 50),

                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Container(
                        decoration: pw.BoxDecoration(
                          borderRadius: pw.BorderRadius.circular(20),
                          color: PdfColors.white
                        ),
                        width: 565,
                        height: 550,
                        child: pw.Center(
                          child: pw.Column(
                            mainAxisAlignment: pw.MainAxisAlignment.spaceEvenly,
                            children: [
                              pw.Image( agencyLogo , height: 70 ),
                              pw.Column(
                                children: [
                                  pw.Text(
                                    'Ti interessa questo immobile?',
                                    style: pw.TextStyle(
                                      fontSize: 30,
                                      color: PdfColors.black,
                                      font: ralewayBold
                                    )
                                  ),
                                  pw.SizedBox(height: 10),
                                  pw.Text(
                                    'Contatta l’agenzia',
                                    style: pw.TextStyle(
                                      fontSize: 28,
                                      color: PdfColors.black,
                                      font: ralewayMedium
                                    )
                                  ),
                                ]
                              ),
                              pw.Row(
                                children: [
                                  pw.Expanded(
                                    child: pw.Column(
                                      crossAxisAlignment: pw.CrossAxisAlignment.center,
                                      children: [
                                        pw.Image( phoneIcon, height: 40 ),
                                        pw.SizedBox(height: 10),
                                        pw.Text(
                                          agencyData.phone!,
                                          style: pw.TextStyle(
                                            fontSize: 17,
                                            color: PdfColors.black,
                                            font: ralewayMedium
                                          )
                                        ),
                                      ]
                                    )
                                  ),
                                  pw.Expanded(
                                    child: pw.Column(
                                      crossAxisAlignment: pw.CrossAxisAlignment.center,
                                      children: [
                                        pw.Image( locationIcon, height: 40 ),
                                        pw.SizedBox(height: 10),
                                        pw.Text(
                                          agencyData.toShortAddress()!,
                                          textAlign: pw.TextAlign.center,
                                          style: pw.TextStyle(
                                            fontSize: 17,
                                            color: PdfColors.black,
                                            font: ralewayMedium,
                                          )
                                        ),
                                      ]
                                    )
                                  ),
                                ]
                              )
                            ]
                          )
                        )
                      ),
                      pw.SizedBox(width: 30),
                      pw.Container(
                        decoration: pw.BoxDecoration(
                          borderRadius: pw.BorderRadius.circular(20),
                          color: PdfColors.white
                        ),
                        width: 565,
                        height: 550,
                        child: pw.Center(
                          child: pw.Column(
                            mainAxisAlignment: pw.MainAxisAlignment.spaceEvenly,
                            children: [
                              pw.Image( risLogoImage , height: 70 ),
                              pw.Column(
                                children: [
                                  pw.Text(
                                    'Ti piace questa ristrutturazione?',
                                    style: pw.TextStyle(
                                      fontSize: 30,
                                      color: PdfColor.fromHex('#489B79'),
                                      font: ralewayBold
                                    )
                                  ),
                                  pw.SizedBox(height: 10),
                                  pw.Text(
                                    'Contatta Newarc',
                                    style: pw.TextStyle(
                                      fontSize: 28,
                                      color: PdfColor.fromHex('#489B79'),
                                      font: ralewayMedium
                                    )
                                  ),
                                ]
                              ),
                              pw.Row(
                                children: [
                                  pw.Expanded(
                                    child: pw.Column(
                                      crossAxisAlignment: pw.CrossAxisAlignment.center,
                                      children: [
                                        pw.Image( phoneIcon, height: 40 ),
                                        pw.SizedBox(height: 10),
                                        pw.Text(
                                          '************',
                                          style: pw.TextStyle(
                                            fontSize: 17,
                                            color: PdfColors.black,
                                            font: ralewayMedium
                                          )
                                        ),
                                      ]
                                    )
                                  ),
                                  pw.Expanded(
                                    child: pw.Column(
                                      crossAxisAlignment: pw.CrossAxisAlignment.center,
                                      children: [
                                        pw.Image( locationIcon, height: 40 ),
                                        pw.SizedBox(height: 10),
                                        pw.Text(
                                          'Corso Ferrucci 36, Torino',
                                          style: pw.TextStyle(
                                            fontSize: 17,
                                            color: PdfColors.black,
                                            font: ralewayMedium
                                          )
                                        ),
                                      ]
                                    )
                                  ),
                                ]
                              )
                            ]
                          )
                        )
                      ),
                    ]
                  ),

                  pw.SizedBox(height: 50),

                  pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.center,
                    children: [
                      pw.Text(
                        'Newarc Srl',
                        style: pw.TextStyle(
                          fontSize: 16,
                          color: PdfColor.fromHex('#ffffff'),
                          font: ralewayMedium
                        )
                      ),
                      pw.SizedBox(height: 15),

                      pw.Text(
                        'Sede Legale: Via Vittorio Emanuele II 29, Chieri • Sede Operativa: Corso Ferrucci 36, Torino • P.Iva 12533550013',
                        style: pw.TextStyle(
                          fontSize: 14,
                          color: PdfColor.fromHex('#ffffff'),
                          font: ralewayMedium
                        )
                      ),
                      pw.SizedBox(height: 3),
                      pw.Text(
                        '<EMAIL> • 011 02 63 850',
                        style: pw.TextStyle(
                          fontSize: 14,
                          color: PdfColor.fromHex('#ffffff'),
                          font: ralewayMedium
                        )
                      ),

                    ]
                  )




                ]

              )
            )
          )

        ]
      )

    );

    pdf.addPage(
        pw.Page(
          theme:
              pw.ThemeData(defaultTextStyle: pw.TextStyle(font: ralewayMedium)),
          pageFormat: PdfPageFormat(1300, 940),
          orientation: pw.PageOrientation.landscape,
          margin: const pw.EdgeInsets.all(0),
          build: (pw.Context context) => lastPage,
        )
    );


  final pdfBytes = await pdf.save();



   final compressor = ILovePDFCompressor();
    Uint8List compressedBytes = await compressor.compressPDF(pdfBytes);

    XFile compressedXFile =   XFile.fromData(compressedBytes, name: "${adData.code}.pdf", length: compressedBytes.length);
    await uploadFileDelayed("/newarcHomes/${adData.firebaseId}/brochure-pdf/","${adData.code}.pdf",compressedXFile);

    progress?.value = 0.98;

  try{
    final FirebaseFirestore _db =
        FirebaseFirestore.instance;
    await _db
        .collection(appConfig.COLLECT_NEWARC_HOME)
        .doc(adData.firebaseId)
        .update({
      "brochurePdfPath":{
        "filename":"${adData.code}.pdf",
        "location":"/newarcHomes/${adData.firebaseId}/brochure-pdf/",
      }
    });
  }catch (e,s) {
    print("while updating COLLECT_NEWARC_HOME ${e.toString()}");
  }
    //   // Create a Blob from PDF bytes
    // final blob = html.Blob([compressedBytes], 'application/pdf');
    // //Create a link element
    // final url = html.Url.createObjectUrlFromBlob(blob);
    // final anchor = html.AnchorElement(href: url)
    //   ..setAttribute('download', adData.code! +'.pdf')
    //   ..click();
    //
    // //Clean up
    // html.Url.revokeObjectUrl(url);
    progress?.value = 1.0;
  } catch (e,s) {
    print("While Generating PDF Error -->  ${e.toString()}");
    print("While Generating PDF S --> ${s.toString()}");
  }





}

Future<XFile> convertBytesToXFile(Uint8List bytes, String filename) async {
  final tempDir = await getTemporaryDirectory();
  final filePath = '${tempDir.path}/$filename';

  final file = File(filePath);
  await file.writeAsBytes(bytes);

  return XFile(file.path, mimeType: 'application/pdf');
}



PdfColor getEnergyColorCode(String code) {
  code = code.toLowerCase();

  switch (code) {
    case 'g':
      return PdfColor.fromHex('#D30202');
    case 'f':
      return PdfColor.fromHex('#E8422E');
    case 'e':
      return PdfColor.fromHex('#F38F24');
    case 'd':
      return PdfColor.fromHex('#FDDF04');
    case 'c':
      return PdfColor.fromHex('#7DCB2D');
    case 'b':
      return PdfColor.fromHex('#25B106');
    case 'a1':
      return PdfColor.fromHex('#037603');
    case 'a2':
      return PdfColor.fromHex('#037603');
    case 'a3':
      return PdfColor.fromHex('#037603');
    case 'a4':
      return PdfColor.fromHex('#037603');
    default:
      return PdfColor.fromHex('#D30202');
  }
}
