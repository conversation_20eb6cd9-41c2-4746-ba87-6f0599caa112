import 'package:flutter/material.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:flutter_svg/flutter_svg.dart';


class AddressSearchBar extends StatefulWidget {
  /// Label for the text field. 
  /// Example: "Indirizzo" (can be customized).
  final String label;

  /// Callback that returns the Google place object that the user selects.
  final ValueChanged<Map<String, dynamic>> onPlaceSelected;

  final String? initialAddress;

  ///Allows user to pass in a controller for the text field.
  final TextEditingController? controller ;

  /// Validator function to validate the address field.
  
  final String? Function(String? rawText)? validator;
  const AddressSearchBar({
    Key? key,
    required this.label,
    required this.onPlaceSelected,
    this.initialAddress,
    this.controller = null, 
    this.validator,
  }) : super(key: key);

  @override
  State<AddressSearchBar> createState() => _AddressSearchBarState();
}

class _AddressSearchBarState extends State<AddressSearchBar> {
  /// Controller for the search text field.
   TextEditingController _controller =  TextEditingController();

  /// List of suggestions retrieved from Google Autocomplete API.
  List<Map<String, dynamic>> _suggestions = [];

  /// Whether or not to show the suggestions dropdown.
  bool _showSuggestions = false;

  /// Whether or not the street number is missing (error).
  bool _invalidStreetNumber = false;

  /// Whether or not the external validator has an error.
  bool _hasExternalValidationError = false;

  /// Error message to show below the TextField when `_invalidStreetNumber` is `true`.
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController(); 
    // If there's an initialStreetName passed in and it's not empty, show it in the text field.
    if (widget.initialAddress != null && widget.initialAddress!.isNotEmpty) {
      _controller.text = widget.initialAddress!;
    }
  }

  Future fetchGooglePlacesAPISuggestions(String inputText) async {
    try {
      HttpsCallable callable = FirebaseFunctions.instance.httpsCallable(
        'fetchGooglePlacesAPISuggestions',
      );
      Map<String, dynamic> params = {'inputText': inputText};
      final HttpsCallableResult result = await callable.call(params);
      return result;
    } on FirebaseFunctionsException catch (e) {
      print('caught firebase functions exception');
      print(e);
      return null;
    } catch (e) {
      print('caught generic exception');
      print(e);
      return null;
    }
  }

  Future fetchGooglePlaceIdDetails(String placeId) async {
    try {
      HttpsCallable callable = FirebaseFunctions.instance.httpsCallable(
        'fetchGooglePlaceIdDetails',
      );
      Map<String, dynamic> params = {'placeId': placeId};
      final HttpsCallableResult result = await callable.call(params);
      return result;
    } on FirebaseFunctionsException catch (e) {
      print('caught firebase functions exception');
      print(e);
      return null;
    } catch (e) {
      print('caught generic exception');
      print(e);
      return null;
    }
  }

  /// This method is called every time the user types in the text field.
  /// It calls your Cloud Function to retrieve autocomplete suggestions from Google.
  Future<void> _getSuggestions(String inputText) async {
    if (inputText.isEmpty) {
      setState(() {
        _suggestions.clear();
      });
      return;
    }
    
    try {
      final response = await fetchGooglePlacesAPISuggestions(inputText);
      if (response != null &&
          response.data != null &&
          response.data['status'] == 'OK') {
        /// Extract up to 3 suggestions.
        final predictions = response.data['predictions'] as List;
        setState(() {
          _suggestions = predictions
              .take(3)
              .map((item) => {
                    'description': item['description'],
                    'placeId': item['place_id'],
                  })
              .toList();
          _showSuggestions = _suggestions.isNotEmpty;
        });
      } else {
        // If the status is not OK, we can clear suggestions
        setState(() {
          _suggestions.clear();
          _showSuggestions = false;
        });
      }
    } catch (e) {
      setState(() {
        _suggestions.clear();
        _showSuggestions = false;
      });
    }
  }

  /// Fetch details for a given placeId to retrieve structured info like 
  /// streetName, streetNumber, city, and postalCode.
  Future<Map<String, dynamic>> _fetchPlaceDetails(String placeId) async {
    final response = await fetchGooglePlaceIdDetails(placeId);
    if (response != null &&
        response.data != null &&
        response.data['status'] == 'OK') {
      final result = response.data['result'];

      // Extract lat and lng
      double? lat;
      double? lng;
      if (result['geometry'] != null && result['geometry']['location'] != null) {
        lat = (result['geometry']['location']['lat'] as num?)?.toDouble();
        lng = (result['geometry']['location']['lng'] as num?)?.toDouble();
      }

      // Extract address components
      final addressComponents = result['address_components'] as List<dynamic>?;

      if (addressComponents == null) {
        return {};
      }

      // Parse out specific fields
      String? streetName;
      String? streetNumber;
      String? locality;
      String? city;
      String? postalCode;
      String? province;
      String? region;
      String? country;

      // Loop through address components
      for (var component in addressComponents) {
        final List<dynamic> types = component['types'];
        if (types.contains('route')) {
          streetName = component['long_name'];
        }
        if (types.contains('street_number')) {
          streetNumber = component['long_name'];
        }
        if (types.contains('locality')) {
          locality = component['long_name'];
        }
        if (types.contains('postal_code')) {
          postalCode = component['long_name'];
        }
        if (types.contains("administrative_area_level_3")) {
          city = component['short_name'];
        }
        if (types.contains("administrative_area_level_2")) {
          province = component['short_name'];
        }
        if (types.contains("administrative_area_level_1")) {
          region = component['long_name'];
        }
        if (types.contains("country")) {
          country = component['long_name'];
        }
      }

      return {
        'streetName': streetName,
        'streetNumber': streetNumber,
        'city': city,
        'locality': locality,
        'postalCode': postalCode,
        'province': province,
        'region': region,
        'country': country,
        'latitude': lat,
        'longitude': lng,
      };
    }
    return {};
  }

  /// Called when the user selects a suggestion from the dropdown.
  void _onSuggestionSelected(Map<String, dynamic> suggestion) async {
     _controller.text = suggestion['description'] ?? '';

    // Reset error state first
    setState(() {
      _invalidStreetNumber = false;
      _hasExternalValidationError = false;
      _errorMessage = '';
    });

    // Fetch details from the placeId
    final placeDetails = await _fetchPlaceDetails(suggestion['placeId']);

    // Check if streetNumber is found
    if (placeDetails['streetNumber'] == null ||
        placeDetails['streetNumber'].toString().isEmpty) {
      // Mark an error
      setState(() {
        _invalidStreetNumber = true;
        _errorMessage = 'Inserisci indirizzo completo di numero civico';
      });
    }

    // Build the final Map to return
    final selectedPlace = {
      'description': suggestion['description'],
      'placeId': suggestion['placeId'],
      'place': placeDetails,
    };
    setState(() {
      _showSuggestions = false;
    });
    // Return the selected place object to parent via callback
    widget.onPlaceSelected(selectedPlace);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      // If user taps outside the suggestion box, hide it
      onTap: () {
        setState(() {
          _showSuggestions = false;
        });
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Label + SearchBar
          _buildSearchBar(),
          // The suggestions list, shown only if _showSuggestions == true
          _showSuggestions ? _buildSuggestionsList() : const SizedBox(),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // The label on top
        NarFormLabelWidget(
          label: widget.label,
          textAlign: TextAlign.left,
          textColor: Color(0xff696969),
          fontSize: 13,
          fontWeight: '600',
        ),
        const SizedBox(height: 4),
        // The box containing the text field + Google logo
        Container(
          padding: const EdgeInsets.only(right: 8.0),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: (_invalidStreetNumber || _hasExternalValidationError)? Colors.red : Color(0xffdbdbdb)),
          ),
          child: Row(
            children: [
              // Expanded text field to take up horizontal space
              Expanded(
                child: TextFormField(
                  controller: _controller,
                  validator: (value) {
                    final validationResult = widget.validator?.call(value);
                    setState(() {
                      _hasExternalValidationError = validationResult != null;
                    });
                    return validationResult;
                  },
                  decoration: InputDecoration(
                    hoverColor: Colors.transparent,
                    hintText: 'Cerca indirizzo...',
                    hintStyle: TextStyle(
                      color: Color.fromARGB(255, 200, 200, 200),
                      fontSize: 14,
                      fontFamily: 'Raleway-600',
                    ),
                    // Show the error message if `_invalidStreetNumber` is true
                    errorText: _invalidStreetNumber ? _errorMessage : null,
                    border: InputBorder.none,
                  
                    enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.transparent),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.transparent),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    errorBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.transparent),
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  // autovalidateMode: AutovalidateMode.onUserInteraction,
                  style: TextStyle(
                    color: Theme.of(context).disabledColor,
                    fontSize: 14,
                    fontFamily: 'Raleway-700',
                  ),
                  onFieldSubmitted: (value) {
                                        _controller.text = value;

                  },
                  onChanged: (value) {
                    setState(() {
                      _invalidStreetNumber = false;
                      _hasExternalValidationError = false;
                      _errorMessage = '';
                    });
                    _getSuggestions(value);
                    Form.of(context).validate();
                  },
                ),
              ),
              // Add horizontal space before the G logo
              const SizedBox(width: 8.0),

              // The Google 'G' logo
              GestureDetector(
                onTap: () {
                  // You could define a specific action here or leave as decorative
                },
                child: SvgPicture.asset(
                  'assets/icons/Google_G_logo.svg',
                  height: 24,
                  width: 24,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSuggestionsList() {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 8.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(color: Color(0xffdbdbdb)),
      ),
      child: Column(
        children: _suggestions.map((suggestion) {
          return InkWell(
            onTap: () => _onSuggestionSelected(suggestion),
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12.0),
              child: Text(
                suggestion['description'] ?? '',
                style: TextStyle(
                  color: Theme.of(context).disabledColor,
                  fontSize: 14,
                  fontFamily: 'Raleway-700',
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}
