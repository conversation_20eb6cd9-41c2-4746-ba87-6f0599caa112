import 'package:flutter/material.dart';

class CustomIconButton extends StatefulWidget {
  CustomIconButton(
      {Key? key,
      required this.label,
      required this.icon,
      required this.color,
      required this.function,
      this.borderRadius = 8,
      this.width = 100,
      this.height = 35,
      this.textStyle=const TextStyle(color: Color(0xff6C6C6C)),
      this.leadingIcon=false,
      this.padding= const EdgeInsets.symmetric(horizontal: 10),
      this.boarderColor,
      this.boarderWidth = 1,
      this.disabled = false,
      })
      : super(key: key);

  final String label;
  final Color color;
  final Color? boarderColor;
  final Widget icon;
  final double width;
  final double height;
  final TextStyle textStyle;
  final Function ? function;
  final bool leadingIcon;
  final double borderRadius;
  final EdgeInsetsGeometry padding;
  final bool disabled;
  final double boarderWidth;

  @override
  _CustomIconButtonState createState() => _CustomIconButtonState();
}

class _CustomIconButtonState extends State<CustomIconButton> {

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      //width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        color:  !widget.disabled ?widget.color : widget.color.withOpacity(0.5),
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: widget.boarderColor != null 
        ? Border.all(
          color: widget.boarderColor!,
          width: widget.boarderWidth
        ) 
        : null,
        
      ),

      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            if(widget.function != null){
              widget.function!();
            }
          },
          enableFeedback: !widget.disabled,
          borderRadius: BorderRadius.circular(widget.borderRadius),
          child: Padding(
            padding:widget.padding,
            child: Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              children: _getTextAndIcon(),
            ),
          ),
        ),
      ),
    );
  }

  List<Widget> _getTextAndIcon() {
    if (!widget.leadingIcon) {
      return [
        Text(
          widget.label,
          style:  widget.textStyle,
        ),
        widget.icon
      ];
    } else {
      return [
        widget.icon,
        Text(
          widget.label,
          style: widget.textStyle,
        ),
      ];
    }
  }
}
