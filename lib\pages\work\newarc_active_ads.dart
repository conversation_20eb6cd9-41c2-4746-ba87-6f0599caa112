import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_webservice/places.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/classes/immaginaProject.dart';
import 'package:newarc_platform/classes/newarcProject.dart';
import 'package:newarc_platform/classes/projectEconomic.dart';
import 'package:newarc_platform/classes/property.dart';
import 'package:newarc_platform/classes/renovationContact.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/utils/common_utils.dart';
import 'package:newarc_platform/utils/downloadAdBrochurePDF.dart';
import 'package:newarc_platform/utils/downloadAdBrochurePDFCopy.dart';
import 'package:newarc_platform/utils/downloadAdImageBrochure.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
import 'package:newarc_platform/widget/UI/block-file-picker.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/widget/UI/filter.dart';
import 'package:newarc_platform/widget/UI/image-select-box.dart';
import 'package:newarc_platform/widget/UI/link.dart';
import 'package:newarc_platform/widget/UI/newarc_data_table.dart';
import 'package:newarc_platform/widget/UI/select-box.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/app_const.dart' as appConst;
import 'package:newarc_platform/widget/UI/tab/common_icon_button.dart';
import 'package:newarc_platform/widget/UI/tab/status_widget.dart';
import 'package:newarc_platform/widget/UI/tab/text_style.dart';
import 'package:newarc_platform/widget/UI/tab/users_stack_list.dart';
import 'package:newarc_platform/widget/custom_drawer.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:provider/provider.dart';
import 'package:cloud_functions/cloud_functions.dart';

class NewarcActiveAdsController extends GetxController {
  RxBool loadingProperties = false.obs;
  RxBool loadingContacts = false.obs;

  List<Property> ads = [];
  List<Map> projectEconomicData = [];
  List<ProjectEconomic> projectEconomic = [];

  List<NewarcProject> newarcProjects = [];
  List<ImmaginaProject> immaginaProjects = [];

  List<RenovationContact> renovationContact = [];
  List<Map> contacts = [];
  String currentlyShowing = '';

  int totalRecords = 0;
  final recordsPerPage = 20;

  int pageCounter = 1;
  int totalPages = 0;
  bool disablePreviousButton = true;
  bool disableNextButton = false;

  List<DocumentSnapshot> documentList = [];

  List<dynamic> cacheFirestore = [];

  List<String> formMessages = [];
  TextEditingController contProjectName = new TextEditingController();
  TextEditingController contProjectType = new TextEditingController();
  TextEditingController contProjectEconomic = new TextEditingController();
  TextEditingController contCity = new TextEditingController();
  TextEditingController contZone = new TextEditingController();

  final TextEditingController pubblicatoFilterController = new TextEditingController();

  String pubblicatoFilter = '';
  String contSearchProjectType = '';
  final TextEditingController contSearchText = new TextEditingController();
  final TextEditingController searchTextController = TextEditingController();

  final TextEditingController contRenovationContact = new TextEditingController();
  final TextEditingController contSearchProjectTypeController = new TextEditingController();

  bool titleEnabled = true;

  String query = "";
  List<Map> filters = [];
  String currentMenu = 'active-ads';
}

class NewarcActiveAds extends StatefulWidget {
  final bool isArchived;
  final bool forceDataFetch;

  // final AgencyUser agencyUser;
  final Function? updateViewCallback;
  final Map? projectArguments;

  static const String route = '/active-ads/index';

  const NewarcActiveAds(
      {super.key,
      required this.isArchived,
      // required this.agencyUser,
      this.updateViewCallback,
      this.forceDataFetch = false,
      this.projectArguments = const {}
      });

  @override
  State<NewarcActiveAds> createState() => _NewarcActiveAdsState();
}

class _NewarcActiveAdsState extends State<NewarcActiveAds> {
  final controller = Get.put<NewarcActiveAdsController>(NewarcActiveAdsController());

  GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
      controller.filters.clear();
      controller.pubblicatoFilter = '';
      controller.contSearchProjectType = '';
      controller.contSearchProjectTypeController.clear();
      controller.pubblicatoFilterController.clear();

    if (controller.currentMenu != widget.projectArguments?['current_menu']) {
      controller.ads = [];

      if( widget.projectArguments != null ) {
        controller.currentMenu = widget.projectArguments?['current_menu'];
      }

    }

    WidgetsBinding.instance.addPostFrameCallback((_) {
      initialFetch( force: widget.forceDataFetch );
    });
  }

  

  Widget dataTablePagination() {
    return Visibility(
      visible: controller.currentlyShowing.isNotEmpty,
      child: Padding(
          padding: EdgeInsets.symmetric(vertical: 10),
          child: IconTheme.merge(
            data: const IconThemeData(opacity: 0.54),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  "${controller.currentlyShowing} di  ${controller.totalRecords.toString()}",
                  style: TextStyle(
                    fontFamily: '',
                    fontSize: 12.0,
                    color: Colors.black.withOpacity(0.54),
                  ),
                ),
                SizedBox(width: 32.0),
                IconButton(
                  icon: const Icon(Icons.chevron_left),
                  padding: EdgeInsets.zero,
                  onPressed: () {
                    if (controller.disablePreviousButton == true) return;
                    if (controller.loadingProperties.value == true) return;
                    fetchPrevProperties();
                  },
                ),
                SizedBox(width: 24.0),
                IconButton(
                  icon: const Icon(Icons.chevron_right),
                  padding: EdgeInsets.zero,
                  onPressed: () {
                    if (controller.disableNextButton == true) return;
                    if (controller.loadingProperties.value == true) return;

                    fetchNextProperties();
                  },
                ),
                SizedBox(width: 14.0),
              ],
            ),
          )),
    );
  }

  _initialFetch({bool force = false}) {
    initialFetch(force: force);
  }

  Future<void> initialFetch({bool force = false}) async {
    
    if (controller.ads.isNotEmpty && !force) return;

    controller.pageCounter = 1;

    setState(() {
      controller.ads = [];
      controller.loadingProperties.value = true;
    });

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshotCounter;
      Query<Map<String, dynamic>> counterQuery;

      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> dataQuery;

      dataQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_NEWARC_HOME).where('isArchived', isEqualTo: widget.isArchived).orderBy('insertTimestamp', descending: true);
      counterQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_NEWARC_HOME).where('isArchived', isEqualTo: widget.isArchived).orderBy('insertTimestamp', descending: true);

      if (controller.filters.length > 0) {
        for (var i = 0; i < controller.filters.length; i++) {
          if (controller.filters[i]['search'] == 'equal') {
            dataQuery = dataQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
            counterQuery = counterQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
          }
        }
      }

      collectionSnapshot = await dataQuery.limit(controller.recordsPerPage).get();

      collectionSnapshotCounter = await counterQuery.get();

      controller.totalRecords = collectionSnapshotCounter.docs.length;
      controller.totalPages = (controller.totalRecords / controller.recordsPerPage).ceil();

      if (controller.totalRecords > controller.recordsPerPage) {
        controller.currentlyShowing = '1-' + controller.recordsPerPage.toString();
      }

      controller.documentList = collectionSnapshot.docs;

      generateDataRows(collectionSnapshot);
      setState(() {});
    } catch (e, s) {
      print({e, s});
      setState(() {
        controller.loadingProperties.value = false;
      });
    }
  }

  fetchNextProperties() async {
    setState(() {
      controller.loadingProperties.value = true;
    });

    controller.pageCounter++;

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> dataQuery;

      int indexOfSnapshot = isRecordExists(controller.pageCounter);

      if (indexOfSnapshot > -1) {
        collectionSnapshot = controller.cacheFirestore[indexOfSnapshot]['snapshot'];
      } else {
        dataQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_NEWARC_HOME).where('isArchived', isEqualTo: widget.isArchived);

        if (controller.filters.length > 0) {
          for (var i = 0; i < controller.filters.length; i++) {
            dataQuery = dataQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
          }
        }
        // if (contSearchProjectType != '') {
        //   dataQuery = FirebaseFirestore.instance
        //       .collection(appConfig.COLLECT_NEWARC_PROJECTS)
        //       .where('type', isEqualTo: contSearchProjectType)
        //       // .orderBy('created', descending: true)
        //       .startAfterDocument(documentList[documentList.length - 1])
        //       .limit(recordsPerPage);
        // } else {
        //   dataQuery = FirebaseFirestore.instance
        //       .collection(appConfig.COLLECT_NEWARC_PROJECTS)
        //       // .orderBy('created', descending: true)
        //       .startAfterDocument(documentList[documentList.length - 1])
        //       .limit(recordsPerPage);
        // }

        collectionSnapshot =
            await dataQuery.orderBy('insertTimestamp', descending: true).limit(controller.recordsPerPage).startAfterDocument(controller.documentList[controller.documentList.length - 1]).get();
      }

      controller.documentList = collectionSnapshot.docs;

      generateDataRows(collectionSnapshot);
    } catch (e,s) {

      print({e,s});
      setState(() {
        controller.loadingProperties.value = false;
      });
    }
  }

  fetchPrevProperties() async {
    setState(() {
      controller.loadingProperties.value = true;
    });

    controller.pageCounter--;

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> dataQuery;

      int indexOfSnapshot = isRecordExists(controller.pageCounter);
      if (indexOfSnapshot > -1) {
        collectionSnapshot = controller.cacheFirestore[indexOfSnapshot]['snapshot'];
      } else {
        dataQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_NEWARC_HOME).where('isArchived', isEqualTo: widget.isArchived);

        if (controller.filters.isNotEmpty) {
          for (var i = 0; i < controller.filters.length; i++) {
            dataQuery = dataQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
          }
        }
        // if (contSearchProjectType != '') {
        //   dataQuery = FirebaseFirestore.instance
        //       .collection(appConfig.COLLECT_NEWARC_PROJECTS)
        //       .where('type', isEqualTo: contSearchProjectType)
        //       // .orderBy('created', descending: true)
        //       .endBeforeDocument(documentList[documentList.length - 1])
        //       .limit(recordsPerPage);
        // } else {
        //   dataQuery = FirebaseFirestore.instance
        //       .collection(appConfig.COLLECT_NEWARC_PROJECTS)
        //       // .orderBy('created', descending: true)
        //       .endBeforeDocument(documentList[documentList.length - 1])
        //       .limit(recordsPerPage);
        // }

        collectionSnapshot = await dataQuery.orderBy('insertTimestamp', descending: true).limit(controller.recordsPerPage).endBeforeDocument(controller.documentList[controller.documentList.length - 1]).get();
      }

      controller.documentList = collectionSnapshot.docs;

      generateDataRows(collectionSnapshot);
    } catch (e) {
      setState(() {
        controller.loadingProperties.value = false;
      });
    }
  }

  int isRecordExists(int pageCounter) {
    String filtersKey = controller.filters.map((f) => '${f['field']}:${f['value']}').join('|');
    return controller.cacheFirestore.indexWhere(
      (record) => record['key'] == pageCounter && record['filtersKey'] == filtersKey,
    );
  }

  generateDataRows(QuerySnapshot<Map<String, dynamic>> collectionSnapshot) async {
    if (isRecordExists(controller.pageCounter) < 0) {
      String filtersKey = controller.filters.map((f) => '${f['field']}:${f['value']}').join('|');
      controller.cacheFirestore.add({
        'key': controller.pageCounter,
        'snapshot': collectionSnapshot,
        'filtersKey': filtersKey,
      });
    }
    if (controller.pageCounter == 1) {
      controller.disablePreviousButton = true;
    } else {
      controller.disablePreviousButton = false;
    }

    if (controller.pageCounter == controller.totalPages) {
      controller.disableNextButton = true;
    } else {
      controller.disableNextButton = false;
    }

    List<Property> _ads = [];
    for (var element in collectionSnapshot.docs) {
      
      Property _tmp = Property.fromDocument(element);

      Query<Map<String, dynamic>> projectQuery;

      _ads.add(_tmp);
    }

    int lastRecordNumber = controller.pageCounter * controller.recordsPerPage;
    if (_ads.length == controller.recordsPerPage) {
      controller.currentlyShowing = (lastRecordNumber - (controller.recordsPerPage - 1)).toString() + '-' + lastRecordNumber.toString();
    } else if (_ads.length > 0 && _ads.length < controller.recordsPerPage) {
      int prevLastRecordNumber = (controller.pageCounter - 1) * controller.recordsPerPage;

      controller.currentlyShowing = (lastRecordNumber - (controller.recordsPerPage - 1)).toString() + '-' + (prevLastRecordNumber + _ads.length).toString();
    }

    setState(() {
      controller.ads = _ads;
      controller.loadingProperties.value = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      key: scaffoldKey,
      drawer: CustomDrawer(),
      body: LayoutBuilder(builder: (BuildContext context, BoxConstraints constraints) {
        return Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                NarFormLabelWidget(
                  label: widget.isArchived ? 'Annunci archiviati' : 'Annunci attivi',
                  fontSize: 19,
                  fontWeight: '700',
                ),
                _create(context),
              ],
            ),

            // Filter goes here
            _filter(),

            SizedBox(height: 10),
            Container(
              height: constraints.maxHeight / 1.2,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: AppColor.white,
                border: Border.all(width: 1.5, color: AppColor.borderColor),
              ),
              child: Column(
                children: [
                  Expanded(
                    child: Stack(
                      children: [
                        Opacity(
                          opacity: controller.loadingProperties.value ? 0.5 : 1,
                          child: NewarcDataTable(
                            rowsPerPage: 20,
                            isHasDecoration: false,
                            hidePaginator: true,
                            onPageChanged: (val) {
                              // print("page : $val");
                            },
                            source: AdsRowSource(
                              ads: controller.ads,
                              context: context,
                              showArchiveButton: widget.isArchived ? false : true,
                              redirectToSinglePage: (Property property, dynamic project) {
                                widget.projectArguments!.clear();
                                widget.updateViewCallback!(
                                  'active-ad-single',
                                  projectArguments: {
                                    'property': property,
                                    'project': project,
                                    'wasArchived': widget.isArchived,
                                    'isInputChangeDetected': [false]
                                  }
                                );
                                
                              },
                              updateAfterArchive: (){
                                initialFetch(force:true);
                              }
                            ),
                            columns: getColumns1(constraints),
                          ),
                        ),
                        if (controller.loadingProperties.value)
                          Positioned.fill(
                            child: Center(
                              child: CircularProgressIndicator(
                                color: Theme.of(context).primaryColor,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                  Align(
                    alignment: Alignment.centerRight,
                    child: dataTablePagination(),
                  ),
                ],
              ),
            )

            // dataTablePagination(),
          ],
        );
      }),
    );
  }

  NarFilter _filter() {
    return NarFilter(
      showSearchInput: true,
      searchHintText: "Cerca per indirizzo...",
      onChanged: (String? searchQuery)async{
        if(searchQuery?.isNotEmpty ?? false){
          if (searchQuery != null && searchQuery.isNotEmpty) {
            List<Property> filtered = controller.ads.where((project) {
              final code = project.code?.toLowerCase() ?? "";
              final address = project.addressInfo;
              final city = address?.city?.toLowerCase() ?? project.city?.toLowerCase() ?? "";
              final streetName = address?.streetName?.toLowerCase() ?? '';
              final fullAddress = address?.fullAddress?.toLowerCase() ?? "";
              return
                code.contains(searchQuery.toLowerCase()) ||
                city.contains(searchQuery.toLowerCase()) ||
                    streetName.contains(searchQuery.toLowerCase()) ||
                    fullAddress.contains(searchQuery.toLowerCase());
            }).toList();

            setState(() {
              controller.ads = filtered;
            });
          }
        }else{
          await initialFetch(force: true);
        }
      },
      suffixIconOnTap: ()async{
        //await initialFetch(force: true);
        if(controller.searchTextController.text.trim().isNotEmpty){
          List<Property> filtered = controller.ads.where((project) {
            final code = project.code?.toLowerCase() ?? "";
            final address = project.addressInfo;
            final city = address?.city?.toLowerCase() ?? project.city?.toLowerCase() ?? "";
            final streetName = address?.streetName?.toLowerCase() ?? '';
            final fullAddress = address?.fullAddress?.toLowerCase() ?? "";
            return
              code.contains(controller.searchTextController.text.toLowerCase()) ||
                  city.contains(controller.searchTextController.text.toLowerCase()) ||
                  streetName.contains(controller.searchTextController.text.toLowerCase()) ||
                  fullAddress.contains(controller.searchTextController.text.toLowerCase());
          }).toList();

          setState(() {
            controller.ads = filtered;
          });
        }else{
          await initialFetch(force: true);
        }
      },
      searchTextEditingControllers: controller.searchTextController,
      selectedFilters: [controller.pubblicatoFilter,controller.contSearchProjectType],
      textEditingControllers: [controller.pubblicatoFilterController,controller.contSearchProjectTypeController],
      filterFields: [
        {
          'Pubblicato': NarSelectBoxWidget(
            options: ["Pubblicato", "Non pubblicato",],
            onChanged: (value) async{
              if(value == "Pubblicato"){
                controller.filters = [
                  {
                    'field': 'isActive',
                    'value': true,
                    'search':'equal'
                  }
                ];
              }
              if(value == "Non pubblicato"){
                controller.filters = [
                  {
                    'field': 'isActive',
                    'value': false,
                    'search':'equal'
                  }
                ];
              }
              controller.pubblicatoFilter = controller.pubblicatoFilterController.text;

              setState(() {});
            },
            controller: controller.pubblicatoFilterController,
          ),
        },
        {
          'Tipologia': NarSelectBoxWidget(
            options: ["Newarc Subito", "Newarc Insieme", "Ristrutturazione", "Immagina"],
            onChanged: (value) async{

              String passValue = value == "Newarc Subito" ? "Newarc" : value;
              controller.filters = [
                {
                  'field': 'projectType',
                  'value': passValue,
                  'search':'equal'
                }
              ];

              controller.contSearchProjectType = controller.contSearchProjectTypeController.text;

              setState(() {});
            },
            controller: controller.contSearchProjectTypeController,
          ),
        },
      ],
      onSubmit: () async {
        await initialFetch(force: true);
      },
      onReset: () async {
        setState(() {
          controller.filters.clear();
          controller.pubblicatoFilter = '';
          controller.contSearchProjectType = '';
          controller.contSearchProjectTypeController.clear();
          controller.pubblicatoFilterController.clear();
        });
        await initialFetch(force: true);
      },
    );
  }

  NewarcProject selectedProject = NewarcProject.empty();
  ImmaginaProject selectedImmaginaProject = ImmaginaProject.empty();
  
  Future<List> fetchProjects() async {

    // controller.contProjectName.text = '';
    try {
      

      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;
      // ["Immagina", "Newarc Subito", "Newarc Insieme"]

      print("here");

      if (controller.contProjectType.text != '') {

        if( controller.contProjectType.text == 'Newarc Subito' || controller.contProjectType.text == 'Newarc Insieme' ) {

          List<NewarcProject> _projects = [];
          
          collectionSnapshotQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_NEWARC_PROJECTS)
          .where('type', isEqualTo: controller.contProjectType.text)
          .where('propertyId', isNull: true)
          .where('type', isNotEqualTo: 'Ristrutturazione');

          collectionSnapshot = await collectionSnapshotQuery.get();

          _projects.add(NewarcProject.empty());

          if (collectionSnapshot.docs.length > 0) {
            for (var element in collectionSnapshot.docs) {
              try {
                NewarcProject _tmp = NewarcProject.fromDocument(element.data(), element.id);
                _projects.add(_tmp);
                
              } catch (e) {}
            }
          }

          controller.newarcProjects.clear();
          controller.newarcProjects = _projects;
          if (controller.newarcProjects.length > 1) {
            return controller.newarcProjects.map((e) {
              String _address = '';
              if( e.addressInfo!.streetName != null ) {
                _address += e.addressInfo!.streetName??'';
              }

              if( e.addressInfo!.streetNumber != null ) {
                _address += ' '+e.addressInfo!.streetNumber!;
              }

              if( e.addressInfo!.city != null ) {
                _address += ', '+e.addressInfo!.city!;
              }
              return {'value': e.id, 'label': _address };
            }).toList();
          } else {
            return [];
          }

        } else if( controller.contProjectType.text == 'Immagina' ) {

          List<ImmaginaProject> _projects = [];

          try {
            collectionSnapshotQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS )
            .where('propertyId', isNull: true)
            .where("isArchived", isEqualTo: false)
            .where("isWorkArchived", isEqualTo: false)
            .where("isAgencyArchived", isEqualTo: false)
            .where("requestStatus", whereIn: [CommonUtils.confermata, CommonUtils.inLavorazione]);

            collectionSnapshot = await collectionSnapshotQuery.get();

            _projects.add(ImmaginaProject.empty());

            if (collectionSnapshot.docs.length > 0) {
              for (var element in collectionSnapshot.docs) {
                try {
                  ImmaginaProject _tmp = ImmaginaProject.fromDocument(element.data(), element.id);
                  _projects.add(_tmp);
                  
                } catch (e,s) {
                  // print({'_tmp', e,s});
                }
              }
            }

            controller.immaginaProjects.clear();
            controller.immaginaProjects = _projects;
            
            if ( controller.immaginaProjects.length > 1) {
              return controller.immaginaProjects.map((e) {

                String _address = '';
                if( e.addressInfo!.streetName != null ) {
                  _address += e.addressInfo!.streetName??'';
                }

                if( e.addressInfo!.streetNumber != null ) {
                  _address += ' '+e.addressInfo!.streetNumber!;
                }

                if( e.addressInfo!.city != null ) {
                  _address += ', '+e.addressInfo!.city!;
                }

                if( _address != '' ) {
                  _address += ' - '+ e.projectId;  
                }
                

                return {'value': e.getid(), 'label': _address };
              }).toList();
            } else {
              return [];
            }
          } catch(e,s) {
            print({e,s});
            return [];
          }

          

          // return [];
        } else {
          return [];
        }
      
        
      } else {
        return [];
      }
    } catch (e, s) {
      print({'fetchProjects',e, s});
      return [];
    }
  }

  Future<int> getLastPropertyNumber( year) async {

    try {
      final querySnapshot = await FirebaseFirestore.instance
        .collection(appConfig.COLLECT_NEWARC_HOME)
        .where('year', isEqualTo: year)
        .orderBy('codeCounter', descending: true)
        .limit(1)
        .get();

      if (querySnapshot.docs.isNotEmpty) {
        return querySnapshot.docs.first.get('codeCounter') as int;
      }

      return 0; // Start from 0 if no quotations exist for the yea  
    } catch (e,s) {
      print({e,s});
      return 0;
    }
    
  }

  MouseRegion _create(BuildContext context) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () async {
          controller.formMessages.clear();

          return await showDialog(
              context: context,
              barrierDismissible: false,
              builder: (BuildContext context) {
                return StatefulBuilder(builder: (BuildContext context, StateSetter _setState) {
                  return Center(
                    child: BaseNewarcPopup(
                      formErrorMessage: controller.formMessages,
                      buttonText: 'Crea Annuncio',
                      onPressed: () async {
                        _setState(() {
                          controller.formMessages.clear();
                          controller.formMessages.add('Creazione in corso..');
                        });

                        Property adData = Property.empty();

                        if( controller.contProjectType.text == 'Immagina' ) {
                          
                          if ( selectedImmaginaProject.getid() == "" ) {
                            controller.formMessages.clear();
                            controller.formMessages.add('Invalid Project.');
                            return false;
                          }

                          String _address = '';
                          if( selectedImmaginaProject.addressInfo!.streetName != null ) {
                            _address += selectedImmaginaProject.addressInfo!.streetName??'';
                          }

                          if( selectedImmaginaProject.addressInfo!.streetNumber != null ) {
                            _address += ' '+selectedImmaginaProject.addressInfo!.streetNumber!;
                          }

                          if( selectedImmaginaProject.addressInfo!.city != null ) {
                            _address += ', '+selectedImmaginaProject.addressInfo!.city!;
                          }
                          adData.propertyName = _address;
                          adData.addressInfo = selectedImmaginaProject.addressInfo;
                          adData.zone = selectedImmaginaProject.marketZone;
                          

                          adData.styles!.add(
                            PropertyConfigStyles(
                              styleName: '',
                              price: selectedImmaginaProject.listingPrice.toString(),
                              styleId: 'styleGroup-0',
                              isDefault: true,
                              picturePaths: [],
                              pictures: [],
                              description: ''
                            )
                          );

                          // adData.geoLocation = Location(lat: selectedImmaginaProject.latitude!, lng: selectedImmaginaProject.longitude!);

                          adData.description = selectedImmaginaProject.description;
                          
                          adData.mq = selectedImmaginaProject.grossSquareFootage.toString();
                          adData.locals = selectedImmaginaProject.rooms.toString();
                          adData.floors = selectedImmaginaProject.unitFloor;
                          adData.baths = selectedImmaginaProject.numberOfBathrooms.toString();

                          adData.actualEnergyClass = selectedImmaginaProject.energyClass;
                          adData.type = selectedImmaginaProject.propertyType;

                          /* current features */
                          Map<String, dynamic> characteristicsMap = Map.fromEntries(appConst.houseFeatures.entries.map((e) => MapEntry(e.key, e.value)));
                          characteristicsMap['Ascensore'] = selectedImmaginaProject.elevator ?? false;
                          characteristicsMap['Cantina'] = selectedImmaginaProject.hasCantina ?? false;
                          characteristicsMap['Terrazzo'] = selectedImmaginaProject.terrace ?? false;
                          characteristicsMap['Portineria'] = selectedImmaginaProject.hasConcierge ?? false;
                          characteristicsMap['Infissi ad alta efficienza'] = selectedImmaginaProject.highEfficiencyFrames ?? false;
                          characteristicsMap['Doppia esposizione'] = selectedImmaginaProject.doubleEsposition ?? false;
                          characteristicsMap['Tripla esposizione'] = selectedImmaginaProject.tripleEsposition ?? false;
                          characteristicsMap['Quadrupla esposizione'] = selectedImmaginaProject.quadrupleEsposition ?? false;
                          characteristicsMap['Risc. centralizzato'] = selectedImmaginaProject.centralizedHeating ?? false;
                          characteristicsMap['Risc. autonomo'] = selectedImmaginaProject.autonomousHeating ?? false;
                          characteristicsMap['Giardino privato'] = selectedImmaginaProject.privateGarden ?? false;
                          characteristicsMap['Giardino condominiale'] = selectedImmaginaProject.sharedGarden ?? false;
                          characteristicsMap['Stabile signorile'] = selectedImmaginaProject.nobleBuilding ?? false;
                          characteristicsMap['Stabile videosorvegliato'] = selectedImmaginaProject.surveiledBuilding ?? false;
                          characteristicsMap['Fibra ottica'] = selectedImmaginaProject.fiber ?? false;
                          characteristicsMap['Pred. condizionatore'] = selectedImmaginaProject.airConditioning ?? false;
                          characteristicsMap['Porta blindata'] = selectedImmaginaProject.securityDoor ?? false;
                          characteristicsMap['Impianto TV'] = selectedImmaginaProject.tvStation ?? false;
                          characteristicsMap['Pred. antifurto'] = selectedImmaginaProject.alarm ?? false;
                          characteristicsMap['Tapparelle motorizzate'] = selectedImmaginaProject.motorizedSunblind ?? false;
                          characteristicsMap['Tapparelle domotizzate'] = selectedImmaginaProject.domotizedSunblind ?? false;
                          characteristicsMap['Luci domotizzate'] = selectedImmaginaProject.domotizedLights ?? false;
                          characteristicsMap['Piano alto'] = selectedImmaginaProject.highFloor ?? false;
                          characteristicsMap['Vicinanza Metro'] = selectedImmaginaProject.metroVicinity ?? false;
                          characteristicsMap['Ampi balconi'] = selectedImmaginaProject.bigBalconies ?? false;
                          characteristicsMap['Grande zona living'] = selectedImmaginaProject.bigLiving ?? false;
                          characteristicsMap['Doppi servizi'] = selectedImmaginaProject.doubleBathroom ?? false;
                          characteristicsMap['Piscina'] = selectedImmaginaProject.swimmingPool ?? false;
                          characteristicsMap['Box o garage'] = selectedImmaginaProject.hasGarage ?? false;
                          characteristicsMap['Cabina armadio'] = selectedImmaginaProject.walkInCloset ?? false;
                          characteristicsMap['Fotovoltaico'] = selectedImmaginaProject.solarPanel ?? false;

                          List<String> currentFeatures = [];
                          characteristicsMap.keys.map((k){
                            if( characteristicsMap[k] == true ) currentFeatures.add(k);
                          }).toList();

                          adData.currentFeatures = currentFeatures;
                          
                        
                        } else {

                          if ( selectedProject.id == "" ) {
                            controller.formMessages.clear();
                            controller.formMessages.add('Invalid Project.');
                            return false;
                          }

                          String _address = '';
                          if( selectedProject.addressInfo!.streetName != null ) {
                            _address += selectedProject.addressInfo!.streetName??'';
                          }

                          if( selectedProject.addressInfo!.streetNumber != null ) {
                            _address += ' '+selectedProject.addressInfo!.streetNumber!;
                          }

                          if( selectedProject.addressInfo!.city != null ) {
                            _address += ', '+selectedProject.addressInfo!.city!;
                          }
                          adData.propertyName = _address;
                          adData.zone = selectedProject.fixedProperty!.zone;
                          
                          adData.addressInfo = selectedProject.addressInfo;

                          adData.mq = selectedProject.fixedProperty!.areaMQ.toString();
                          adData.type = selectedProject.fixedProperty!.propertyType;
                          adData.locals = selectedProject.fixedProperty!.locals.toString();
                          adData.floors = selectedProject.fixedProperty!.floors.toString();
                          adData.baths = selectedProject.fixedProperty!.baths.toString();
                        }

                        adData.isActive = false;
                        adData.isArchived = false;
                        adData.insertTimestamp = Timestamp.now().millisecondsSinceEpoch;
                        adData.updateTimestamp = Timestamp.now().millisecondsSinceEpoch;
                        adData.clicks = 0;
                        adData.projectType = controller.contProjectType.text;

                        String projectCode = '';
                        
                        if( controller.contProjectType.text == 'Immagina' ) {
                          projectCode = 'IM';
                        } else if( controller.contProjectType.text == 'Newarc Subito' ) {
                          projectCode = 'SU';
                        } else if( controller.contProjectType.text == 'Newarc Insieme' ) {
                          projectCode = 'IN';
                        } 


                        /*int cityIndex = appConst.cities.indexWhere((e) => e == adData.city );
                        String cityCode = cityIndex.toString();
                        if( cityIndex < 10 ) {
                          cityCode = "0$cityIndex";
                        }*/

                        String projectRegion = adData.addressInfo!.region ?? appConst.cityToRegionConverter[adData.addressInfo!.city]; 
                        String projectRegionCode = appConst.composeProjectCode['region']![projectRegion]!;
                        
                        DateTime createdDate = DateTime.fromMillisecondsSinceEpoch(adData.insertTimestamp ?? DateTime.now().millisecondsSinceEpoch);
                        String yearCode = createdDate.year.toString().substring(2); // Get last two digits of the year

                        int year = createdDate.year;

                        int lastPropertyNumber = await getLastPropertyNumber(year);
                        String propertyNumber = (lastPropertyNumber + 1).toString().padLeft(6, '0'); // Ensure 4 digits with leading zeros

                        // Assemble the code
                        String _code = "AN_${projectCode}${projectRegionCode}${yearCode}${propertyNumber}";
                        adData.code = _code;
                        adData.year = createdDate.year;
                        adData.codeCounter = lastPropertyNumber + 1;

                        final FirebaseFirestore _db = FirebaseFirestore.instance;
                        DocumentReference<Map<String, dynamic>> adResponse = await _db.collection(appConfig.COLLECT_NEWARC_HOME)
                        .add(adData.toMap());

                        if (adResponse.id != '') {

                          adData.firebaseId = adResponse.id;
                          
                          if( controller.contProjectType.text == 'Immagina' ) {
                            
                            await _db.collection(appConfig.COLLECT_IMMAGINA_PROJECTS)
                            .doc(selectedImmaginaProject.getid())
                            .update({
                              'propertyId':adResponse.id 
                            });

                            setState(() {
                              controller.formMessages.clear();
                              controller.formMessages.add('Ad created!');
                            });
                            // widget.updateViewCallback!(
                            //   'active-ad-single',
                            //   projectArguments: {
                            //     'property': adData,
                            //     'project': selectedProject,
                            //     'isInputChangeDetected': [false]
                            //   }
                            // );

                            widget.updateViewCallback!(
                              'active-ad-single',
                              projectArguments: {
                                'property': adData,
                                'project': selectedImmaginaProject,
                                'isInputChangeDetected': [false]
                              }
                            );

                          } else {
                            
                            await _db.collection(appConfig.COLLECT_NEWARC_PROJECTS)
                            .doc(selectedProject.id)
                            .update({
                              'propertyId':adResponse.id 
                            });

                            setState(() {
                              controller.formMessages.clear();
                              controller.formMessages.add('Ad created!');
                            });
                            widget.updateViewCallback!(
                              'active-ad-single',
                              projectArguments: {
                                'property': adData,
                                'project': selectedProject,
                                'isInputChangeDetected': [false]
                              }
                            );

                          }
                          

                          

                          return true;
                        }

                        return false;
                      },
                      title: "Crea Annuncio",
                      column: Container(
                        width: 345,
                        margin: EdgeInsets.symmetric(horizontal: 100),
                        child: Center(
                          child: ListView(
                            shrinkWrap: true,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Expanded(
                                    child: Column(
                                      mainAxisSize: MainAxisSize.max,
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      mainAxisAlignment: MainAxisAlignment.start,
                                      children: [
                                        NarFormLabelWidget(
                                          label: "Tipologia",
                                          textColor: Color(0xff696969),
                                          fontSize: 14,
                                          fontWeight: '600',
                                        ),
                                        SizedBox(height: 4),
                                        NarSelectBoxWidget(
                                            options: ["Immagina", "Newarc Subito", "Newarc Insieme"],
                                            onChanged: (value) {
                                              controller.contProjectName.text = '';
                                              _setState(() {});
                                            },
                                            controller: controller.contProjectType,
                                            validationType: 'required',
                                            parametersValidate: 'Required!'),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 20),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Expanded(
                                    child: Column(
                                      mainAxisSize: MainAxisSize.max,
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      mainAxisAlignment: MainAxisAlignment.start,
                                      children: [
                                        NarFormLabelWidget(
                                          label: "Seleziona progetto",
                                          textColor: Color(0xff696969),
                                          fontSize: 14,
                                          fontWeight: '600',
                                        ),
                                        SizedBox(height: 4),
                                        FutureBuilder<List>(
                                          future: fetchProjects(),
                                          builder: (context, snapshot) {
                                            if (snapshot.hasData) {
                                              
                                              return Container(
                                                child: NarImageSelectBoxWidget(
                                                  options: snapshot.data!,
                                                  controller: controller.contProjectName,
                                                  validationType: 'required',
                                                  parametersValidate: 'Required!',
                                                  onChanged: (val) {
                                                    if( controller.contProjectType.text == 'Newarc Subito' || controller.contProjectType.text == 'Newarc Insieme' ) {
                                                      selectedProject = controller.newarcProjects.firstWhere((p) => p.id == val['value'], orElse: () => NewarcProject.empty() );
                                                    } else if( controller.contProjectType.text == 'Immagina' ) {
                                                      selectedImmaginaProject = controller.immaginaProjects.firstWhere((p) => p.id == val['value'], orElse: () => ImmaginaProject.empty() );
                                                    }
                                                    

                                                    _setState(() {});
                                                  },
                                                ));
                                            } else if (snapshot.hasError) {
                                              return Container(
                                                width: 30,
                                                height: 30,
                                                decoration: BoxDecoration(
                                                  borderRadius: BorderRadius.circular(100),
                                                  color: const Color.fromARGB(255, 19, 17, 17),
                                                ),
                                              );
                                            }

                                            return CircularProgressIndicator(
                                              color: Theme.of(context).primaryColor,
                                            );
                                          }
                                        )
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              
                              
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                });
              });
        },
        child: Container(
          height: 32,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20.0),
            child: Text(
              "Crea Annuncio",
              style: TextStyle(
                color: Colors.white,
                fontSize: 13,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ),
    );
  }

  List<DataColumn> getColumns1(BoxConstraints constraints) {
    List<DataColumn> list = [];

    List<String> activeColumn = [
      'Pubblicato',
      'Codice',
      'Indirizzo',
      'Tipologia',
      'Agenzia',
      'Creazione',
      'Click',
      'Azioni'
    ];

    List<String> archivedColumn = [
      'Indirizzo',
      'Codice',
      'Agenzia',
      'Tipologia',
      'Creazione',
      'Archiviazione',
      'Azioni'
    ];

    if( widget.isArchived ) {

      

      for (var i = 0; i < archivedColumn.length; i++) {
        if (i==0) {
          list.add(
            DataColumn2(
              size: ColumnSize.L,
              label: Text(
                archivedColumn[i],
              ),
            ),
          );
        } else if (i==2 || i==6) {
          list.add(
            DataColumn2(
              size: ColumnSize.S,
              label: Text(
                archivedColumn[i],
              ),
            ),
          );
        } else {
          list.add(
            DataColumn2(
              size: ColumnSize.M,
              label: Text(
                archivedColumn[i],
              ),
            ),
          );
        }
      }
    } else {
      for (var i = 0; i < activeColumn.length; i++) {
        if (i==1 || i==3 || i==5) {
          list.add(
            DataColumn2(
              size: ColumnSize.M,
              label: Text(
                activeColumn[i],
              ),
            ),
          );
        } else if (i==2) {
          list.add(
            DataColumn2(
              size: ColumnSize.L,
              label: Text(
                activeColumn[i],
              ),
            ),
          );
        } else {
          list.add(
            DataColumn2(
              size: ColumnSize.S,
              label: Text(
                activeColumn[i],
              ),
            ),
          );
        }

      }
    }

    return list;
  }
}

Future<String> getImageUrl(String userId) async {
  final extensions = ['.jpeg', '.png', '.jpg'];
  for (final extension in extensions) {
    final ref = FirebaseStorage.instance.ref().child('users/$userId/profile$extension');
    try {
      return await ref.getDownloadURL();
    } catch (error) {
      continue;
    }
  }
  throw Exception('Profile image not found for user $userId');
}

class AdsRowSource extends DataTableSource {
  AdsRowSource({
    required this.ads, 
    required this.redirectToSinglePage, 
    required this.context, 
    this.updateAfterArchive, 
    this.showArchiveButton = true 
  });

  List<Property> ads = [];
  BuildContext context;
  Function(Property, dynamic) redirectToSinglePage;
  Function? updateAfterArchive;
  bool showArchiveButton;

  @override
  DataRow? getRow(int index) {
    if (index < ads.length) {
      final row = ads[index];
      double completedPercentage = 0;
      var associatedProject;
      // if( row.projectType == 'Immagina' ){

      // }

      if( !showArchiveButton ) {
        print({'widget.isArchived', 'showing arvhicved'});
        return archivedRow(row);
      } else {
        print({'widget.isArchived', 'showing active'});
        return activeRow(row);
      }

      
    }
    return null;
  }

  Future<NewarcProject> getAssociatedNewarcProject(Property row) async {
    
    Query<Map<String, dynamic>> projectQuery;

    projectQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_NEWARC_PROJECTS)
    .where('propertyId', isEqualTo: row.firebaseId)
    .limit(1);

    QuerySnapshot<Map<String, dynamic>> collectionSnapshot = await projectQuery.get();

    if( collectionSnapshot.docs.length > 0 ) {
      
      return row.associatedProject = NewarcProject.fromDocument(collectionSnapshot.docs.first.data(), collectionSnapshot.docs.first.id);
      
    } else {
      return NewarcProject.empty();
    }
  }

  Future<ImmaginaProject> getAssociatedImmaginaProject(Property row ) async {
    Query<Map<String, dynamic>> projectQuery;
    
    projectQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS)
    .where('propertyId', isEqualTo: row.firebaseId)
    .limit(1);

    QuerySnapshot<Map<String, dynamic>> collectionSnapshot = await projectQuery.get();

    if( collectionSnapshot.docs.length > 0 ) {
      
      return ImmaginaProject.fromDocument(collectionSnapshot.docs.first.data(), collectionSnapshot.docs.first.id);
    } else {
      return ImmaginaProject.empty();
    }
  }

  DataRow activeRow( Property row ) {

    String _address = '';

    var _pro = row.associatedProject;
    
    _address = row.addressInfo!.toShortAddress();

    return DataRow(
        cells: [
          DataCell(
            StatusWidget(
              statusColor: row.isActive! ? Color(0xff39C14F) : Color(0xffDD0000) ,
            ),
          ),
          DataCell(
            NarFormLabelWidget(
              label: row.code,
              // label: row.firebaseId,
              fontSize: 12,
              fontWeight: '600',
              textColor: AppColor.black,
            ),

          ),
          DataCell(

            Container(
              decoration: BoxDecoration(

              ),
              clipBehavior: Clip.hardEdge,
              width: 245,
              child: MouseRegion(
                cursor: SystemMouseCursors.click,
                child: GestureDetector(
                  onTap: () async {
                    
                    Query<Map<String, dynamic>> projectQuery;
                    if( row.projectType == 'Immagina' ) {

                      ImmaginaProject _tmp =  await getAssociatedImmaginaProject(row);

                      redirectToSinglePage(row, _tmp);
                    } else {

                      NewarcProject _tmp =  await getAssociatedNewarcProject(row);
                      redirectToSinglePage(row, _tmp);
                      
                    }
                    
                    

                    
                  },
                  child: NarFormLabelWidget(
                    textDecoration: TextDecoration.underline,
                    // label: row.propertyName ?? "",
                    label: _address,
                    textColor: Colors.black,
                    fontWeight: '700',
                    fontSize: 12,
                    overflow: TextOverflow.ellipsis,
                  )
                ),
              )
              
              
            ),
            
          ),
          DataCell(
            NarFormLabelWidget(
              label: row.associatedProject.runtimeType == NewarcProject ? row.associatedProject.type : row.projectType,
              fontSize: 12,
              fontWeight: '600',
              textColor: AppColor.black,
            ),
          ),
          DataCell(
            IconButtonWidget(
              onTap: () async {

                var associatedProject;

                if( row.projectType == 'Immagina' ) {
                  row.associatedProject = await getAssociatedImmaginaProject(row);
                } else {
                  row.associatedProject = await getAssociatedNewarcProject(row);
                }
                
                showDialog(
                  context: context,
                  barrierDismissible: false,
                  builder: (BuildContext context) {

                    
                    
                    String agencyName = '';
                    return Center(
                      child: BaseNewarcPopup(
                        formErrorMessage: [],
                        buttonText: 'Inizia',
                        onPressed: () {
                          // print('Inizia Pressed!');
                          // localizzazioneDialog();
                        },
                        title: '',
                        noButton: true,
                        column: Container(
                            width: 575,
                            height: 315,
                            padding: EdgeInsets.symmetric(horizontal: 100, vertical: 0),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                FutureBuilder<DocumentSnapshot>(
                                  future: 
                                  row.projectType == 'Immagina'
                                  ? FirebaseFirestore.instance.collection(appConfig.COLLECT_AGENCIES).doc(row.associatedProject!.agencyId).get()
                                  : FirebaseFirestore.instance.collection(appConfig.COLLECT_AGENCIES).doc(row.associatedProject!.assignedAgency!['agencyId']).get(),
                                  builder: (context, snapshot) {
                                    if (snapshot.connectionState == ConnectionState.waiting) {
                                      return NarFormLabelWidget(
                                        label: 'Loading...',
                                        overflow: TextOverflow.ellipsis,
                                        fontSize: 12,
                                        fontWeight: '600',
                                        textColor: AppColor.black,
                                      );
                                    }
                                    if (snapshot.hasError || !snapshot.hasData || !snapshot.data!.exists) {
                                      return NarFormLabelWidget(
                                        label: '',
                                        overflow: TextOverflow.ellipsis,
                                        fontSize: 12,
                                        fontWeight: '600',
                                        textColor: AppColor.black,
                                      );
                                    }

                                    AgencyUser agencyName = AgencyUser.fromDocument(snapshot.data!.data() as Map<String, dynamic>, snapshot.data!.id);
                                    
                                    TextEditingController phone = new TextEditingController();
                                    TextEditingController email = new TextEditingController();

                                    phone.text = agencyName.phone!;
                                    email.text = agencyName.email!;
                                    return Column(
                                      children: [
                                        NarFormLabelWidget(
                                          label: agencyName.name,
                                          overflow: TextOverflow.ellipsis,
                                          fontSize: 20,
                                          fontWeight: 'bold',
                                          textColor: AppColor.black,
                                        ),

                                        SizedBox(height: 45,),

                                        Row(
                                          children: [
                                            CustomTextFormField(
                                              label: 'Telefono',
                                              readOnly: true,
                                              controller: phone,
                                              // enabled: false,
                                              fillColor: Color(0xffF2F2F2),
                                            ),
                                          ],
                                        ),
                                        SizedBox(height: 15,),
                                        Row(
                                          children: [
                                            CustomTextFormField(
                                              label: 'Email',
                                              readOnly: true,
                                              controller: email,
                                              fillColor: Color(0xffF2F2F2),
                                            ),
                                          ],
                                        )
                                      ],
                                    );
                                  },
                                )
                              ],
                            )),
                      ),
                    );
                  });
                
              },
              isSvgIcon: true,
              icon: 'assets/icons/agency-action.svg',
              iconColor: AppColor.greyColor,
              
            ),

          ),
          DataCell(
            NarFormLabelWidget(
              label: timestampToUtcDate(row.insertTimestamp??0),
              fontSize: 12,
              fontWeight: '600',
              textColor: AppColor.black,
            ),

          ),
          DataCell(
            NarFormLabelWidget(
              label: row.clicks.toString(),
              fontSize: 12,
              fontWeight: '600',
              textColor: AppColor.black,
            ),

          ),
          DataCell(
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // IconButtonWidget(
                //   onTap: () {
                //     imageAdsBrochure(row, row.associatedProject!);
                //     // pdfAdsBrochureCopy(row, row.associatedProject!);
                //   },
                //   isSvgIcon: true,
                //   icon: 'assets/icons/download_brochure.svg',
                //   iconColor: AppColor.greyColor,
                // ),
                // SizedBox(width: 4),
                IconButtonWidget(
                  onTap: () async {
                    if( row.projectType == 'Immagina' ) {
                      row.associatedProject = await getAssociatedImmaginaProject(row);
                    } else {
                      row.associatedProject = await getAssociatedNewarcProject(row);
                    }
                    redirectToSinglePage(row, row.associatedProject!);
                  },
                  isSvgIcon: true,
                  icon: 'assets/icons/edit.svg',
                  iconColor: AppColor.greyColor,
                ),
                SizedBox(width: 4),
                IconButtonWidget(
                  isOnlyBorder: true,
                  // backgroundColor: Colors.transparent,
                  // borderColor: AppColor.redColor,
                  iconColor: AppColor.greyColor,
                  onTap: () {
                    List<String> formMessages = [''];

                    showDialog(
                      context: context,
                      builder: (BuildContext _bc1) {
                        return StatefulBuilder(builder: (BuildContext _bc2, StateSetter  setState) {
                          return Center(
                            child: BaseNewarcPopup(
                              formErrorMessage: formMessages,
                              buttonText: 'Ok',
                              onPressed: () async {
                              int archivedTime = Timestamp.now().millisecondsSinceEpoch; 
                                await FirebaseFirestore.instance
                                    .collection(appConfig
                                        .COLLECT_NEWARC_HOME)
                                    .doc(row.firebaseId)
                                    .update({'isArchived': true, 'archivedTimestamp': archivedTime });

                                setState(() {
                                  row.isArchived = true;
                                  row.archivedTimestamp = archivedTime;
                                });

                                notifyListeners();

                                updateAfterArchive!();

                                // initialFetchContacts();

                                return true;
                              },
                              title: "Attenzione!",
                              column: Container(
                                width: 600,
                                margin:
                                    EdgeInsets.symmetric(vertical: 30),
                                child: Center(
                                  child: ListView(
                                    shrinkWrap: true,
                                    children: [
                                      NarFormLabelWidget(
                                        label:
                                            "Vuoi davvero archiviare questo annuncio?",
                                        fontSize: 18,
                                        textAlign: TextAlign.center,
                                        fontWeight: '600',
                                      )
                                    ],
                                  ),
                                ),
                              ),
                            ));
                        });
                    });
                  },
                  isSvgIcon: false,
                  icon: 'assets/icons/archive.png',
                )
              
              ],
            ),

          ),
        ],
      );
  }

  DataRow archivedRow( Property row ) {

    String _address = '';

    var _pro = row.associatedProject;
    _address = row.addressInfo!.toShortAddress();

    return DataRow(
        cells: [
          DataCell(

            Container(
              decoration: BoxDecoration(

              ),
              clipBehavior: Clip.hardEdge,
              width: 245,
              child: MouseRegion(
                cursor: SystemMouseCursors.click,
                child: GestureDetector(
                  onTap: (){
                    redirectToSinglePage(row, row.associatedProject!);
                  },
                  child: NarFormLabelWidget(
                    textDecoration: TextDecoration.underline,
                    // label: row.propertyName ?? "",
                    label: _address,
                    textColor: Colors.black,
                    fontWeight: '700',
                    fontSize: 12,
                    overflow: TextOverflow.ellipsis,
                  )
                ),
              )
              
              
            ),
            
          ),
          DataCell(
            NarFormLabelWidget(
              label: row.code,
              // label: row.firebaseId,
              fontSize: 12,
              fontWeight: '600',
              textColor: AppColor.black,
            ),

          ),
          DataCell(
            IconButtonWidget(
              onTap: () {
                
                showDialog(
                  context: context,
                  barrierDismissible: false,
                  builder: (BuildContext context) {
                    String agencyName = '';
                    return Center(
                      child: BaseNewarcPopup(
                        formErrorMessage: [],
                        buttonText: 'Inizia',
                        onPressed: () {
                          // print('Inizia Pressed!');
                          // localizzazioneDialog();
                        },
                        title: '',
                        noButton: true,
                        column: Container(
                            width: 575,
                            height: 315,
                            padding: EdgeInsets.symmetric(horizontal: 100, vertical: 0),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                FutureBuilder<DocumentSnapshot>(
                                  future: 
                                  row.projectType == 'Immagina'
                                  ? FirebaseFirestore.instance.collection(appConfig.COLLECT_AGENCIES).doc(row.associatedProject!.agencyId).get()
                                  : FirebaseFirestore.instance.collection(appConfig.COLLECT_AGENCIES).doc(row.associatedProject!.assignedAgency!['agencyId']).get(),
                                  builder: (context, snapshot) {
                                    if (snapshot.connectionState == ConnectionState.waiting) {
                                      return NarFormLabelWidget(
                                        label: 'Loading...',
                                        overflow: TextOverflow.ellipsis,
                                        fontSize: 12,
                                        fontWeight: '600',
                                        textColor: AppColor.black,
                                      );
                                    }
                                    if (snapshot.hasError || !snapshot.hasData || !snapshot.data!.exists) {
                                      return NarFormLabelWidget(
                                        label: '',
                                        overflow: TextOverflow.ellipsis,
                                        fontSize: 12,
                                        fontWeight: '600',
                                        textColor: AppColor.black,
                                      );
                                    }

                                    AgencyUser agencyName = AgencyUser.fromDocument(snapshot.data!.data() as Map<String, dynamic>, snapshot.data!.id);
                                    
                                    TextEditingController phone = new TextEditingController();
                                    TextEditingController email = new TextEditingController();

                                    phone.text = agencyName.phone!;
                                    phone.text = agencyName.email!;
                                    return Column(
                                      children: [
                                        NarFormLabelWidget(
                                          label: agencyName.name,
                                          overflow: TextOverflow.ellipsis,
                                          fontSize: 20,
                                          fontWeight: 'bold',
                                          textColor: AppColor.black,
                                        ),

                                        SizedBox(height: 45,),

                                        Row(
                                          children: [
                                            CustomTextFormField(
                                              label: 'Telefono',
                                              readOnly: true,
                                              controller: phone,
                                              // enabled: false,
                                              fillColor: Color(0xffF2F2F2),
                                            ),
                                          ],
                                        ),
                                        SizedBox(height: 15,),
                                        Row(
                                          children: [
                                            CustomTextFormField(
                                              label: 'Email',
                                              readOnly: true,
                                              controller: phone,
                                              fillColor: Color(0xffF2F2F2),
                                            ),
                                          ],
                                        )
                                      ],
                                    );
                                  },
                                )
                              ],
                            )),
                      ),
                    );
                  });
                
              },
              isSvgIcon: true,
              icon: 'assets/icons/agency-action.svg',
              iconColor: AppColor.greyColor,
              
            ),

          ),
          
          DataCell(
            NarFormLabelWidget(
              label: row.associatedProject.runtimeType == NewarcProject ? row.associatedProject.type : row.projectType,
              fontSize: 12,
              fontWeight: '600',
              textColor: AppColor.black,
            ),
          ),
          DataCell(
            NarFormLabelWidget(
              label: timestampToUtcDate(row.insertTimestamp??0),
              fontSize: 12,
              fontWeight: '600',
              textColor: AppColor.black,
            ),

          ),
          DataCell(
            NarFormLabelWidget(
              label: timestampToUtcDate(row.archivedTimestamp??0),
              fontSize: 12,
              fontWeight: '600',
              textColor: AppColor.black,
            ),

          ),
          
          DataCell(
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButtonWidget(
                  isOnlyBorder: true,
                  // backgroundColor: Colors.transparent,
                  // borderColor: AppColor.redColor,
                  iconColor: AppColor.greyColor,
                  onTap: () {
                    List<String> formMessages = [''];

                    showDialog(
                      context: context,
                      builder: (BuildContext _bc1) {
                        return StatefulBuilder(builder: (BuildContext _bc2, StateSetter  setState) {
                          return Center(
                            child: BaseNewarcPopup(
                              formErrorMessage: formMessages,
                              buttonText: 'Ok',
                              onPressed: () async {
                                await FirebaseFirestore.instance
                                    .collection(appConfig
                                        .COLLECT_NEWARC_HOME)
                                    .doc(row.firebaseId)
                                    .update({ 'isArchived': false });

                                setState(() {
                                  row.isArchived = false;
                                });

                                notifyListeners();

                                updateAfterArchive!();

                                // initialFetchContacts();

                                return true;
                              },
                              title: "Attenzione!",
                              column: Container(
                                width: 600,
                                margin:
                                    EdgeInsets.symmetric(vertical: 30),
                                child: Center(
                                  child: ListView(
                                    shrinkWrap: true,
                                    children: [
                                      NarFormLabelWidget(
                                        label:
                                            "Vuoi davvero ripristinare questo annuncio?",
                                        fontSize: 18,
                                        textAlign: TextAlign.center,
                                        fontWeight: '600',
                                      )
                                    ],
                                  ),
                                ),
                              ),
                            ));
                        });
                    });
                  },
                  isSvgIcon: false,
                  icon: 'assets/icons/restore.png',
                )
              
              ],
            ),

          ),
        ],
      );
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => ads.length;

  @override
  int get selectedRowCount => 0;
}
